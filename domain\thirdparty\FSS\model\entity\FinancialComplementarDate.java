package com.exhibition.domain.thirdparty.FSS.model.entity;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.thirdparty.FSS.model.vo.FssSimpleVO;
import com.exhibition.domain.thirdparty.FSS.model.vo.FssVo;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/5/11 10:23
 * @describe
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "financial_complementar")
public class FinancialComplementarDate extends BaseEntity<FSS, FssVo> implements Serializable {

    private static final long serialVersionUID = 6425572621567053584L;
    //納稅人所屬年度之組別
    private String m1Group;
    //申報資料之所屬年度
    private Integer m1Exer;

    private String isThreeZero;
    //所得補充稅備註
    private String comRemark;
}
