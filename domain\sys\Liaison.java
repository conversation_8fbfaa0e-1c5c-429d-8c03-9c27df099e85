package com.exhibition.domain.sys;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.apply.Encourage;
import com.exhibition.vo.sys.LiaisonVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * @ProjectName: exhibition-backend @Package: com.exhibition.domain.sys @ClassName: Liaison
 * @description: 机构联络人
 * @author: ShiXin
 * @create: 2020-02-21 16:50
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "liaison")
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE, region = "liaison",
        include = "non-lazy")
public class Liaison extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 3773358591971385710L;

    /**
     * 联络人头像
     */
    @JsonView(Encourage.EncourageSimpleView.class)
    private String      avatar;
    /**
     * 联络人名称(中文)
     */
    @JsonView(Encourage.EncourageSimpleView.class)
    private String      nameZh;
    /**
     * 联络人名称(英文/葡文)
     */
    @JsonView(Encourage.EncourageSimpleView.class)
    private String      nameEnOrPt;
    /**
     * 性别
     */
    @JsonView(Encourage.EncourageSimpleView.class)
    @Enumerated(value = EnumType.STRING)
    private Gender      gender;
    /**
     * 所属机构
     */
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private Institution institution;
    /** 职称 */
    // @ManyToOne(optional = true, fetch = FetchType.LAZY)
    // private CommonConfig title;
    /**
     * 职称中文
     */
    @JsonView(Encourage.EncourageSimpleView.class)
    private String      titleNameZh;
    /**
     * 职称英文
     */
    @JsonView(Encourage.EncourageSimpleView.class)
    private String      titleNameEnOrPt;
    /**
     * 电话區號
     */
    @JsonView(Encourage.EncourageSimpleView.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private AreaCode    telAreaCode;
    /**
     * 电话
     */
    @JsonView(Encourage.EncourageSimpleView.class)
    private String      tel;
    /**
     * 國內流动电话區號
     */
    @JsonView(Encourage.EncourageSimpleView.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private AreaCode    inAreaCode;
    /**
     * 國內流动电话
     */
    @JsonView(Encourage.EncourageSimpleView.class)
    private String      phone;
    /**
     * 國外流动电话區號
     */
    @JsonView(Encourage.EncourageSimpleView.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private AreaCode    abroadAreaCode;
    /**
     * 國外流动电话
     */
    @JsonView(Encourage.EncourageSimpleView.class)
    private String      abroadPhone;
    /**
     * 邮箱
     */
    @JsonView(Encourage.EncourageSimpleView.class)
    private String      email;
    /**
     * 其他邮箱
     */
    @Column(columnDefinition = "text")
    private String   otherEmail;

    /**
     * 傳真
     */
    @JsonView(Encourage.EncourageSimpleView.class)
    private String      fax;
    /**
     * 傳真區號
     */
    private String                       faxAreaCode;
    /**
     * 地址
     */
    @JsonView(Encourage.EncourageSimpleView.class)
    private String      address;
    /**
     * 在职/离职
     */
    @Column(columnDefinition = "tinyint default 0")
    @JsonView(Encourage.EncourageSimpleView.class)
    private boolean     incumbency;
    /**
     * 逻辑删除状态,0:无效,1:有效
     */
    @JsonView(Encourage.EncourageSimpleView.class)
    @Column(columnDefinition = "tinyint default 1")
    private Boolean     status;

    /**
     * 是否為主要聯絡人
     */
    @Column(columnDefinition = "tinyint default 0")
    @JsonView(Encourage.EncourageSimpleView.class)
    private boolean     primaryliaison;

    @Column(columnDefinition = "tinyint default 0")
    @JsonView(Encourage.EncourageSimpleView.class)
    private boolean     recipients1;

    @Column(columnDefinition = "tinyint default 0")
    @JsonView(Encourage.EncourageSimpleView.class)
    private boolean     recipients2;

    /**
     * 擴展字段1（BM）
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField1;
    /**
     * 擴展字段2（BM）
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField2;
    /**
     * 擴展字段3（BM）
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField3;
    /**
     * 擴展字段4（BM）
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField4;
    /**
     * 擴展字段5（BM）
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField5;
    /**
     * 擴展字段6（BM）
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField6;
    /**
     * 擴展字段7（BM）
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField7;
    /**
     * 擴展字段8（BM）
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField8;
    /**
     * 擴展字段9（BM）
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField9;
    /**
     * 擴展字段10（BM）
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField10;

    /**
     * 稱呼
     */
    @JsonView(Encourage.EncourageSimpleView.class)
    @Enumerated(value = EnumType.STRING)
    private Call calls;

    /**
     * 备注字段
     */
    @Column(columnDefinition = "VARCHAR(500) default ''")
    private String remark;

    public Liaison(LiaisonVO vo) {
        if (null != vo) {
            BeanUtils.copyProperties(vo, this);
            if (null != vo.getInstitutionId()) {
                institution = new Institution();
                institution.setId(vo.getInstitutionId());
            }
            Long telAreaCodeId = vo.getTelAreaCodeId();
            if (null != telAreaCodeId) {
                AreaCode code = new AreaCode();
                code.setId(telAreaCodeId);
                this.setTelAreaCode(code);
            }
            Long inAreaCodeId = vo.getInAreaCodeId();
            if (null != inAreaCodeId) {
                AreaCode code = new AreaCode();
                code.setId(inAreaCodeId);
                this.setInAreaCode(code);
            }
            Long abroadAreaCodeId = vo.getAbroadAreaCodeId();
            if (null != abroadAreaCodeId) {
                AreaCode code = new AreaCode();
                code.setId(abroadAreaCodeId);
                this.setAbroadAreaCode(code);
            }
        }
    }

    public LiaisonVO toVO() {
        return toVO(false);
    }

    public LiaisonVO toVO(boolean includeLazy) {
        LiaisonVO liasionVO = new LiaisonVO();
        BeanUtils.copyProperties(this, liasionVO,"telAreaCode", "inAreaCode", "abroadAreaCode", "institution");
        if (null != telAreaCode) {
            liasionVO.setTelAreaCodeId(telAreaCode.getId());
            liasionVO.setTelAreaCode(telAreaCode.toVO());
        }
        if (null != inAreaCode) {
            liasionVO.setInAreaCodeId(inAreaCode.getId());
            liasionVO.setInAreaCode(inAreaCode.toVO());
        }
        if (null != abroadAreaCode) {
            liasionVO.setAbroadAreaCodeId(abroadAreaCode.getId());
            liasionVO.setAbroadAreaCode(abroadAreaCode.toVO());
        }
        if (includeLazy) {
            if (null != institution) {
                liasionVO.setInstitutionId(institution.getId());
                liasionVO.setInstitutionNameZh(institution.getNameZh());
                liasionVO.setInstitutionNameEn(institution.getNameEn());
                liasionVO.setInstitutionNamePt(institution.getNamePt());
            }
        }
        if (fax != null) {
            liasionVO.setFax(fax);
        }
        if (faxAreaCode != null) {
            liasionVO.setFaxAreaCode(faxAreaCode);
        }
        return liasionVO;
    }
}
