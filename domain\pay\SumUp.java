package com.exhibition.domain.pay;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.apply.Encourage;
import com.exhibition.domain.sys.User;
import com.exhibition.vo.pay.SumUpVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@Entity
@Table(name = "boc_sum_up")
@JsonView(Encourage.EncourageSimpleView.class)
public class SumUp extends BaseEntity {
    private Date date;                // 日期
    private Integer transactionCount;     // 收款数量
    private Integer refundCount;     // 退款数量
    private BigDecimal totalIncome;       // 收款总额
    private BigDecimal totalRefund; // 退款总额
    private BigDecimal discrepancy; // 差异额
    private Integer discrepancyCount; // 差异笔数
    @ManyToOne
    @Fetch(FetchMode.SELECT)
    private User user;      // 日结人员
    private String closingTime;         // 日(月)结时间
    private String closingStatus;     // 日结状态
    private String paymentMethod;     // 支付方式
    private Date operatorDate;        // 人员操作日期
    //dayEnd  || monthEnd
    private String mode;

    public SumUp(SumUpVO vo) {
        BeanUtils.copyProperties(vo, this);
    }

    public SumUpVO toVO() {
        SumUpVO vo = new SumUpVO();
        BeanUtils.copyProperties(this, vo);
        if (null != this.user) {
            if(null!=user.getLastName()){
                vo.setUserName(user.getName()!=null?user.getName():""+user.getLastName());
            }else{
                vo.setUserName(user.getName()!=null?user.getName():"");
            }
        }
        return vo;
    }
}
