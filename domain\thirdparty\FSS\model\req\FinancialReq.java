package com.exhibition.domain.thirdparty.FSS.model.req;

import cn.hutool.core.date.DateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/5/6 13:38
 * @describe 經濟及科技發展局接口
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class FinancialReq {
    private String traceId;
    private String requestDateTime;
    private RequestData requestData;
    private Request requester;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString
    public static class RequestData{
        private Integer ContributeNumber;
        private Integer CadastroNumber;
        private String CadastroNumberAppendix;
    }
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString
    public static class Request{
        private String department;
        private String project;
        private String username;
    }
}
