package com.exhibition.domain.apply;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Embeddable;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Embeddable
public class PotentialCustomer implements Serializable {
    public static enum Status {
        /**
         * 洽談中
         */
        DISCUSSION,
        /**
         * 進入意向　
         */
        INTENTION,
        /**
         * 擬簽定合作
         */
        READY_SIGN
    }

    public static enum Region {
        /**
         * 中國内地
         */
        CHINA,
        /**
         * 香港
         */
        HONGKONG,
        /**
         * 葡語係
         */
        PORTUGUESE,
        /**
         * 其他
         */
        OTHER,
    }
    
    private static final long serialVersionUID = 1L;

    /**
     * 合作機構名稱
     */
    private String            name;
    /**
     * 合作方式（DISCUSSION/INTENTION/READY_SIGN，洽談中/進入意向/擬簽定合作）
     */
    private String            cooperationWay;
    /**
     * 進展情況
     */
    private Status            status;
    /**
     * 地區
     */
    private Region            region;
    /**
     * 金額區間
     */
    private String            amount;
}