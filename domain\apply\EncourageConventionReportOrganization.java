package com.exhibition.domain.apply;

import com.exhibition.vo.apply.EncourageConventionActivityExpenditureVO;
import com.exhibition.vo.apply.EncourageConventionReportOrganizationVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;

/**
 * 活動組織架構
 */
@Data
@NoArgsConstructor
@Embeddable
public class EncourageConventionReportOrganization implements Serializable {
    /**
     * 於組織架構內的身份
     */
    @Column(length = 20)
    private String identity;
    /**
     * 機構名稱
     */
    @Column(length = 50)
    private String orgName;

    public EncourageConventionReportOrganization(EncourageConventionReportOrganizationVO vo) {
        BeanUtils.copyProperties(vo, this);
    }

    public EncourageConventionReportOrganizationVO toVO(boolean includeLazy) {
        EncourageConventionReportOrganizationVO vo = new EncourageConventionReportOrganizationVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }

    public EncourageConventionReportOrganizationVO toVO() {
        return toVO(false);
    }

}
