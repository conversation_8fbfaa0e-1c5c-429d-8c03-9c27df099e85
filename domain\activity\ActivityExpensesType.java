package com.exhibition.domain.activity;

import java.util.Arrays;

public enum ActivityExpensesType {
    /**
     * 支持單位
     */
    A("隨團去程"),
    /**
     * 特邀支持單位
     */
    B("隨團回程"),
    /**
     * 官方支持單位
     */
    C("住宿費用");

    private final String name;

    ActivityExpensesType(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ActivityExpensesType getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }
        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
