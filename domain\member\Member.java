package com.exhibition.domain.member;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.apply.Encourage;
import com.exhibition.vo.member.MemberVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Where;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * 规则表
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "member")
@BatchSize(size = 20)
@JsonView(Encourage.EncourageSimpleView.class)
@Where(clause = "is_delete != 1")
public class Member extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    //规则名称
    private String name;
    //积分
    private Integer integral;
    //是否生效
    private Boolean whetherToTakeEffect;
    //生效日期
    private Date effectiveDate;
    //备注
    private String remark;
    //code
    private String code;
    //删除
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isDelete=false;

    public Member(MemberVO vo) {
        BeanUtils.copyProperties(vo, this);
    }

    public MemberVO toVO() {
        MemberVO vo = new MemberVO();
        BeanUtils.copyProperties(this, vo);
        return vo;
    }
}
