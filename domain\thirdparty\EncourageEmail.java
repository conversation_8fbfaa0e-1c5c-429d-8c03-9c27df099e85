package com.exhibition.domain.thirdparty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/3/15 20:03
 * @describe
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Data
public class EncourageEmail {
    private String email;
    private String activityName;
    private String personId;
    private String username;
    private String personnel;
    private String startTime;
    private String endTime;
    private String institution;
}
