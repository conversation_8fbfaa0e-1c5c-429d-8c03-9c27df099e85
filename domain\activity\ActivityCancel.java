package com.exhibition.domain.activity;

import com.exhibition.vo.activity.ActivityCancelVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 展会取消凭证
 * @date 2020-03-02
 * @since 1.0.0
 */

@Data
@NoArgsConstructor
@Embeddable
public class ActivityCancel implements Serializable {
    /**
     * 憑證名
     */
    private String              name;
    /**
     *憑證url
     */
    private String              url;

    public ActivityCancel(ActivityCancelVO vo) {
        BeanUtils.copyProperties(vo, this);

    }

    public ActivityCancelVO toVO() {
        return toVO(false);
    }

    public ActivityCancelVO toVO(boolean includeLazy) {
        ActivityCancelVO vo = new ActivityCancelVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }
}
