package com.exhibition.domain.apply;

import com.exhibition.vo.apply.ConventionVenueRentalVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: ConventionVenueRental
 * @description: 展览场地租金
 * @author: ShiXin
 * @create: 2020-03-13 16:42
 **/
@Data
@NoArgsConstructor
@Embeddable
public class ConventionVenueRental implements Serializable {
    private static final long                      serialVersionUID = -549653606840693571L;
    /**
     * 搭建、展覽、拆展
     */
    private              ConventionVenueRentalType type;
    /**
     * 開始
     */
    @Temporal(TemporalType.TIMESTAMP)
    private              Date                      venueFrom;
    /**
     * 結束
     */
    @Temporal(TemporalType.TIMESTAMP)
    private              Date                      venueTo;
    /**
     * 落實展覽場地面積
     */
    private              Double                    groupArea;
    /**
     * 場地租金單價(每日每平方米澳門元)
     */
    private              Double                    price;
    /**
     * 場地租金總價(澳門元)
     */
    private              Double                    totalPrice;

    public ConventionVenueRental(ConventionVenueRentalVO vo) {
        BeanUtils.copyProperties(vo,this);
    }

    public ConventionVenueRentalVO toVO() {
        ConventionVenueRentalVO v = new ConventionVenueRentalVO();
        BeanUtils.copyProperties(this,v);
        return v;
    }
}
