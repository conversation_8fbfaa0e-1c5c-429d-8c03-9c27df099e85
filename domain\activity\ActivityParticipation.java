package com.exhibition.domain.activity;

import com.exhibition.domain.apply.Encourage;
import com.exhibition.vo.activity.ActivityHelperVO;
import com.exhibition.vo.activity.ActivityParticipationVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 * @desc 活動參加單位
 * @date 2025-05-26
 * @since 1.0.0
 */

@Data
@NoArgsConstructor
@Embeddable
@JsonView(Encourage.EncourageSimpleView.class)
public class ActivityParticipation implements Serializable {
    /**
     * 參加類型
     */
    @Column(columnDefinition = "VARCHAR(75)")
    @Enumerated(EnumType.STRING)
    private ActivityParticipationType participationType;

    /**
     * 報名截止日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date expiryTime;

    /**
     * 補交日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date exhibitionFileEndTime;

    /**
     * 是否主動截止報名
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean expiry;

    /**
     * 限定参展
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean limitedEnterprise;

    public ActivityParticipation(ActivityParticipationVO vo) {
        BeanUtils.copyProperties(vo, this,"activity");
    }

    public ActivityParticipationVO toVO() {
        return toVO(false);
    }
    public ActivityParticipationVO toVO(boolean includeLazy) {
        ActivityParticipationVO vo = new ActivityParticipationVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }
}