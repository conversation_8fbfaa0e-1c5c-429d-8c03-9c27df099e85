package com.exhibition.domain.apply;

import java.util.Arrays;

public enum AppOtherMainlandMeetingEnum {
    /**
     * “國際會議協會”(ICCA)認證的會議
     */
    MEETING("會議"),
    /**
     * □具條件申請“國際會議協會”(ICCA)認證之會議
     */
    EXHIBITION("展覽"),

    MEETINGANDEXHIBITION("會議及展覽"),

    OTHER("其他");


    private final String name;

    AppOtherMainlandMeetingEnum(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static AppOtherMainlandMeetingEnum getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }

}
