package com.exhibition.domain.sys;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.vo.sys.UserSubAccountVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.sys
 * @ClassName: UserSubAccount
 * @description: 子账号
 * @author: ShiXin
 * @create: 2020-04-15 17:58
 **/
@Data
@NoArgsConstructor
@Entity
@Table(name = "user_sub_account")
public class UserSubAccount extends BaseEntity {

    /**
     * 别名
     */
    private String name;

    /**
     * 父账号
     */
    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    private User parent;

    /**
     * 子账号
     */
    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    private User child;


    /**
     * 对应的机构
     */
    @OneToOne(optional = true, fetch = FetchType.LAZY)
    private Institution institution;


    public UserSubAccount(UserSubAccountVO vo) {
        if (null != vo) {
            BeanUtils.copyProperties(vo, this, "parent", "child", "institution");
            if (null != vo.getParentId()) {
                User user = new User();
                user.setId(vo.getParentId());
                setParent(user);
            }
            if (null != vo.getChildId()) {
                User user = new User();
                user.setId(vo.getChildId());
                setChild(user);
            }
            if (null != vo.getInstitutionId()) {
                Institution institution = new Institution();
                institution.setId(vo.getInstitutionId());
                setInstitution(institution);
            }
        }
    }

    public UserSubAccountVO toVO() {
        return toVO(false);
    }

    public UserSubAccountVO toVO(boolean b) {
        UserSubAccountVO accountVO = new UserSubAccountVO();
        BeanUtils.copyProperties(this, accountVO, "parent", "child", "institution");
        if (null != parent) {
            accountVO.setParentId(parent.getId());
            accountVO.setParentName(parent.getName());
            accountVO.setParentEmail(parent.getAccount());
        }

        if (null != child) {
            accountVO.setChildId(child.getId());
            accountVO.setEmail(child.getAccount());
            accountVO.setChildName(child.getName());
            accountVO.setChildEmail(child.getAccount());
        }

        if (null != institution) {
            accountVO.setInstitutionId(institution.getId());
            accountVO.setInstitutionName(institution.getNameZh());
            accountVO.setInstitution(institution.toVO());
        }
        return accountVO;
    }

}
