package com.exhibition.domain.activity;

import com.exhibition.domain.apply.Encourage;
import com.exhibition.vo.activity.ActivityExpensesVO;
import com.exhibition.vo.activity.ActivityHelperVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 費用
 * @date 2020-02-23
 * @since 1.0.0
 */

@Data
@NoArgsConstructor
@Embeddable
@JsonView(Encourage.EncourageSimpleView.class)
public class ActivityExpenses implements Serializable {
    /**
     * 費用金額
     */
    private Double expenses;

    /**
     * 費用類型
     */
    @Enumerated(EnumType.STRING)
    private ActivityExpensesType activityExpensesType;


    public ActivityExpenses(ActivityExpensesVO activityExpensesVO) {
        BeanUtils.copyProperties(activityExpensesVO, this,"activity");
    }


    public ActivityExpensesVO toVO() {
       return toVO(false);
    }
    public ActivityExpensesVO toVO(boolean includeLazy) {
        ActivityExpensesVO vo = new ActivityExpensesVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }
}
