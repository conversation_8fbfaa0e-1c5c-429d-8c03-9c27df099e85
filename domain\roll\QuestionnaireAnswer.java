package com.exhibition.domain.roll;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.activity.Activity;
import com.exhibition.domain.activity.ActivityScope;
import com.exhibition.domain.sys.Institution;
import com.exhibition.vo.roll.AnswerVO;
import com.exhibition.vo.roll.QuestionnaireAnswerVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.BatchSize;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 展会问卷结果
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "questionnaire_answer")
public class QuestionnaireAnswer extends BaseEntity implements Serializable {

    private static final String[] IGNORE_PROPERTIES = new String[]{"activity", "institution", "questionnaire", "answers"};

    /**
     * 参展的展會範圍
     */
    @Enumerated(EnumType.STRING)
    private ActivityScope participateScope;
    /**
     * 参展记录id
     */
    private Long          participateId;
    /**
     * 展會
     */
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private Activity      activity;
    /**
     * 機構
     */
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private Institution   institution;
    /**
     * 提交时间
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date          submitTime;
    /**
     * 展会问卷
     */
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private Questionnaire questionnaire;
    /**
     * 题目结果
     */
    @OneToMany(mappedBy = "questionnaireAnswer", fetch = FetchType.LAZY, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @BatchSize(size = 20)
    private List<Answer>  answers;


    public QuestionnaireAnswer(QuestionnaireAnswerVO v) {
        BeanUtils.copyProperties(v, this, IGNORE_PROPERTIES);
        Long activityId = v.getActivityId();
        if (null != activityId) {
            Activity activity = new Activity();
            activity.setId(activityId);
            this.setActivity(activity);
        }
        Long institutionId = v.getInstitutionId();
        if (null != institutionId) {
            Institution institution = new Institution();
            institution.setId(institutionId);
            this.setInstitution(institution);
        }

        Long questionnaireId = v.getQuestionnaireId();
        if (null != questionnaireId) {
            Questionnaire questionnaire = new Questionnaire();
            questionnaire.setId(questionnaireId);
            this.setQuestionnaire(questionnaire);
        }

        List<AnswerVO> answers = v.getAnswers();
        if (!CollectionUtils.isEmpty(answers)) {
            List<Answer> collect = answers.stream().map(Answer::new)
                    .peek(a -> a.setQuestionnaireAnswer(this))
                    .collect(Collectors.toList());

            this.setAnswers(collect);
        } else {
            this.setAnswers(Collections.emptyList());
        }

    }

    public QuestionnaireAnswerVO toVO() {
        return toVO(false);
    }

    public QuestionnaireAnswerVO toVO(boolean includeLazy) {
        QuestionnaireAnswerVO vo = new QuestionnaireAnswerVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);

        if (null != activity) {
            vo.setActivityId(activity.getId());
            vo.setActivityNameZh(activity.getNameZh());
            vo.setActivityNameEn(activity.getNameEn());
            vo.setActivityNamePt(activity.getNamePt());
        }

        if (null != institution) {
            vo.setInstitutionId(institution.getId());
            vo.setInstitutionNameZh(institution.getNameZh());
            vo.setActivityNameEn(institution.getNameEn());
            vo.setInstitutionNamePt(institution.getNamePt());
        }

        if (null != questionnaire) {
            vo.setQuestionnaireName(questionnaire.getName());
        }

        if (includeLazy) {

            if (null != questionnaire) {
                vo.setQuestionnaire(questionnaire.toSimpleVO());
            }

            if (!CollectionUtils.isEmpty(answers)) {
                vo.setAnswers(answers.stream().map(Answer::toVO).collect(Collectors.toList()));
            }
        }

        return vo;
    }
}
