package com.exhibition.domain.apply;

import com.exhibition.vo.apply.RecordExceptionVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Data
@NoArgsConstructor
@Embeddable
public class RecordException {
    /**
     *
     */
    private static final long serialVersionUID = -4127720903254430399L;

    /**
     * 記錄時間
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date recordTimes;
    /**
     *記錄字段
     */
    @Column(columnDefinition = "varchar(95)")
    private String fields;

    public RecordException(RecordExceptionVO vo) {
        BeanUtils.copyProperties(vo, this);
    }

    public RecordExceptionVO toVO() {
        RecordExceptionVO vo = new RecordExceptionVO();
        BeanUtils.copyProperties(this, vo);
        return vo;
    }
}
