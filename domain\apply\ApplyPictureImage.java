package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.vo.apply.ApplyPictureImageVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "apply_picture_image")
public class ApplyPictureImage extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    private static final String[] IGNORE_PROPERTIES = new String[]{"applyPicture"};
    /**
     * 展會
     */
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private ApplyPicture         applyPicture;

    /**
     * 圖片名稱
     */
    private String                   name;
    /**
     * 圖片url
     */
    private String                   url;


    public ApplyPictureImage(ApplyPictureImageVO vo) {
        BeanUtils.copyProperties(vo, this,IGNORE_PROPERTIES);
    }

    public ApplyPictureImageVO toVO() {
        ApplyPictureImageVO vo = new ApplyPictureImageVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);
        return vo;
    }
}
