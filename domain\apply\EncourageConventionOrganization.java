package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.vo.apply.EncourageConventionOrganizationVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;

import javax.persistence.CascadeType;
import javax.persistence.CollectionTable;
import javax.persistence.Column;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;

/**
 * 活動組織架構
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_convention_organizations")
public class EncourageConventionOrganization extends BaseEntity implements Serializable {
    private static final String[] IGNORE_PROPERTIES = new String[]{"encourageConvention"};
    /**
     * 於組織架構內的身份
     */
    @Column(length = 500)
    private String identity;
    /**
     * 機構名稱
     */
    @Column(length = 500)
    private String orgName;

    @ManyToOne(cascade= CascadeType.ALL)
    @JoinColumn(name = "encourage_convention_id", referencedColumnName = "id")
    private EncourageConvention           encourageConvention;

    /**
     * 活動組織介紹
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_int_file")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> introduceFiles;

    /**
     * 商業登記
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_bus_file")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> businessFiles;

    /**
     * 機構簡介
     */
    @Column(columnDefinition = "text" )
    private String institutionProfile;


    /**
     * 機構附件資料（附件檔案）
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_archives")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> archives;



    public EncourageConventionOrganization(EncourageConventionOrganizationVO vo) {
        BeanUtils.copyProperties(vo, this,IGNORE_PROPERTIES);
    }

    public EncourageConventionOrganizationVO toVO(boolean includeLazy) {
        EncourageConventionOrganizationVO vo = new EncourageConventionOrganizationVO();
        BeanUtils.copyProperties(this, vo,IGNORE_PROPERTIES);

        return vo;
    }

    public EncourageConventionOrganizationVO toVO() {
        return toVO(false);
    }

}
