package com.exhibition.domain.activityInstitution;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/** 
* <AUTHOR>
* @version 创建时间：2020年8月3日 下午2:21:07 
* 类说明 
*/
@Data
public class ActivityInstitution {
	private  String id;
	@JsonFormat(pattern="yyyy-MM-dd")
	private Date startdate;
	@JsonFormat(pattern="yyyy-MM-dd")
	private Date enddate;
	private String sposor;
	private String exurl;
	private String liaisonname;
	private String liaisonphone;
	private String liaisonemail;
	private String regpersonphone;
	private String regpersonname;
	private String regPersonInsitution;
	private String regPersonTitle;
	private String institutionid;
	private String brief;
	private String name;
	private String location;
}
