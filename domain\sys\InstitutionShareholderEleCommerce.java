package com.exhibition.domain.sys;

import com.exhibition.domain.apply.Encourage;
import com.exhibition.vo.sys.InstitutionShareholderEleCommerceVO;
import com.exhibition.vo.sys.InstitutionShareholderVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/9/23 9:40
 * @describe
 */
@Data
@NoArgsConstructor
@Embeddable
@JsonView(Encourage.EncourageSimpleView.class)
public class InstitutionShareholderEleCommerce implements Serializable {

    private static final long serialVersionUID = -4885094193051381289L;

    /**
     * 股东名称
     */
    private String name="";

    /**
     * 股份占比
     */
    private String percent="";

    /**
     * 身份证号码
     */
    private String identification = "";
    /**
     * 公司任职
     */
    private String corporationPosition="";


    public InstitutionShareholderEleCommerce(InstitutionShareholderEleCommerceVO vo) {
        if (null != vo) {
            BeanUtils.copyProperties(vo, this);

        }
    }

    public InstitutionShareholderEleCommerceVO toVO() {
        return toVO(false);
    }

    public InstitutionShareholderEleCommerceVO toVO(boolean includeLazy) {
        InstitutionShareholderEleCommerceVO vo = new InstitutionShareholderEleCommerceVO();
        BeanUtils.copyProperties(this, vo);
        return vo;
    }
}
