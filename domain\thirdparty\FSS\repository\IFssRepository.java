package com.exhibition.domain.thirdparty.FSS.repository;


import cn.hutool.json.JSONObject;
import com.exhibition.base.repository.BaseRepository;
import com.exhibition.domain.sys.Institution;
import com.exhibition.domain.thirdparty.DockingType;
import com.exhibition.domain.thirdparty.FSS.model.entity.FSS;
import com.exhibition.domain.thirdparty.FSS.model.req.FssReq;
import com.exhibition.domain.thirdparty.FSS.model.vo.FssVo;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/29 11:53
 * @describe
 */
public interface IFssRepository extends BaseRepository<FSS,Long> {
    @Query(value = "select ei.name_zh as username,dept,dsf_no,fss_emp_no,stryq,endyq,ef.id\n" +
            "from ex_macau_fss ef\n" +
            "    left join ex_institution ei on ef.institution_id = ei.id\n" +
            "where is_verify = ?1 and status_code !=200",nativeQuery = true)
    List<JSONObject> findFss(String isVerify);

    List<FSS> findByisVerifyAndType(Boolean verify, DockingType type);

    FSS findFirstByInstitutionOrderByIdDesc(Institution institution);

    FSS findByTraceId(String traceId);

    @Query(value = "select ei.name_zh as username,\n" +
            "       ef.dept,\n" +
            "       ef.dsf_no as dsfNo,\n" +
            "       ef.fss_emp_no as fssEmpNo,\n" +
            "       ef.stryq,\n" +
            "       ef.endyq,\n" +
            "       ef.location_registration_code as locationRegistrationCode,\n" +
            "       ef.financial_premises_code as financialPremisesCode,\n" +
            "       ef.business_situation as businessSituation,\n" +
            "       ef.acquire_supplements as acquireSupplements,\n" +
            "       ef.is_verify as isVerify,\n" +
            "       ef.is_pay_social_security as isPaySocialSecurity,\n" +
            "       ef.status_code as statusCode,\n" +
            "       ef.id\n" +
            "from ex_macau_fss ef\n" +
            "left join ex_institution ei on ef.institution_id = ei.id\n" +
            "where ef.is_verify = ?1 and ef.status_code =?2",nativeQuery = true)
    List<JSONObject> findFssVo(Boolean verify, String statusCode);

    List<FSS> findByAccount(String account);

    List<FSS> findByAccountAndType(String account, DockingType fundoDeSegurancaSocial);
}
