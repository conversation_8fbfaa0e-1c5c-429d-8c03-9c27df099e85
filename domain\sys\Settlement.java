package com.exhibition.domain.sys;

import com.exhibition.domain.apply.Encourage;
import com.exhibition.exception.RenException;
import com.fasterxml.jackson.annotation.JsonView;

import java.util.Arrays;

@JsonView(Encourage.EncourageSimpleView.class)
public enum Settlement {

    /**
     * 未結算
     */
    nosettlement("未結算"),
    /**
     * 已結算
     */
    clsd("已結算");

    private final String name;

    Settlement(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static Settlement getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }

    public static Settlement getEnumFromString(String string) {
        if (string != null) {
            try {
                return Enum.valueOf(Settlement.class, string.trim());
            } catch (IllegalArgumentException e) {
                throw new RenException(e.getMessage());
            }
        }
        return null;
    }
}
