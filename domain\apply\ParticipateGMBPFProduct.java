package com.exhibition.domain.apply;

import java.util.Arrays;

public enum ParticipateGMBPFProduct {

    /**
     * 澳門製造商品
     */
    MADE_IN_MACAO("澳門製造商品"),
    /**
     * 澳門品牌商品
     */
    MACAO_BRAND("澳門品牌商品"),
    /**
     * 本澳企業代理之海外品牌之商品
     */
    MACAO_AGENCY("本澳企業代理之海外品牌之商品"),
    MACAO_four("横琴粤澳深度合作区产品"),
    /**
     * 日常用品
     */
    DAILY_SUPPLIES("日常用品"),
    /**
     * 禮品
     */
    GIFT("禮品"),
    /**
     * 食品
     */
    FOOD("食品"),
    /**
     *商匯館之商品
     */
    MACAO_one("商匯館之商品"),
    /**
     *葡語國家食品展示中心之商品
     */
    MACAO_two("葡語國家食品展示中心之商品"),
    /**
     *本澳企業代理境外國家/地區之商品
     */
    MACAO_three("本澳企業代理境外國家/地區之商品"),
    Thailand("泰國"),
    Malaysia("馬來西亞"),
    myanmar("緬甸"),
    Indonesia("印尼"),
    Singapore("新加坡"),
    /**
     * 其他
     */
    OTHER("其他");

    private final String name;

    ParticipateGMBPFProduct(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ParticipateGMBPFProduct getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
