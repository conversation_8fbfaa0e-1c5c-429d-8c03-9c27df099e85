package com.exhibition.domain.roll;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.vo.roll.ExamineVO;
import com.exhibition.vo.roll.QuestionVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Type;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Lob;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 普通问卷
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "examine")
public class Examine extends BaseEntity implements Serializable {
    private static final long     serialVersionUID  = 7930283883250587657L;
    private static final String[] IGNORE_PROPERTIES = new String[]{"questions"};
    /**
     * 名称
     */
    private String name;
    /**
     * 标题
     */
    private String title;
    /**
     * 发起机构
     */
    private String initiator;
    /**
     * 主题
     */
    private String theme;
    /**
     * 说明内容
     */
    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Type(type="text")
    @Column(name="content", nullable=false)
    private String content;


    /**
     * 逻辑删除状态,0:无效,1:有效
     */
    @Column(columnDefinition = "tinyint default 1")
    private Boolean        status;
    /**
     * 题目
     */
    @OneToMany(mappedBy = "questionnaire", fetch = FetchType.LAZY, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @BatchSize(size = 20)
    private List<Question> questions;


    public Examine(ExamineVO vo) {
        BeanUtils.copyProperties(vo, this, IGNORE_PROPERTIES);

        List<QuestionVO> questions = vo.getQuestions();
        if (!CollectionUtils.isEmpty(questions)) {
            List<Question> collect = questions.stream()
                    .map(Question::new)
                    .peek(q -> q.setExamine(this))
                    .collect(Collectors.toList());
            this.setQuestions(collect);
        } else {
            setQuestions(Collections.emptyList());
        }

    }

    public ExamineVO toVO() {
        return toVO(false);
    }

    public ExamineVO toVO(boolean includeLazy) {
        ExamineVO vo = new ExamineVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);

        if (!CollectionUtils.isEmpty(questions)) {
            vo.setQuestionSize(questions.size());
        }

        if (includeLazy) {

            if (!CollectionUtils.isEmpty(questions)) {
                vo.setQuestions(questions.stream().map(q -> q.toVO(true)).collect(Collectors.toList()));
            }
        }
        return vo;
    }

    public ExamineVO toSimpleVO() {
        ExamineVO vo = new ExamineVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);

        if (!CollectionUtils.isEmpty(questions)) {
            vo.setQuestionSize(questions.size());
        }

        if (!CollectionUtils.isEmpty(questions)) {
            vo.setQuestions(questions.stream().map(q -> q.toVO(true)).collect(Collectors.toList()));
        }

        return vo;
    }
}
