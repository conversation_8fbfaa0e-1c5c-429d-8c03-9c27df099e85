package com.exhibition.domain.apply;

import com.exhibition.util.DesensitizationUtils;
import com.exhibition.util.StringUtils;
import com.exhibition.vo.apply.AppOtherMainlandVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/25 15:39
 * @describe
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "appother_mainland")
@BatchSize(size = 20)
public class AppOtherMainland extends Encourage<AppOtherMainland, AppOtherMainlandVO> implements Serializable {

    private static final long serialVersionUID = 5222788763829965175L;

    private static final String[] IGNORE_PROPERTIES = new String[]{
            "liaisonSub", "meetingDates", "activityDates", "conventionHistory", "openTimes", "activityJoins",
            "meetingRooms", "conventionDinings", "conventionVenueRentals", "exMeetingRooms", "conventionOtherGovs", "applyItems", "organizations"};


    //第一部分，申請者資料
    private String nameZh;
    private String nameEn;
    @Column(columnDefinition = "tinyint default 0")
    //0為男，1為女
    private Boolean sex;
    @Temporal(TemporalType.TIMESTAMP)
    private Date brithday;
    //大陸省身份證
    private String identifyCard;
    //澳門身份證
    private String identifyCardOfMacao;
    //->場所地址
    @Column(length = 500)
    private String streetZh;
    //->城市
    @Column(length = 500)
    private String cityZh;
    //>省份
    @Column(length = 500)
    private String provinceZh;
    //聯絡電話
    private String phone;
    //電郵
    private String email;
    //緊急聯絡人
    private String emergencyContactName;
    private String emergencyContactPhone;
    private String emergencyContactEmail;
    private String institutionName;
    private String institutionCode;
    //第二部分：申請事由
    //赴澳目的
    @Enumerated(EnumType.STRING)
    private AppOtherMainlandPurposeEnum purpose;
    //赴澳目的其他
    private String purposeEnumOther;
    //活動名稱
    private String activityName;
    //活動類型
    @Enumerated(EnumType.STRING)
    private AppOtherMainlandMeetingEnum meeting;
    //活動類型其他
    private String meetingEnumOther;
    //主辦單位
    private String organizers;
    //活動開始時間
    @Temporal(TemporalType.TIMESTAMP)
    private Date activityStart;
    //活動結束時間
    @Temporal(TemporalType.TIMESTAMP)
    private Date activityEnd;
    //參與角色
    @Enumerated(EnumType.STRING)
    private AppOtherMainlandRoleEnum role;
    //參與角色其他
    private String roleOther;
    //第三部分
    private Boolean isSure;
    //二維碼内容
    private String scanCode;
    //营业执照
    @ElementCollection
    @CollectionTable(name = "appother_mainland_business_license_files")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> businessLicenseFiles;
    //出具證明
    @ElementCollection
    @CollectionTable(name = "appother_mainland_certificate_files")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> certificate;
    //入澳时间
    @Temporal(TemporalType.TIMESTAMP)
    private Date enterMacaoTime;
    //离澳时间
    @Temporal(TemporalType.TIMESTAMP)
    private Date exitMacaoTime;
    //备注
    private String remark;

    @ElementCollection
    @CollectionTable(name = "appother_mainland_supplementinfo_file")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> mainland_accessory;
    
    private AppOtherMainlandFrequencyEnum frequencyEnum;
    /**
     * 補齊資料時間
     */
    private Date supplementinfoTime;


    public AppOtherMainlandVO toVO(boolean includeLazy) {
        AppOtherMainlandVO vo = new AppOtherMainlandVO();
        copyProperties(this, vo, IGNORE_PROPERTIES);
        return vo;
    }

    @Override
    public AppOtherMainlandVO toVO() {
        return toVO(false);
    }

    /*
    * 脱敏专用
    * */
    public AppOtherMainlandVO toDesensitization(AppOtherMainlandVO vo){
        vo.setNameZh(StringUtils.isNotEmpty(vo.getNameZh())?DesensitizationUtils.name(vo.getNameZh()):"");
        vo.setIdentifyCard(StringUtils.isNotEmpty(vo.getIdentifyCard())?DesensitizationUtils.identificationCard(vo.getIdentifyCard()):"");
        vo.setIdentifyCardOfMacao(StringUtils.isNotEmpty(vo.getIdentifyCardOfMacao())?DesensitizationUtils.passportOfMacaoAndHongkong(vo.getIdentifyCardOfMacao()):"");
        return vo;
    }


    public AppOtherMainland(AppOther e){
        BeanUtils.copyProperties(e, this);
    }

    public AppOtherMainland(AppOtherMainlandVO vo) {
        copyProperties(vo, this, IGNORE_PROPERTIES);
    }

}
