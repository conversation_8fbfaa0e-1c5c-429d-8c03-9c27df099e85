package com.exhibition.domain.apply;

import com.exhibition.vo.apply.ParticipateMIECFVO;
import com.exhibition.vo.apply.ParticipateVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import javax.persistence.CollectionTable;
import javax.persistence.Column;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: ParticipateThisBureau
 * @description: 參加本局展會
 * @author: ShiXin
 * @create: 2020-03-17 10:21
 **/
@Data
@NoArgsConstructor
@Entity
@Table(name = "participate_miecf")
public class ParticipateMIECF extends Participate<ParticipateMIECF, ParticipateVO> implements Serializable {


    private static final long serialVersionUID = 810225304277862927L;


    /**
     * 其他參展方式
     */
    private String otherExhibitMethod;

    /**
     * 展位喜好
     */
    @Enumerated(value = EnumType.STRING)
    private ParticipateExhibitBoothPreference preference;

    /**
     * 希望參展面積
     */
    private double area;

    /**
     * 過往是否參展
     */
    private Boolean attendHistoryExhibition;

    /**
     * 过往參展年份
     */
    private String attendHistoryYear;


    /**
     * 參展產品、服務
     */
    @ElementCollection
    @CollectionTable(name = "participate_miecf_exhibition_product")
    @Enumerated(EnumType.STRING)
    private List<ParticipateMIECFProduct> exhibitionProducts;

    /**
     * 其他產品說明
     */
    private String otherProductSpecify;

    /**
     * 商業配對意向
     */
    @ElementCollection
    @CollectionTable(name = "participate_miecf_business_matching")
    @Enumerated(EnumType.STRING)
    private List<ParticipateBusinessMatching> businessMatchings;

    /**
     * 其他商業配對意向說明
     */
    private String otherMatchingSpecify;

    /**
     * 主要目標市場
     */
    @ElementCollection
    @CollectionTable(name = "participate_miecf_tarket_market")
    @Enumerated(EnumType.STRING)
    private List<ParticipateTargetMarket> targetMarkets;

    /**
     * 其他亞洲地區說明
     */
    private String otherAsiaAreaSpecify;

    /**
     * 其他國家地區說明
     */
    private String otherCountriesSpecify;


    /**
     * 繳費記錄
     */
    @ElementCollection
    @CollectionTable(name = "participate_miecf_payment_record")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> paymentRecordFiles;


    /**
     * 組團企業
     */
    @ElementCollection
    @CollectionTable(name = "participate_miecf_group")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateEnterpriseGroup> groups;


    /**
     * 上傳信函文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_miecf_letter")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> letterFiles;


    /**
     * 申請單位文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_miecf_unit")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> applicantUnitFiles;
    /**
     * 由商業及動產登記局於三個月內發出之本澳之商業登記證明/書面報告副本—>附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_miecf_shangye")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> shangyeFiles;
    /**
     * 營業稅 - 開業/更改申報表(M/1表格) 或財政局發出之開業聲明書副本—>附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_miecf_macaom1")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> macaom1Files;
    /**
     * 身份证
     */
    @ElementCollection
    @CollectionTable(name = "participate_miecf_identity")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> identityFiles;


    /**
     * 備註
     */
    private String remarks;
    /**
     * 参展优惠
     */
    private String concessions;


    /**\
     * 公司简介
     */
    @Column(nullable = false,columnDefinition = "TEXT")
    private String companyProfile;

    /**
     * 参展的照片
     */
    @ElementCollection
    @CollectionTable(name = "participate_miecf_image")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> images;

    /**
     *  参展的宣传片
     */
    @ElementCollection
    @CollectionTable(name = "participate_miecf_video")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> video;

    /**
     *  参展商名单
     */
    @ElementCollection
    @CollectionTable(name = "participate_miecf_exhibitor")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> exhibitor;
    /**
     * 展品
     */
    private String exhibits;
    /**
     * 展位楣板（中文）
     */
    private String boothlintel;
    /**
     * 展位楣板（英文）
     */
    private String boothlintelen;
    /**
     * 公司簡介英文
     */
    @Column(columnDefinition = "text")
    private String companyProfileen;
    /**
     * 主要業務範圍
     */
    @ElementCollection
    private List<String> mainBusinessscope;
    /**
     * 其它
     */
    private String otherMainBusinessscope;
    /**
     * 次要業務範圍
     */
    @ElementCollection
    private List<String> lessBusinessscope;
    /**
     * 其它
     */
    private String otherlessBusinessscope;
    /**
     * 是否電商平台
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean ePlatform;
    /**
     * 電商平台名稱
     */
    private String platformName;
    /**
     * 商業配對意向
     */
    private String  busniessIntention;
    /**
     * 有意尋找的採購商/合作夥伴類型（如超市、餐廳或貿易公司等）
     */
    private String Partner;
    /**
     * 展品類別
     */
    @ElementCollection
    private List<String>  ExhibitCategory;
    /**
     * 其它展品類別
     */
    private String otherexhibitCategory;
    //是否同意
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isagree;
    //是否聲明
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isstate;
    //現聲明本申請單位知悉並同意主辦及承辦單位對上述資料及內容不負任何法律責任，並同意主辦及承辦單位保留使用、發放及宣傳推廣的權利
    @Column(columnDefinition = "tinyint default 0")
    private Boolean ispublic;

    //是否商業配對
    @Column(columnDefinition = "tinyint default 0")
    private Boolean  isbm;


    public ParticipateMIECF(ParticipateMIECFVO vo) {
        copyProperties(vo, this);
    }

    @Override
    public ParticipateMIECFVO toVO() {
        return toVO(false);
    }

    public ParticipateMIECFVO toVO(boolean includeLazy) {
        ParticipateMIECFVO vo = new ParticipateMIECFVO();
        copyProperties(this, vo);
        return vo;
    }

}
