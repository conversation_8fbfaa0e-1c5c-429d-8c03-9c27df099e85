package com.exhibition.domain.apply;

import com.exhibition.vo.apply.ParticipateIIICFVO;
import com.exhibition.vo.apply.ParticipateVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import javax.persistence.CollectionTable;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "participate_iiicf")
public class ParticipateIIICF extends Participate<ParticipateIIICF, ParticipateVO>  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 展位喜好
     */
    @Enumerated(value = EnumType.STRING)
    private ParticipateExhibitBoothPreference preference;

    /**
     * 希望參展面積
     */
    private double area;

    /**
     * 過往是否參展
     */
    private Boolean attendHistoryExhibition;

    /**
     * 过往參展年份
     */
    private String attendHistoryYear;

    /**
     * 商業配對意向說明
     */
    @ElementCollection
    @CollectionTable(name = "participate_iiicf_business_matching")
    @Enumerated(EnumType.STRING)
    private List<ParticipateBusinessMatching> businessMatchings;

    /**
     * 商業配對意向說明
     */
    private String otherMatchingSpecify;

    /**
     * 主要目標市場
     */
    @ElementCollection
    @CollectionTable(name = "participate_iiicf_tarket_market")
    @Enumerated(EnumType.STRING)
    private List<ParticipateTargetMarket> targetMarkets;

    /**
     * 其他亞洲地區說明
     */
    private String otherAsiaAreaSpecify;

    /**
     * 其他國家地區說明
     */
    private String otherCountriesSpecify;


    /**
     * 繳費記錄
     */
    @ElementCollection
    @CollectionTable(name = "participate_iiicf_payment_record")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> paymentRecordFiles;


    /**
     * 組團企業
     */
    @ElementCollection
    @CollectionTable(name = "participate_iiicf_group")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateEnterpriseGroup> groups;


    /**
     * 上傳信函文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_iiicf_letter")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> letterFiles;


    /**
     * 申請單位文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_iiicf_unit")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> applicantUnitFiles;


    /**
     * 備註
     */
    private String remarks;


    /**\
     * 公司简介
     */
    private String companyProfile;

    /**
     * 参展的照片
     */
    @ElementCollection
    @CollectionTable(name = "participate_iiicf_image")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> images;

    /**
     *  参展的宣传片
     */
    @ElementCollection
    @CollectionTable(name = "participate_iiicf_video")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> video;

    /**
     *  参展商名单
     */
    @ElementCollection
    @CollectionTable(name = "participate_iiicf_exhibitor")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> exhibitor;

    public ParticipateIIICF(ParticipateIIICFVO vo) {
        copyProperties(vo, this);
    }

    @Override
    public ParticipateIIICFVO toVO() {
        return toVO(false);
    }

    public ParticipateIIICFVO toVO(boolean includeLazy) {
        ParticipateIIICFVO vo = new ParticipateIIICFVO();
        copyProperties(this, vo);
        return vo;
    }
}
