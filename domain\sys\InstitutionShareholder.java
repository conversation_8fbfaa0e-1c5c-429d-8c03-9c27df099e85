package com.exhibition.domain.sys;

import cn.hutool.core.util.ObjectUtil;
import com.exhibition.domain.apply.Encourage;
import com.exhibition.vo.sys.InstitutionShareholderVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import java.io.Serializable;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.sys
 * @ClassName: Shareholder
 * @description: 股东
 * @author: ShiXin
 * @create: 2020-02-25 16:44
 **/
@Data
@NoArgsConstructor
@Embeddable
@JsonView(Encourage.EncourageSimpleView.class)
public class InstitutionShareholder implements Serializable {

    private static final long serialVersionUID = -4885094193051381289L;

    /**
     * 股东名称
     */
    private String name="";

    /**
     * 股份占比
     */
    private String percent="";

    /**
     * 身份证号码
     */
    private String identification = "";
//    /**
//     * 身份证号码脱敏
//     */
//    private String     idCardEncryption = "";
    /**
     * 是否澳門居民
     */
    private Boolean     macaoResidents;

    /**
     * 公司任职
     */
    private String corporationPosition="";


    public InstitutionShareholder(InstitutionShareholderVO vo) {
        if (null != vo) {
            BeanUtils.copyProperties(vo, this);

        }
    }

    public InstitutionShareholderVO toVO() {
        return toVO(false);
    }

    public InstitutionShareholderVO toVO(boolean includeLazy) {
        InstitutionShareholderVO vo = new InstitutionShareholderVO();
        BeanUtils.copyProperties(this, vo);
        if (ObjectUtil.isEmpty(this.identification)) {
            return vo;
        }
        vo.setIdentification(desensitizeIdCard(this.identification));
        return vo;
    }
    /**
     * 身份证号码脱敏处理
     * @param idNumber 身份证号码
     * @return 脱敏后的身份证号码
     */
    public static String desensitizeIdCard(String idNumber) {
        if (idNumber == null || idNumber.isEmpty()) {
            return "";
        }
        idNumber.trim();
        // 澳门身份证处理(8位，首字母+6数字+1校验码)
        if (idNumber.length() == 8) {
            // 保留后4位，前面用*代替
            int length = idNumber.length();
            String lastFour = idNumber.substring(length - 4);
            return "****" + lastFour;
        }
        // 大陆18位身份证处理
        else if (idNumber.length() == 18) {
            int length = idNumber.length();
            String lastFour = idNumber.substring(length - 4);
            return "****" + lastFour;
        }
        // 大陆15位身份证处理
        else if (idNumber.length() == 15) {
            int length = idNumber.length();
            String lastFour = idNumber.substring(length - 4);
            return "****" + lastFour;
        }
        // 其他格式不做处理
        else if(idNumber.length()>4) {
            int length = idNumber.length();
            String lastFour = idNumber.substring(length - 4);
            return "****" + lastFour;
        }
        else {
            return idNumber;
        }
    }
}
