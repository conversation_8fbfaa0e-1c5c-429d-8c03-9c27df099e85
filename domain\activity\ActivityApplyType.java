package com.exhibition.domain.activity;

import com.exhibition.domain.apply.Encourage;
import com.fasterxml.jackson.annotation.JsonView;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @desc 展會申請類型
 * @date 2020-03-17
 * @since 1.0.0
 */
@JsonView(Encourage.EncourageSimpleView.class)
public enum ActivityApplyType {
    /**
     * 澳門國際環保合作發展論壇及展覽 -MIECF
     */
    MIECF("澳門國際環保合作發展論壇及展覽 -MIECF"),
    /**
     * 國際基礎設施投資與建設高峰論墰 -IIICF
     */
    IIICF("國際基礎設施投資與建設高峰論墰 -IIICF"),
    /**
     * 澳門國際品牌連鎖加盟展 -MFE
     */
    MFE("澳門國際品牌連鎖加盟展 -MFE"),
    /**
     * 粵澳名優商品展 -GMBPF
     */
    GMBPF("粵澳名優商品展 -GMBPF"),
    /**
     * 澳門國際貿易展覽會 -MIF
     */
    MIF("澳門國際貿易展覽會 -MIF"),
    /**
     * 葡語國家產品及服務展 -PLPEX
     */
    PLPEX("葡語國家產品及服務展 -PLPEX"),

    /**
     * 參展
     */
    PARTICIPATE("參展"),
    /**
     * 參加代表團
     */
    MISSION("參加代表團"),
    /**
     * 參會記錄
     */
    ATTEND_RECORD("參會記錄"),
    /**
     * 簽約記錄
     */
    CONTRACT_RECORD("簽約記錄"),
    /**
     * 簽約記錄
     */
    BM_RECORD("BM匹配記錄"),
    /**
     * 專業觀眾
     */
    PROFESSION("專業觀眾"),
    /**
     * 非牟利社團赴境外展會設置展位
     */
    ATTEND("非牟利社團赴境外展會設置展位"),
    /**
     * 非牟利社團組織代表團參與境外展會
     */
    EN_MISSION("非牟利社團組織代表團參與境外展會"),
    /**
     * 企業參與本地或境外展會
     */
    ENTERPRISE("企業參與本地或境外展會"),
    /**
     * 會議及展覽資質計劃
     */
    CONVENTION("會議及展覽資質計劃"),
    /**
     * 展會及商務旅游展支持計劃
     */
    TOUR("展會及商務旅游展支持計劃"),
    /**
     * 展會專業人才培訓支援計劃
     */
    TRAIN("展會專業人才培訓支援計劃"),
    /**
     * 電子商務B2B推廣
     */
    ECB2B("電子商務B2B推廣"),/**
     * 全球合法與可持續木業高峰論壇
     */
    ITTO("全球合法與可持續木業高峰論壇"),
    /**
     * 電子商務B2C推廣
     */
    ECB2C("電子商務B2C推廣"),
    /**
     * 國際專業會議募集計劃
     */
    CONFERENCE("國際專業會議募集計劃"),

    /**
     * 一站式
     */
    ONESTEP("一站式"),
    /**
     * B2C
     */
    B2C("B2C"),
    /**
     * B2B
     */
    B2B("B2B"),

    /**
     * 內地赴澳證明
     */
    APPOTHER_MAINLAND("內地赴澳證明");

    private final String name;

    ActivityApplyType( String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ActivityApplyType getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
