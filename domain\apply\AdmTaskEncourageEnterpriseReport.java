package com.exhibition.domain.apply;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 * <AUTHOR>
 * @desc 支持及鼓励措施审批
 * @date 2020-06-06
 * @since 1.0.0
 */
@Data
@Entity
@NoArgsConstructor
@DiscriminatorValue(Flowtpl.ENCOURAGE_ENTERPRISE_REPORT)
public class AdmTaskEncourageEnterpriseReport extends AdmTask {
 
    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    private EncourageEnterpriseReport  encourageEnterpriseReport;
    
}