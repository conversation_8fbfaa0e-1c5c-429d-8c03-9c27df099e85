package com.exhibition.domain.thirdparty;

import com.exhibition.util.StringUtils;
import com.exhibition.util.task.AwokeTask;
import lombok.*;
import org.apache.ibatis.javassist.SerialVersionUID;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/12 10:49
 * @describe 034预审使用，视图层
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EncourageConventionInquiry implements Serializable {
    private static final long serialVersionUID = 1L;
    private String code;
    private String institutionName;
    private String activityName;
    private String applyTime;
    private String personId;
    private String situatingType;
}
