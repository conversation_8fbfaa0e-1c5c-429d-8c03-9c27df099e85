package com.exhibition.domain.sys;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.vo.sys.PurviewVO;
import com.fasterxml.jackson.annotation.JsonBackReference;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 权限
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "purview")
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE, region = "purview",
        include = "non-lazy")
public class Purview extends BaseEntity implements Serializable {
    private static final long          serialVersionUID = 8602951632793271768L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private              Long          id;
    /**
     * 权限代码
     */
    @Column(nullable = false, unique = true, length = 100)
    private              String        code;
    /**
     * 权限名称
     */
    @Column(nullable = false)
    private              String        name;
    /**
     * 权限类型
     */
    @Enumerated(value = EnumType.STRING)
    private              PurviewType   type;
    /**
     * 所属权限分组
     */
    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    private              Purview       parent;
    /**
     * 当权限类型为分组时，显示期下面的子权限
     */
    @OneToMany(mappedBy = "parent", fetch = FetchType.LAZY)
    @BatchSize(size = 20)
    private              List<Purview> child;
    /**
     * 角色
     */
    @ManyToMany(mappedBy = "purviews", fetch = FetchType.LAZY)
    @JsonBackReference
    private              List<Role>    roles;

    public Purview(PurviewVO vo) {
        BeanUtils.copyProperties(vo, this, "parent", "child", "roles");
        if (null != vo.getParentId()) {
            Purview parent = new Purview();
            parent.setId(vo.getParentId());
            this.setParent(parent);
        }
    }

    public PurviewVO toVO() {
        return toVO(false);
    }

    public PurviewVO toVO(boolean includeLazy) {
        PurviewVO vo = new PurviewVO();
        BeanUtils.copyProperties(this, vo, "parent", "child", "roles");
        if (null != parent) {
            vo.setParentId(parent.getId());
            vo.setParentName(parent.getName());
            vo.setParentCode(parent.getCode());
        }
        vo.setChildrenCount(this.getChild() != null ? this.getChild().size() : 0);
        if (includeLazy) {
            if (!CollectionUtils.isEmpty(child)) {
                vo.setChildren(child.stream().map(p -> p.toVO(includeLazy)).collect(Collectors.toList()));
            }
        }
        return vo;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Purview purview = (Purview) o;
        return Objects.equals(id, purview.id) &&
                Objects.equals(code, purview.code) &&
                Objects.equals(name, purview.name) &&
                type == purview.type;
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), id, code, name, type);
    }

    @Override
    public String toString() {
        return "Purview{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", type=" + type +
                ", parent=" + parent +
                ", child=" + child +
                ", roles=" + roles +
                '}';
    }
}
