package com.exhibition.domain.apply;

import com.exhibition.vo.apply.EncourageMICEVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import javax.persistence.CollectionTable;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 035會展及商務旅遊展
 * @date 2020-06-12
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_mice")
public class EncourageMICE extends Encourage<EncourageMICE, EncourageMICEVO> implements Serializable {
  

    private static final long serialVersionUID = 6161376160807268753L;
    
    /**
     * 机构名称(中文)
     */
    private String                       nameZh;
    /**
     * 机构名称(英文)
     */
    private String                       nameEn;
    /**
     * 机构名称(英文)
     */
    private String                       address;
    /**
     * 联络人
     */
    private String                       contactPersion;
    /**
     * 电话
     */
    private String                       phone;
    /**
     * 职位
     */
    private String                       title;
    /**
     * 电子邮箱
     */
    private String                       email;
    /**
     * 传真
     */
    private String                       fax;
    /**
     * 網址
     */
    private String                       website;
    /**
     * 開業登記M/1中之場所登記編號
     */
    private String                        M1Number;
    /**
     * 業務範圍
     */
    private String                        scopeService;
    
    /**
     * 展會名稱
     */
    private String                        name;
    /**
     * 日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date                          startTime;
    /**
     * 地點
     */
    private String                        venue;
    /**
     * 附件
     */
    @ElementCollection
    @CollectionTable(name = "encourage_mice_attachment")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCertAttachment> attachments;
    /**
     * 声明
     */
    private Boolean                       stateAgree;

    public EncourageMICE(EncourageMICEVO vo) {
        copyProperties(vo, this);
    }

    @Override
    public EncourageMICEVO toVO() {
        EncourageMICEVO vo = new EncourageMICEVO();
        copyProperties(this, vo);
        return vo;
    }
}