package com.exhibition.domain.thirdparty;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.apply.Encourage;
import com.exhibition.domain.sys.User;
import com.exhibition.vo.sys.PurviewVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.util.List;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@Entity
@Table(name = "macau_one_account_entity")
@BatchSize(size = 20)
@JsonView(Encourage.EncourageSimpleView.class)
public class GovMoEntity extends BaseEntity {
    private String euid;
    private String username;
    private String namePt;
    private String nameCn;
    private String identityNo;
    private String identityType;
    private String contactPhone;
    private String email;
    private String currentG2eEntityId;
    private String identityIssuePlace;
    /**
     * 机构联络人
     */
    @ElementCollection
    @CollectionTable(name = "macau_one_account_entity_entities")
    @Fetch(FetchMode.SELECT)
    private List<GovMoEntityEntities> entities;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id") // 外键列
    private User user;

    private String captcha;

    public PurviewVO toVO(boolean includeLazy) {
        PurviewVO vo = new PurviewVO();
        BeanUtils.copyProperties(this, vo, "entities");
        return vo;
    }
}
