package com.exhibition.domain.sys;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.vo.sys.CompanyDocumentVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.sys
 * @ClassName: CompanyDocument
 * @description: 檔案管理
 * @author: ShiXin
 * @create: 2020-04-17 10:57
 **/
@Data
@NoArgsConstructor
@Entity
@Table(name = "company_document")
public class CompanyDocument extends BaseEntity implements Serializable {

    private static final long         serialVersionUID = -6977068551004765059L;
    /**
     * 文档的名称
     */
    private              String       name;
    /**
     * 文档的地址
     */
    private              String       url;
    /**
     * 文档的格式
     */
    private              String       filePattern;
    /**
     * 文档的大小
     */
    private              String       fileSize;
    /**
     * 分类
     */
    @ManyToOne
    @Fetch(FetchMode.SELECT)
    private              CommonConfig category;
    /**
     * 是否在用户端展示，true：展示
     */
    private              Boolean       clientShow;


    public CompanyDocument(CompanyDocumentVO companyDocumentVO) {
        BeanUtils.copyProperties(companyDocumentVO, this);
        if (null != companyDocumentVO.getCategoryId()) {
            CommonConfig commonConfig = new CommonConfig();
            commonConfig.setId(companyDocumentVO.getCategoryId());
            setCategory(commonConfig);
        }
    }

    public CompanyDocumentVO toVO() {
        return toVO(false);
    }

    public CompanyDocumentVO toVO(boolean includeLazy) {
        CompanyDocumentVO documentVO = new CompanyDocumentVO();
        BeanUtils.copyProperties(this, documentVO, "category");
        if (null != category) {
            documentVO.setCategoryId(category.getId());
            documentVO.setCategoryName(category.getName());
        }
        return documentVO;
    }
}
