#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自動為 exhibition_pros.sql 文件添加繁體中文備註
"""

import re
import os

def add_chinese_comments():
    """為 SQL 文件添加繁體中文備註"""
    
    # 字段名到繁體中文備註的映射
    field_comments = {
        # 基礎字段
        'id': '主鍵ID',
        'create_at': '建立時間',
        'update_at': '更新時間',
        'created_at': '建立時間',
        'updated_at': '更新時間',
        
        # 文件相關
        'oriname': '原始檔案名',
        'uid': '檔案唯一標識',
        'url': '檔案URL',
        
        # 基本信息
        'name': '名稱',
        'code': '代碼',
        'type': '類型',
        'status': '狀態',
        'title': '標題',
        'content': '內容',
        'description': '描述',
        'remark': '備註',
        'comment': '備註',
        'note': '備註',
        
        # 聯絡信息
        'email': '電子郵箱',
        'phone': '電話',
        'tel': '電話',
        'fax': '傳真',
        'address': '地址',
        'area_code': '區域代碼',
        'area_code_or_fax': '區域代碼或傳真',
        
        # 個人信息
        'gender': '性別',
        'name_zh': '姓名(中文)',
        'name_en': '姓名(英文)',
        'name_pt': '姓名(葡文)',
        
        # 活動相關字段
        'activity_scope': '活動範圍',
        'apply_num': '申請機構數',
        'city': '城市',
        'city_zh': '城市(中文)',
        'country_zh': '國家(中文)',
        'province_zh': '省份(中文)',
        'content_en': '展會內容(英文)',
        'content_pt': '展會內容(葡文)',
        'content_zh': '展會內容(中文)',
        'cost': '費用',
        'end_time': '結束時間',
        'expiry': '是否過期',
        'expiry_time': '過期時間',
        'momentous': '是否重點活動',
        'other_text': '其他文字',
        'place': '舉辦地點',
        'place_en': '舉辦地點(英文)',
        'publish_time': '發佈時間',
        'scope': '展會配圖',
        'start_time': '開始時間',
        'website': '網站',
        'institution_id': '機構ID',
        'reg_person_name': '註冊人姓名',
        'reg_person_phone': '註冊人電話',
        'admin_id': '管理員ID',
        
        # 統計數據字段
        'activity_amount': '活動數量',
        'activity_area': '活動區域',
        'average_consumption': '平均消費',
        'budget': '預算',
        'business_talk': '商務洽談',
        'degree_of_satisfaction': '滿意度',
        'entrance_person': '入場人數',
        'exhibitor_amount': '參展商數量',
        'exhibitor_region': '參展商地區',
        'final_budget': '最終預算',
        'income': '收入',
        'major_audience': '主要觀眾',
        'officer_person': '官員人數',
        'policymaker': '政策制定者',
        'signed_project': '簽約項目',
        
        # 費用相關
        'exhibition_cost': '參展費用',
        'exhibition_cost_explain': '參展費用說明(中文)',
        'exhibition_cost_explain_en': '參展費用說明(英文)',
        'exhibition_cost_explain_pn': '參展費用說明(葡文)',
        'exhibition_end_time': '參展報名截止日期',
        'exhibition_file_end_time': '參展補交文件日期',
        'tuxedo_cost': '參團費用',
        'uxedo_end_time': '參團報名截止日期',
        
        # 詳細統計字段
        'actual_number_of_transactions_on_site': '現場實際交易宗數',
        'agent_countries': '代理葡語國家產品',
        'aggregate_service_amount': '綜合服務金額',
        'amount_involved_in_negotiation': '洽談涉及金額',
        'amount_of_actual_sales_on_site': '現場實際銷售總金額',
        'attendance': '出席人數',
        'attendance_two': '出席人數2',
        'booth_rental_of_macau_pavilion': '澳門館展位租金',
        'browse_the_total': '瀏覽總數',
        'business_relation': '建立新商務聯繫數量',
        'estimated_total_sales_amount': '預計銷售總金額',
        'exhibition_bidding': '展會招標',
        'flow_by_delegation': '代表團流量',
        'flow_satisfaction_score': '流動滿意度評分',
        'made_in_macao': '澳門製造澳門品牌',
        'number_by_delegation': '代表團數量',
        'number_of_enterprises': '展品涉及企業數量',
        'number_of_enterprises_to_negotiate': '洽談企業數量',
        'number_of_exhibitors': '參展商數量',
        'number_of_exhibits': '葡語國家食品展示中心展品數量',
        'number_of_product_code_sweeps': '產品掃碼總次數',
        'number_of_queries': '查詢數量',
        'number_of_questionnaires_returned': '回收問卷數量',
        'number_of_trade_delegations': '貿易代表團數量',
        'overall_cost_of_the_event': '活動總成本',
        'page_view': '頁面瀏覽量',
        'promotion_activity_name': '推廣活動名稱',
        'promotion_activity_name_two': '推廣活動名稱2',
        'reach_negotiation_sessions': '達成洽談會議數',
        'reach_negotiation_sessions_two': '達成洽談會議數2',
        'the_number_of_queries': '查詢次數',
        'the_service_amount': '服務金額',
        'total_number_of_queries': '查詢總數',
        'types_of_exhibitors': '參展企業類型',
        
        # 布爾字段
        'is_cluster': '是否集群',
        'display_amount': '展示數量',
        'display_area': '展示區域',
        'fair_type': '展會類型',
        'limited_enterprise': '限制企業',
        
        # 組織相關
        'coorganizer': '協辦單位',
        'coorganizer_en': '協辦單位(英文)',
        'organizer': '主辦單位',
        'organizer_en': '主辦單位(英文)',
        'sposor': '贊助單位',
        'sposor_en': '贊助單位(英文)',
        'support': '支持單位',
        'support_en': '支持單位(英文)',
        
        # 註冊人信息
        'reg_person_insitution': '註冊人機構',
        'reg_person_title': '註冊人職稱',
        
        # 活動狀態相關
        'active_state': '活動狀態',
        'agreement': '協議',
        'archivist': '檔案管理員',
        'cancel': '取消',
        'contact_email': '聯絡電郵',
        'contact_name': '聯絡人姓名',
        'contact_phone': '聯絡電話',
        'cover_date': '封面日期',
        'events': '活動事件',
        'file_form': '文件格式',
        'file_type_num': '文件類型編號',
        'follow_path': '跟進路徑',
        'fund_activity': '資助活動',
        'fund_scheme': '資助計劃',
        'hearten': '鼓勵',
        'industry': '行業',
        'inter_cert': '國際認證',
        'introduce': '介紹',
        'language': '語言',
        'local_icard': '本地身份證',
        'meeting_size': '會議規模',
        'onestepyear': '一站式年份',
        'reel': '捲軸',
        'region': '地區',
        'rest': '休息',
        'save_date': '保存日期',
        'save_full': '保存完整',
        'scale': '規模',
        'seat': '座位',
        'serial_number': '序列號',
        'sponsor': '贊助商',
        'sponsor_prope': '贊助商屬性',
        'state_upd_date': '狀態更新日期',
        'support_unit': '支持單位',
        'theme': '主題',
        'venue': '場地',
        'activity_line': '活動線',
        
        # 特殊字段
        'google_drive': '是否使用Google雲端硬碟',
        'entering_ipim': '是否進入IPIM',
        'entering_number': '進入編號',
        'entering_purpose': '進入目的',
        'enteringthecommunity': '是否進入社區',
        'followsituation': '跟進情況',
        'local_meeting': '本地會議',
        'localsp': '本地特殊項目',
        'exhibition_places': '是否展覽場所',
        'is_repeat': '是否重複',
        'local_mini': '本地迷你',
        'attending_en': '參與說明(英文)',
        'attendingpt': '參與說明(葡文)',
        'attending_zh': '參與說明(中文)',
        'is_boc_pay': '是否中銀支付',
        'associate_exhibition_id': '關聯展覽ID',
        'associate_exhibition_name': '關聯展覽名稱',
        'venue_detail': '場地詳情',
        'existq': '是否存在問卷',
        'iscepa': '是否CEPA',
        'is_invite': '是否邀請',
        'is_conference_messenger': '是否會議信使',

        # 添加一些常見字段的翻譯
        'receives': '接收內容',
        'create_by': '創建人',
        'create_time': '創建時間',
        'update_by': '更新人',
        'update_time': '更新時間',
        'bnum': '編號',
        'ftime': '時間',
        'sfkong': '是否空',
        'kaishi': '開始',
        'jieshu': '結束',
        'hezairen': '合作人',
        'jpnum': '票號',
        'shihelv': '適合率',
        'zhaiyao': '摘要',
        'num': '數量',
        'total': '總計',
        'his_lowest': '歷史最低',
        'his_average': '歷史平均',
        'his_highest': '歷史最高',
        'bx_jj_yongjin': '保險基金佣金',
        'bx_zx_money': '保險諮詢費用',
        'chengbao_gz_money': '承包工資',
        'bx_gg_moeny': '保險廣告費用',
        'tb_zx_money': '投保諮詢費用',
        'neikong_zx_money': '內控諮詢費用',
        'zhekoulv': '折扣率',
        'xiaoshoujine': '銷售金額',
        'beizhu': '備註',
        's_id': 'sID',
        'gname': '公司名稱',
        'gdata': '返利',
        'tdata': '備註',
        'didian': '地點',
        'hnum': '貨品編碼',
        'hname': '貨品名稱',
        'xinghao': '型號',
        'fahuocangku': '發貨倉庫',
        'danwei': '單位',
        'danjia': '單價',
        'monty': '月份',
        'main_income': '主要收入',
        'biz_income': '業務收入',
    }
    
    # 表名到繁體中文備註的映射
    table_comments = {
        'ex_activity': '展會活動表',
        'ex_activity_activity_conference_messenger': '活動會議信使關聯表',
        'ex_activity_activityline': '活動線路關聯表',
        'ex_activity_activityregion': '活動區域關聯表',
        'ex_activity_apply_type': '活動申請類型關聯表',
        'ex_activity_brief_appendix': '活動簡介附件表',
        'ex_activity_cancel': '活動取消表',
        'ex_activity_download_files': '活動下載文件表',
        'ex_activity_exhibit': '活動展品表',
        'ex_activity_exhibition_list': '活動展覽清單表',
        'ex_activity_exhibition_plan': '活動展覽計劃表',
        'ex_activity_expenses': '活動費用表',
        'ex_activity_follow': '活動跟進表',
        'ex_activity_follow_sour': '活動跟進來源表',
        'ex_activity_helper': '活動協助者表',
        'ex_activity_institution': '活動機構表',
        'ex_activity_liaison': '活動聯絡人表',
        'ex_activity_local_icardipim': '活動本地ICARDIPIM表',
        'ex_activity_local_provide_assist': '活動本地提供協助表',
        'ex_activity_online_files': '活動線上文件表',
        'ex_activity_organizer': '活動主辦方表',
        'ex_activity_pictures': '活動圖片表',
        'ex_activity_price_appendix': '活動價格附件表',
        'ex_activity_requirement_appendix': '活動需求附件表',
        'ex_activity_schedule': '活動時程表',
        'ex_activity_scope_appendix': '活動範圍附件表',
        'ex_activity_statement': '活動聲明表',
        'ex_activity_trade': '活動貿易表',
        'ex_activity_venue': '活動場地表',
        'ex_answer': '答案表',
        'ex_answer_2_item': '答案選項關聯表',
        'ex_apply_picture': '申請圖片表',
        'ex_apply_picture_image': '申請圖片圖像表',
        'ex_appother': '其他申請表',
        'ex_appother_accessory': '其他申請附件表',
    }
    
    return field_comments, table_comments

def process_sql_file(file_path):
    """處理SQL文件，添加缺少的備註"""
    field_comments, table_comments = add_chinese_comments()
    
    # 讀取文件
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 處理表備註
    def add_table_comment(match):
        table_def = match.group(0)
        table_name = match.group(1)
        
        # 如果已經有表備註，跳過
        if 'COMMENT =' in table_def:
            return table_def
        
        # 添加表備註
        if table_name in table_comments:
            comment = table_comments[table_name]
            # 在 ROW_FORMAT 前添加 COMMENT
            table_def = table_def.replace(
                'ROW_FORMAT = Dynamic;',
                f"COMMENT = '{comment}' ROW_FORMAT = Dynamic;"
            )
        
        return table_def
    
    # 處理字段備註
    def add_field_comment(match):
        field_def = match.group(0)
        field_name = match.group(1)
        
        # 如果已經有備註，跳過
        if 'COMMENT' in field_def:
            return field_def
        
        # 獲取備註
        comment = get_field_comment(field_name)
        if comment:
            # 在行末添加備註
            if field_def.rstrip().endswith(','):
                field_def = field_def.rstrip()[:-1] + f" COMMENT '{comment}',"
            else:
                field_def = field_def.rstrip() + f" COMMENT '{comment}'"
        
        return field_def
    
    def get_field_comment(field_name):
        """獲取字段備註"""
        # 處理ID字段
        if field_name.endswith('_id') and field_name != 'id':
            base_name = field_name[:-3]
            return f'{base_name}ID'
        
        # 直接匹配
        if field_name in field_comments:
            return field_comments[field_name]
        
        # 模糊匹配
        for key, value in field_comments.items():
            if key in field_name:
                return value
        
        return None
    
    # 使用正則表達式匹配CREATE TABLE語句並添加表備註
    table_pattern = r'CREATE TABLE `([^`]+)`[^;]+;'
    content = re.sub(table_pattern, add_table_comment, content, flags=re.MULTILINE | re.DOTALL)

    # 處理字段備註 - 逐行處理
    lines = content.split('\n')
    new_lines = []
    processed_count = 0

    for line in lines:
        # 匹配字段定義行，但排除已有COMMENT的行
        # 更精確的正則表達式來匹配字段定義
        field_match = re.match(r'^(\s*`([^`]+)`\s+[^,\n]*?)(\s*,?\s*)$', line.strip())

        if field_match and 'COMMENT' not in line and '`' in line and not line.strip().startswith('INDEX') and not line.strip().startswith('CONSTRAINT') and not line.strip().startswith('PRIMARY KEY') and not line.strip().startswith('UNIQUE INDEX'):
            field_line = field_match.group(1)
            field_name = field_match.group(2)
            trailing = field_match.group(3)

            # 獲取備註
            comment = get_field_comment(field_name)
            if comment:
                # 在字段定義後添加備註
                if ',' in trailing:
                    new_line = f"  {field_line} COMMENT '{comment}',"
                else:
                    new_line = f"  {field_line} COMMENT '{comment}'"
                new_lines.append(new_line)
                processed_count += 1
                print(f"添加備註到字段: {field_name} -> {comment}")
            else:
                print(f"未找到字段 '{field_name}' 的中文備註")
                new_lines.append(line)
        else:
            new_lines.append(line)

    content = '\n'.join(new_lines)
    print(f"總共處理了 {processed_count} 個字段")
    
    # 寫回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已完成對 {file_path} 的備註添加")

def check_remaining_fields(file_path):
    """檢查還有哪些字段沒有COMMENT"""
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    remaining_fields = []
    for line_num, line in enumerate(lines, 1):
        # 匹配字段定義行
        if re.match(r'^\s*`[^`]+`', line.strip()) and 'INDEX' not in line and 'CONSTRAINT' not in line and 'PRIMARY' not in line and 'UNIQUE' not in line and 'KEY' not in line:
            if 'COMMENT' not in line:
                remaining_fields.append((line_num, line.strip()))

    print(f"\n檢查結果:")
    if remaining_fields:
        print(f"還有 {len(remaining_fields)} 個字段沒有COMMENT:")
        for line_num, line in remaining_fields[:10]:  # 只顯示前10個
            print(f"  行 {line_num}: {line}")
    else:
        print("所有字段都已經有COMMENT了！")

if __name__ == "__main__":
    sql_file = "sql/exhibition_pros.sql"
    if os.path.exists(sql_file):
        process_sql_file(sql_file)
        check_remaining_fields(sql_file)
    else:
        print(f"文件 {sql_file} 不存在")
