package com.exhibition.domain.apply;

import com.exhibition.vo.apply.EncourageConventionActivityExpenditureVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Embeddable
public class EncourageConventionActivityExpenditure implements Serializable {



    //序
    @Column(length = 500)
    private String serial;
    //支出項目
    @Column(length = 500)
    private String expenditureItem;
    //詳細說明
    @Column(columnDefinition = "text")
    private String description;
    //金額(澳門元)
    @Column(length = 20)
    private String sum="0.00";
    //總金額：
    @Column(length = 20)
    private String totalSum="0.00";

    @PrePersist
    @PreUpdate
    void prePersistSaveOrUpdate() {
//        super.prePersist();
        if (sum == null || sum.trim().isEmpty()) {
            sum = "0.00";
        }
        if (totalSum == null || totalSum.trim().isEmpty()) {
            totalSum = "0.00";
        }
    }
    public EncourageConventionActivityExpenditure(EncourageConventionActivityExpenditureVO vo) {
        BeanUtils.copyProperties(vo,this);

    }

    public EncourageConventionActivityExpenditureVO toVO() {
        return toVO(false);
    }

    public EncourageConventionActivityExpenditureVO toVO(boolean includeLazy) {
        EncourageConventionActivityExpenditureVO vo = new EncourageConventionActivityExpenditureVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }
}
