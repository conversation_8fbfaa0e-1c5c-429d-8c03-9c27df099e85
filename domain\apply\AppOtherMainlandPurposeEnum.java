package com.exhibition.domain.apply;

import java.util.Arrays;

public enum AppOtherMainlandPurposeEnum {
    /**
     * 參與會展活動
     */
    PARTICIPATEEXHIBITION("參與會展活動"),

    OTHER("其他");


    private final String name;

    AppOtherMainlandPurposeEnum(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static AppOtherMainlandPurposeEnum getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }

}
