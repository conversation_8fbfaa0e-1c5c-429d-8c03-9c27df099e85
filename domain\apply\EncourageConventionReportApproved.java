package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.vo.apply.EncourageConventionApplyItemsVO;
import com.exhibition.vo.apply.EncourageConventionReportApprovedVO;
import com.exhibition.vo.apply.EncourageConventionReportExpenditureVO;
import com.exhibition.vo.apply.EncourageConventionReportSupportVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;

/*@Data
@NoArgsConstructor
@Embeddable*/
@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_convention_report_approved")
public class EncourageConventionReportApproved extends BaseEntity implements Serializable {
    private static final String[] IGNORE_PROPERTIES = new String[]{"encourageConventionReport"};

    //序
    @Column(length = 20)
    private String serial;
    //獲批准資助項目
    @Column(length = 50)
    private String   approvedItem;
    //詳細說明/計算基礎及方式
    @Column(columnDefinition = "text")
    private String  description;
    //服務供應實體
    @Column(length = 50)
    private String   serviceProvider;
    //收據編號
    @Column(length = 20)
    private String   receiptNo;
    //金額(澳門元)
    @Column(length = 20)
    private String  approvedSum;

    @ManyToOne(cascade=CascadeType.ALL)
    @JoinColumn(name = "encourage_convention_report_id", referencedColumnName = "id")
    private EncourageConventionReport encourageConventionReport;

    @ElementCollection // add by lwc
    @CollectionTable(name = "encourage_convention_report_approved_file")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> details;

    public EncourageConventionReportApproved(EncourageConventionReportApprovedVO vo) {
        BeanUtils.copyProperties(vo,this, IGNORE_PROPERTIES);

    }

    public EncourageConventionReportApproved(EncourageConventionReportExpenditureVO vo) {
        BeanUtils.copyProperties(vo,this, IGNORE_PROPERTIES);
    }

    public EncourageConventionReportApprovedVO toVO() {
        return toVO(false);
    }

    public EncourageConventionReportApprovedVO toVO(boolean includeLazy) {
        EncourageConventionReportApprovedVO vo = new EncourageConventionReportApprovedVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);
        return vo;
    }
}
