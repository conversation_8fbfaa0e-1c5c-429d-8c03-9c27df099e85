package com.exhibition.domain.apply;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Embeddable;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 其他政府機構支持申請
 * @date 2020-06-07
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Embeddable
public class GovSupport implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 政府機構名稱
     */
    private String            name;
    /**
     * 申請支持内容
     */
    private String            description;
}