package com.exhibition.domain.apply;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Embeddable;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 其他服務提供商
 * @date 2020-06-07
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Embeddable
public class ServiceProvider implements Serializable {
  
    private static final long serialVersionUID = 1L;
  
    /**
     * 服務提供商名稱
     */
    private String       name;
    /**
     * 服務内容
     */
    private String       description;
    /**
     * 實際獲支持總額
     */
    private Double       amount;
}