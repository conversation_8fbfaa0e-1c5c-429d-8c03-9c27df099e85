package com.exhibition.domain.apply;

import com.exhibition.vo.apply.EncourageConventionEstimatedScheduleVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@Embeddable
public class EncourageConventionEstimatedSchedule implements Serializable {

    //活動類別(會議/展覽/社區活動/特色或體驗活動/其他：請說明)
    @Column(length = 500)
    private String activityCategory;
    //舉辦日期
    @Temporal(TemporalType.TIMESTAMP)
    private Date holdDate;
    // 地點
    @Column(length = 500)
    private String Place;
    //時數
    @Column(length = 20)
    private String hours;

    public EncourageConventionEstimatedSchedule(EncourageConventionEstimatedScheduleVO vo) {
        BeanUtils.copyProperties(vo,this);

    }

    public EncourageConventionEstimatedScheduleVO toVO() {
        return toVO(false);
    }

    public EncourageConventionEstimatedScheduleVO toVO(boolean includeLazy) {
        EncourageConventionEstimatedScheduleVO vo = new EncourageConventionEstimatedScheduleVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }
}
