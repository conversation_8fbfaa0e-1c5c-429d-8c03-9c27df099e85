package com.exhibition.domain.thirdparty.FSS.model.entity;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.apply.EncourageCert;
import com.exhibition.domain.sys.Institution;
import com.exhibition.domain.thirdparty.DockingType;
import com.exhibition.domain.thirdparty.FSS.model.req.FinancialReq;
import com.exhibition.domain.thirdparty.FSS.model.req.FssReq;
import com.exhibition.domain.thirdparty.FSS.model.vo.FssVo;
import com.exhibition.util.FssUtils;
import com.exhibition.util.StringUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.jsoup.helper.StringUtil;
import org.springframework.beans.BeanUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/29 10:28
 * @describe: 社会保障局传递数据实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "macau_fss")
@BatchSize(size = 20)
public class FSS extends BaseEntity<FSS, FssVo> implements Serializable {

    private static final Long serialVersionUID = 164864548564L;

    /*用戶名稱 -- 對應的是機構名稱*/
    @ManyToOne(fetch = FetchType.EAGER)
    @JsonIgnore
    private Institution institution;
    /*部門名稱*/
    private String dept;
    /*財務納稅人編號*/
    @Column(columnDefinition = "VARCHAR(20) default ''")
    private String dsfNo;
    /*社保雇主注冊編號（必須是10為長度數字符號）*/
    @Column(columnDefinition = "VARCHAR(20) default ''")
    private String fssEmpNo;
    /*開始季度（YYYY,Q=1,2,3,4）*/
    private Integer strYQ;
    /*結束季度（YYYY,Q=1,2,3,4）*/
    private Integer endYQ;
    /*是否已經驗證*/
    @Column(columnDefinition = "tinyint")
    private Boolean isVerify;
    /*是否有繳納社保*/
    @Column(columnDefinition = "tinyint")
    private Boolean isPaySocialSecurity;
    /*接口請求回來的狀態碼*/
    private String statusCode;
    /*場所登記碼*/
    @Column(columnDefinition = "VARCHAR(50) default ''")
    private String locationRegistrationCode;
    /*營業情況*/
    @Column(columnDefinition = "VARCHAR(50) default ''")
    private String businessSituation;
    /*所得補充款*/
    @Column(columnDefinition = "VARCHAR(50) default ''")
    private String acquireSupplements;
    /*債務情況*/
    @Column(columnDefinition = "VARCHAR(50) default ''")
    private String debt;
    /**
     * 對接接口的類型
     */
    @Column(columnDefinition = "int(11) default 1")
    private DockingType type;
    /**
     * 請求追蹤識別碼，格式為32-bit UUID
     */
    private String traceId;
    /**
     * 營業稅檔案編號後綴
     */
    private String CadastroNumberAppendix;

    /**
     * accessToken
     */
    private String accessToken;
    /*账号*/
    private String account;

    /**
     * 与fss_id连接
     */
    @OneToOne(fetch = FetchType.LAZY,cascade = CascadeType.PERSIST)
    @JoinColumn(name = "financial_industrial_id", referencedColumnName = "id")
    private FinancialIndustrial industrial;

    @OneToOne(fetch = FetchType.LAZY,cascade = CascadeType.PERSIST)
    @JoinColumn(name = "financial_complementar_id", referencedColumnName = "id")
    private FinancialComplementarDate complementarDate;

    private String financialStatus;

    public FssReq toReq(){
        FssReq req = new FssReq();
        req.setUsername("IPIM01");
        BeanUtils.copyProperties(this,req);
        return req;
    }

    public FinancialReq toFinancialReq(){
        FinancialReq req = new FinancialReq();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss");
        req.setTraceId(traceId);
        req.setRequestData(new FinancialReq.RequestData(
                Integer.valueOf(StringUtils.isNotEmpty(dsfNo)?dsfNo:"000000"),
                Integer.valueOf(StringUtils.isNotEmpty(locationRegistrationCode)?locationRegistrationCode:"000000"),
                StringUtils.isNotEmpty(CadastroNumberAppendix)?CadastroNumberAppendix:""
        ));
        req.setRequester(new FinancialReq.Request(
                "IPIM",
                "IPIM",
                "IPIM"
        ));
        req.setRequestDateTime(simpleDateFormat.format(new DateTime()));
        return req;
    }

    public FssVo toFssVo(){
        FssVo req = new FssVo();
        BeanUtils.copyProperties(this,req);
        if (ObjectUtil.isNotEmpty(this.institution)){
            req.setInstitution(this.institution.toVO());
            req.setInstitutionId(this.institution.getId());
            req.setInstitutionName(this.institution.getNameZh());
        }
        return req;
    }


    public FSS toFssReq(FssVo req){
        BeanUtils.copyProperties(req,this);
        this.dept = "IPIM";
        this.endYQ = FssUtils.getFssEndYQ();
        this.strYQ = FssUtils.getFssStrYQ();
        this.setIsVerify(false);
        return this;
    }

    public FSS modify(FssVo req){
        BeanUtils.copyProperties(req,this);
        return this;
    }


}
