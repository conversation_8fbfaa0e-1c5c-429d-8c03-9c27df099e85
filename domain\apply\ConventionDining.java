package com.exhibition.domain.apply;

import com.exhibition.vo.apply.ConventionDiningVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: ConventionDining
 * @description: 餐飲或會議套餐
 * @author: ShiXin
 * @create: 2020-03-13 16:09
 **/
@Data
@NoArgsConstructor
@Embeddable
public class ConventionDining implements Serializable {
    /**
     * 預留日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date date;
    /**
     * 預計參加人數
     */
    private Integer attendees;
    /**
     * 預留場地
     */
    private String venue;
    /**
     * 人均消費
     */
    private Double averagePrice;
    /**
     * 實際餐飲總額
     */
    private Double totalPrice;

    public ConventionDining(ConventionDiningVO vo) {
        BeanUtils.copyProperties(vo,this);
    }

    public ConventionDiningVO toVO() {
        ConventionDiningVO v = new ConventionDiningVO();
        BeanUtils.copyProperties(this,v);
        return v;
    }

}
