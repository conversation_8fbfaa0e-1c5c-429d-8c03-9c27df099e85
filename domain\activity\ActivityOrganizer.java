package com.exhibition.domain.activity;

import com.exhibition.domain.apply.Encourage;
import com.exhibition.vo.activity.ActivityOrganizerVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;

/**
 * <AUTHOR>
 * @desc 活動主辦單位
 * @date 2020-02-23
 * @since 1.0.0
 */

@Data
@NoArgsConstructor
@Embeddable
@JsonView(Encourage.EncourageSimpleView.class)
public class ActivityOrganizer {
    /**
     * 展會活動的主辦單位
     */
    private String      name;

    public ActivityOrganizer(ActivityOrganizerVO vo) {
        BeanUtils.copyProperties(vo, this);
    }

    public ActivityOrganizerVO toVO() {
        return toVO(false);
    }

    public ActivityOrganizerVO toVO(boolean includeLazy) {
        ActivityOrganizerVO vo = new ActivityOrganizerVO();
        BeanUtils.copyProperties(this, vo);
        return vo;
    }
}
