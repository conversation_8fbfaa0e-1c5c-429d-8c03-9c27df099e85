package com.exhibition.domain.activity;

import com.exhibition.domain.apply.Encourage;
import com.exhibition.vo.activity.ActivityFollowVO;
import com.exhibition.vo.activity.ActivityVenueVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 展會活动地点
 * @date 2023年03月30日 14:57
 */
@Data
@NoArgsConstructor
@Embeddable
@JsonView(Encourage.EncourageSimpleView.class)
public class ActivityVenue implements Serializable {
    /**
     * 场地名称
     */
    private String name;
    /**
     * 租用面积
     */
    private String area;
    /**
     * 使用
     */
    private String usearea;

    public ActivityVenue(ActivityVenueVO vo) {

        BeanUtils.copyProperties(vo, this,"activity");
    }

    public ActivityVenueVO toVO() {
        return toVO(false);
    }
    public ActivityVenueVO toVO(boolean includeLazy) {
        ActivityVenueVO vo = new ActivityVenueVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }
}
