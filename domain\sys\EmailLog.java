package com.exhibition.domain.sys;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.vo.sys.EmailLogVo;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@NoArgsConstructor
@Entity
@Table(name = "email_log")
public class EmailLog extends BaseEntity {
    /**
     * 賬戶
     */
    private String account;
    /**
     * 參數
     */
    private String requestParam;
    /**
     * 操作说明
     */
    private String operDesc;

    /**
     * 类型
     */
    private String type;

    /**
     * 操作类型
     */
    private String operateType;

    public EmailLog(EmailLogVo emailLog) {
        BeanUtils.copyProperties(emailLog, this);
    }

    public EmailLogVo toVO() {
        return toVO(false);
    }

    public EmailLogVo toVO(boolean includeRole) {
        EmailLogVo vo = new EmailLogVo();
        vo.setCreateTime(this.getCreateAt());
        BeanUtils.copyProperties(this, vo,"startTime","endTime","updateTime");

        if (includeRole) {
        }
        return vo;
    }
}
