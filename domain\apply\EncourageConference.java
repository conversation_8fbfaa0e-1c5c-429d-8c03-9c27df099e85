package com.exhibition.domain.apply;

import cn.hutool.core.util.ObjectUtil;
import com.exhibition.domain.activity.Activity;
import com.exhibition.domain.activity.ActivityHelperType;
import com.exhibition.domain.sys.Institution;
import com.exhibition.domain.sys.Liaison;
import com.exhibition.domain.sys.User;
import com.exhibition.vo.apply.*;
import com.exhibition.vo.sys.LiaisonVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/27 16:27
 * @describe
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_conference")
@BatchSize(size = 20)
public class EncourageConference extends Encourage<EncourageConference, EncourageConferenceVO> implements Serializable {

    private static final long serialVersionUID = 5222788763879965175L;

    private static final String[] IGNORE_PROPERTIES = new String[]{
            "liaisonSub", "meetingDates", "activityDates", "conventionHistory", "openTimes", "activityJoins",
            "meetingRooms", "conventionDinings", "conventionVenueRentals", "exMeetingRooms", "conventionOtherGovs", "applyItems", "organizations"};

    /**
     * 联络人资料二
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private Liaison liaisonSub;
    /**
     * 1.5申請者身份分類
     */
    @Enumerated(EnumType.STRING)
    private ActivityHelperType applicantType;
    /**
     * 申請者身份其他說明
     */
    private String applicantTypeDescription;

    /**
     * 1.2 機構類型/企業類型
     */
    @Enumerated(EnumType.STRING)
    private InstitutionType institutionType;

    private String institutionTypeOther;

    //3.11 入住酒店資料(如適用)
    //預計入住澳門酒店
    @Column(columnDefinition = "tinyint default 0")
    private Boolean estimatedOccupancy;
    //酒店名稱  多选
    @ElementCollection
    private List<String> hotelName;
    // 预计住房数目
    @Column(length = 500)
    private String settleNum;
    // 預計住房數目
    //入住日期
    @Column(length = 10)
    private String checkInDate1;
    @Column(length = 10)
    private String checkInDate2;
    @Column(length = 10)
    private String checkInDate3;
    @Column(length = 10)
    private String checkInDate4;
    @Column(length = 10)
    private String checkInDate5;
    @Column(length = 10)
    private String checkInDate6;
    //房數
    private Integer roomsNumber1;
    private Integer roomsNumber2;
    private Integer roomsNumber3;
    private Integer roomsNumber4;
    private Integer roomsNumber5;
    private Integer roomsNumber6;
    //總房數
    private Integer roomsNumberTotal;

    //3.11 預計活動日程*請以附件形式提供詳細資料
    @ElementCollection
    @CollectionTable(name = "encourage_conference_estimated_schedule")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<EncourageConferenceEstimatedSchedule> estimatedSchedule;
    //3.7活动场地
    @ElementCollection
    @CollectionTable(name = "encourage_conference_activity_place")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<EncourageConferenceActivityPlace> activityPlaces;

    /**
     * 預計活動日程*請以附件形式提供詳細資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_conference_expected_schedule")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> expectedSchedule;

    //3.13 預計參與者資料*請以附件形式提供詳細資料
    /**
     * 預計參與者資料*請以附件形式提供詳細資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_conference_expected_participant")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> expectedParticipant;
    //3.10 其他活動資料
    //活動首屆舉辦日期
    @Temporal(TemporalType.TIMESTAMP)
    private Date firstEventStart;
    @Temporal(TemporalType.TIMESTAMP)
    private Date firstEventEnd;
    //活动地点
    private String firstActivityPlace;
    //活動第二屆舉辦日期
    @Temporal(TemporalType.TIMESTAMP)
    private Date secondlyEventStart;
    @Temporal(TemporalType.TIMESTAMP)
    private Date secondlyEventEnd;
    //活動第二屆活动地点
    private String secondlyActivityPlace;
    //屬“國際會議協會”(ICCA)認證的會議，請提供資料或證明文件
    @Column(columnDefinition = "tinyint default 0")
    private Boolean custBoolean2;
    @ElementCollection
    @CollectionTable(name = "encourage_conference_ICCA_files")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> iccaFiles;
    //屬重點產業相關的專業會議活動(如：中醫藥大健康、現代金融、高新技術、會展商貿和文化    體育)，請於附件提供資料。
    @Column(columnDefinition = "tinyint default 0")
    private Boolean custBoolean1;
    @ElementCollection
    @CollectionTable(name = "encourage_conference_professional_activity_files")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> professionalActivityFiles;
    //3.7 活動簡介
    @Column(columnDefinition = "text")
    private String activityIntroduction;
    @ElementCollection
    @CollectionTable(name = "encourage_conference_introduction_files")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> activityIntroductionFiles;
    //预计參與人數
    private Integer extPerson;
    //首选活动日期
    @Temporal(TemporalType.TIMESTAMP)
    private Date firstActivityStart;
    @Temporal(TemporalType.TIMESTAMP)
    private Date firstActivityEnd;
    //次选日期
    @Temporal(TemporalType.TIMESTAMP)
    private Date secondlyActivityStart;
    @Temporal(TemporalType.TIMESTAMP)
    private Date secondlyActivityEnd;
    //活动日期是否可变动
    @Column(columnDefinition = "tinyint default 0")
    private Boolean custBoolean3;
    /**
     * 會議的舉辦週期
     */
    @Enumerated(EnumType.STRING)
    private ActivityCycle meetingCycle;
    /**
     * 會議的舉辦週期其他(請說明)
     */
    private String meetingCycleDescription;
    /**
     * 3.3 活動類型
     */
    @ElementCollection
    @CollectionTable(name = "encourage_conference_meeting_enum")
    @Enumerated(EnumType.STRING)
    private List<EncourageConferenceMeetingEnum> meetingEnum;
    /*活动网址*/
    @Column(length = 500)
    private String activityWebsite;
    //3.1活動名稱
    private String activityName;
    private String activityNameEn;
    /**
     * 机构名称(中文)
     */
    @Column(length = 500)
    private String nameZh;
    /**
     * 机构名称(英文)
     */
    @Column(length = 500)
    private String nameEn;
    /**
     * 机构名称(葡文)
     */
    @Column(length = 500)
    private String namePt;
    /**
     * 場所登記
     */
    @Column(length = 500)
    private String siteRegistrationCode;
    /**
     * 納稅人編號 - 按財政局 M/1 所得補充稅收益申報書所載之資料填寫
     */
    @Column(length = 500)
    private String taxpayerNo;
    //->場所地址
    @Column(length = 500)
    private String streetZh;
    //->城市
    @Column(length = 500)
    private String cityZh;
    //>省份
    @Column(length = 500)
    private String provinceZh;
    //->國家/地區
    @Column(length = 500)
    private String countryZh;
    //->電話
    @Column(length = 50)
    private String tel;
    //->傳真
    @Column(length = 500)
    private String fax;
    //->電郵
    @Column(length = 500)
    private String email;
    //->網址
    @Column(length = 500)
    private String website;
    //FSP編號
    //(申請年份-FSP編號)
    private String fspno;
    /**
     * 補齊資料時間
     */
    private Date supplementinfoTime;

    public EncourageConferenceVO toVO(boolean includeLazy) {
        EncourageConferenceVO vo = new EncourageConferenceVO();
        copyProperties(this, vo, IGNORE_PROPERTIES);
        // add by ryan 1109
        if (includeLazy) {
            if (null != liaisonSub) {
                vo.setLiaisonSub(liaisonSub.toVO());
                vo.setLiaisonSubId(liaisonSub.getId());
                vo.setLiaisonSubName(liaisonSub.getNameZh());
            }
            if (!CollectionUtils.isEmpty(estimatedSchedule)) {
                vo.setEstimatedSchedule(estimatedSchedule.stream().map(EncourageConferenceEstimatedSchedule::toVO).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(activityPlaces)){
                vo.setActivityPlaces(activityPlaces.stream().map(EncourageConferenceActivityPlace::toVO).collect(Collectors.toList()));

            }
        }
        return vo;
    }

    @Override
    public EncourageConferenceVO toVO() {
        return toVO(false);
    }


    public EncourageConference(Encourage e){
        BeanUtils.copyProperties(e, this);
    }

    public EncourageConference(EncourageConferenceVO vo) {
        copyProperties(vo, this, IGNORE_PROPERTIES);
        Long liaisonSubId = vo.getLiaisonSubId();
        if (null != liaisonSubId) {
            Liaison liaisonSub = new Liaison();
            liaisonSub.setId(liaisonSubId);
            this.setLiaisonSub(liaisonSub);
        }
        if (!CollectionUtils.isEmpty(vo.getEstimatedSchedule())) {
            List<EncourageConferenceEstimatedSchedule> collect =
                    vo.getEstimatedSchedule().stream().map(EncourageConferenceEstimatedSchedule::new).collect(Collectors.toList());
            this.setEstimatedSchedule(collect);
        }
        if (!CollectionUtils.isEmpty(vo.getActivityPlaces())){
            List<EncourageConferenceActivityPlace> collect =
                    vo.getActivityPlaces().stream().map(EncourageConferenceActivityPlace::new).collect(Collectors.toList());
            this.setActivityPlaces(collect);
        }
    }

}
