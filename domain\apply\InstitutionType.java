package com.exhibition.domain.apply;

import java.util.Arrays;

public enum InstitutionType {
    /**
     * 個人
     */
    INDIVIDUAL("個人"),
    /**
     * 個人企業主
     */
    INDIVIDUAL_BUSINESS_OWNER("個人企業主"),
    /**
     * 個人企業
     */
    INDIVIDUAL_BUSINESS("個人企業"),
    /**
     * 有限公司持有之商戶
     */
    MERCHANTS_HELD_BY_A_LIMITED_COMPANY("有限公司持有之商戶"),
    /**
     * 有限公司
     */
    LIMITED_COMPANY("有限公司"),
    /**
     * 團體
     */
    GROUP("團體"),
    /**
     * 其他
     */
    OTHER("其他");

    private final String name;

    InstitutionType(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static InstitutionType getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }

}
