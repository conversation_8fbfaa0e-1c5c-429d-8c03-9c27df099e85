package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.activity.Activity;
import com.exhibition.domain.sys.*;
import com.exhibition.util.CopyUtils;
import com.exhibition.vo.apply.ParticipateCertVOs;
import com.exhibition.vo.apply.ParticipateDelegationUserVO;
import com.exhibition.vo.apply.ParticipateDelegationVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.formula.functions.T;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.CollectionUtils;

import javax.persistence.CollectionTable;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@Entity
@Table(name = "participate_delegation_user")
public class ParticipateDelegationUser extends BaseEntity implements Serializable{

    private static final String[] IGNORE_PROPERTIES =
            new String[]{"participateDelegation"};
    /**
     * id
     */
/*    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)*/
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 02展會实体
//     */
//    @ManyToOne(cascade = { CascadeType.ALL }, fetch = FetchType.LAZY)
//    @JoinColumn(name = "participateDelegation")

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(nullable = false)
    private ParticipateDelegation participateDelegation;
    /**
     * 姓名（中文）
     */
    private String  nameZh;
    /**
     * 姓名（英文）
     */
    private String  nameEn;

    /**
     * 性别，M-男，F-女
     */
    @Enumerated(value = EnumType.STRING)
    private Gender gender;
    /**
     * 出生日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthDate;
    /**
     * 證件名稱（入境）
     */
    private String inboundId;
    /**
     * 证号（入境）
     */
    private String  idCard;
    /**
     * 证有效期（入境）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date    idCardValidityDate;

    /**
     *證件正反面（入境）
     */
    @ElementCollection
    @CollectionTable(name = "participate_delegation_user_inbound")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> inboundFiles;

    /**
     *照片附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_delegation_user_persion")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> persionFiles;

    /**
     * 證件名稱（赴展）
     */
    private String inboundIdExhibition;
    /**
     * 证号（赴展）
     */
    private String  idCardExhibition;
    /**
     * 证有效期（赴展）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date    idCardValidityDateExhibition;

    /**
     *證件正反面（赴展）
     */
    @ElementCollection
    @CollectionTable(name = "participate_delegation_user_exhibition")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> inboundFilesExhibition;


    /**
     *参展申请照
     */
    @ElementCollection
    @CollectionTable(name = "participate_delegation_user_photoexhibition")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> photoFilesExhibition;

    /**
     *其它文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_delegation_user_otherfilesexhibition")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> otherFilesExhibition;

    /**
     * 是否商协会，0-否，1-是
     */
    private Boolean isAssociation;
    /**
     * 商协会名称
     */
    private String  associationName;
    /**
     * 会内职务
     */
    private String  associationPosition;
    /**
     * 商會行业类别
     */
    private String  associateIndustry;
    /**
     * 是否公司，0-否，1-是
     */
    private Boolean isCompany;
    /**
     * 公司名称
     */
    private String  companyName;
    /**
     * 公司地址
     */
    private String  companyAddress;
    /**
     * 公司职务
     */
    private String  companyPosition;
    /**
     * 公司商業類別
     */
    private String  companyIndustry;
    /**
     * 其他名义参与，0-否，1-是
     */
    private Boolean isOtherWay;
    /**
     * 其他名义名称
     */
    private String  otherWayName;
    /**
     * 随团去程，0-否，1-是
     */
    private Boolean goWithGroup;
    /**
     * 隨团回程，0-否，1-是
     */
    private Boolean backWithGroup;
    /**
     * 由本局安排，0-否，1-是
     */
    private Boolean arrangement;
    /**
     * 其他交通和住宿情况
     */
    private String  otherArrangement;

    public ParticipateDelegationUserVO toVO() {
        ParticipateDelegationUserVO vo = new ParticipateDelegationUserVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);
        return vo;
    }

}
