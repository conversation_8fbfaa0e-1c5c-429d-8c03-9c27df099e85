package com.exhibition.domain.thirdparty.FSS.model.res;

import com.exhibition.domain.thirdparty.FSS.model.entity.FinancialComplementarDate;
import com.exhibition.domain.thirdparty.FSS.model.entity.FinancialIndustrial;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/7 13:33
 * @describe 财政局接口
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class FinancialRes {
    private String traceId;
    private String responseDateTime;
    private Boolean success;
    private String errorRemark;
    private Data responseData;

    @AllArgsConstructor
    @NoArgsConstructor
    @ToString
    @Getter
    @Setter
    public static class Data{
        private FinancialIndustrial industrialData;
        private FinancialComplementarDate complementarData;
        private String financialStatus;
    }
}
