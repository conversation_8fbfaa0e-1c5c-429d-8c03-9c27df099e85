package com.exhibition.domain.roll;

import com.exhibition.vo.roll.AnswerVO;
import com.exhibition.vo.roll.QuestionItemVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 *结果
 *
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@Entity
@Table(name = "answer")
public class Answer implements Serializable {

    private static final String[] IGNORE_PROPERTIES = new String[]{"questionnaireAnswer","examineAnswer","question","items"};

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 所属展会问卷结果
     */
    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    private QuestionnaireAnswer questionnaireAnswer;

    /**
     * 所属普通问卷结果
     */
    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    private ExamineAnswer examineAnswer;

    /**
     * 题目
     */
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private Question question;

    /**
     * 选择题选项
     */
    @ManyToMany()
    @JoinTable(name = "answer_2_item", joinColumns = {@JoinColumn(name = "answer_id")},
            inverseJoinColumns = {@JoinColumn(name = "question_item_id")})
    @org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    private List<QuestionItem> items;

    /**
     * 其它项的输入内容
     */
    private String otherText;

    /**
     * 填空题结果
     */
    private String answer;


    public Answer(AnswerVO vo) {
        BeanUtils.copyProperties(vo, this, IGNORE_PROPERTIES);

        Long questionnaireAnswerId = vo.getQuestionnaireAnswerId();
        if (null != questionnaireAnswerId){
            QuestionnaireAnswer questionnaireAnswer = new QuestionnaireAnswer();
            questionnaireAnswer.setId(questionnaireAnswerId);
            this.setQuestionnaireAnswer(questionnaireAnswer);
        }

        Long examineAnswerId = vo.getExamineAnswerId();
        if (null != examineAnswerId){
            ExamineAnswer examineAnswer = new ExamineAnswer();
            examineAnswer.setId(examineAnswerId);
            this.setExamineAnswer(examineAnswer);
        }

        Long questionId = vo.getQuestionId();
        if (null != questionId){
            Question question = new Question();
            question.setId(questionId);
            this.setQuestion(question);
        }

        List<QuestionItemVO> items = vo.getItems();
        if (!CollectionUtils.isEmpty(items)){
            this.setItems(items.stream().map(QuestionItem::new).collect(Collectors.toList()));
        }
    }

    public AnswerVO toVO() {
        AnswerVO vo = new AnswerVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);


        if (null != questionnaireAnswer){
            vo.setQuestionnaireAnswerId(questionnaireAnswer.getId());
        }

        if (null != question){
            vo.setQuestionId(question.getId());
            vo.setQuestion(question.toVO());
        }

        if (!CollectionUtils.isEmpty(items)){
            vo.setItems(items.stream().map(QuestionItem::toVO).collect(Collectors.toList()));
        }
        return vo;
    }
}
