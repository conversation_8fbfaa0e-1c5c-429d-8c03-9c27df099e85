package com.exhibition.domain.apply;

import com.exhibition.vo.apply.EncourageConventionEventVenueVO;
import com.exhibition.vo.apply.EncourageConventionReportVenueVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Embeddable
public class EncourageConventionReportVenue implements Serializable {

    //場地名稱
    @Column(length = 30)
    private String venueName;

    //租用面積(每日平方米)
    @Column(length = 20)
    private String leasedArea;

    //實際使用面積(每日平方米)(會議/展覽及公共設施面積)
    @Column(length = 20)
    private String actualUseArea;

    public EncourageConventionReportVenue(EncourageConventionReportVenueVO vo) {
        BeanUtils.copyProperties(vo,this);

    }

    public EncourageConventionReportVenueVO toVO() {
        return toVO(false);
    }

    public EncourageConventionReportVenueVO toVO(boolean includeLazy) {
        EncourageConventionReportVenueVO vo = new EncourageConventionReportVenueVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }
}
