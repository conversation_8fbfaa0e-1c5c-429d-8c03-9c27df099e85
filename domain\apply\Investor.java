package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.boc.domain.BocOrder;
import com.exhibition.domain.activity.ActivityFollow;
import com.exhibition.domain.sys.Institution;
import com.exhibition.domain.sys.Status;
import com.exhibition.domain.sys.User;
import com.exhibition.vo.apply.EncourageVO;
import com.exhibition.vo.apply.InvestorVO;
import com.exhibition.vo.sys.InstitutionSimpleVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/9 9:19
 * @describe
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "investor")
@Inheritance(strategy = InheritanceType.JOINED)
public class Investor<T extends Investor, V extends InvestorVO>  extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -1356788546467746778L;

    /**
     * 设置视图
     */
    public interface InvestorSimpleView {
    }

    ;

    public interface GlobalSimpleView extends Investor.InvestorSimpleView {
    }

    ;

    private static final String[] IGNORE_PROPERTIES =
            new String[]{"institution", "follow"};
    //线上缴费编号
    @Column(length = 20)
    private String code;
    /**
     * 機構
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private Institution institution;
    /**
     * 注冊資本
     */
    private Double registerSum;
    /**
     * 錄入公司名稱
     */
    private String corporationName;
    /**
     * 需繳費用
     */
    private Integer payment;
    /**
     * 錄入日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date inputTime;
    /**
     * 跟進人員
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private User recordUser;
    /**
     * 狀態
     */
    @Enumerated(EnumType.STRING)
    private Status status;
    /**
     * 備注
     */
    private String remark;
    /**
     * 支付訂單號
     */
    private String bocPay;
    /**
     * 多語言
     */
    private String language;


    public Investor(InvestorVO vo) {
        BeanUtils.copyProperties(vo, this, IGNORE_PROPERTIES);
        if (null != vo.getInstitutionId()) {
            Institution institution = new Institution();
            institution.setId(vo.getInstitutionId());
            this.setInstitution(institution);
        }
        if (null != vo.getRecordUserId()) {
            User user = new User();
            user.setId(vo.getRecordUserId());
            this.setRecordUser(user);
        }
    }
    public InstitutionSimpleVO toInVO() {
        InstitutionSimpleVO i = new InstitutionSimpleVO();
        // 机构
        Institution institution = this.getInstitution();
        if (null != institution) {
            i = institution.toSimpleVO();
        }
        return i;
    }
    public InvestorVO toVO(){
        InvestorVO v = new InvestorVO();
        BeanUtils.copyProperties(this, v, IGNORE_PROPERTIES);
        Institution institution = this.getInstitution();
        if (null != institution) {
            v.setInstitutionId(institution.getId());
            v.setInstitutionName(institution.getNameZh());
            v.setInstitution(institution.toVO());
        }
        User applicant = this.getRecordUser();
        if (null != applicant) {
            v.setRecordUserId(applicant.getId());
            v.setRecordUserName(applicant.getName()+applicant.getLastName()+"("+applicant.getAccount()+")");
            v.setRecordUserAccount(applicant.getAccount());
        }
        return v;
    }
}
