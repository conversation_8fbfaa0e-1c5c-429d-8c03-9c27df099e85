package com.exhibition.domain.roll;

import com.exhibition.domain.activity.Activity;
import com.exhibition.domain.apply.ParticipateCert;
import com.exhibition.domain.record.BlackRank;
import com.exhibition.domain.sys.Institution;
import com.exhibition.domain.sys.User;
import com.exhibition.vo.roll.AnswerVO;
import com.exhibition.vo.roll.QuestionItemVO;
import com.exhibition.vo.roll.RemindVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 *结果
 *
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@Entity
@Table(name = "remind")
public class Remind implements Serializable {

    private static final String[] IGNORE_PROPERTIES = new String[]{"questionnaireAnswer","examineAnswer","question","items"};

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 所属机构
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private Institution institution;
    /**
     * 所属机构
     */
    @ManyToOne( fetch = FetchType.LAZY)
    private Activity activity;
    /**
     * 記錄人
     */
    private String     recordPerson;

    /**
     * 詳情
     */
    @Column(columnDefinition = "text")
    private String     recordDetails;
    /**
     * 標題
     */
    @Column(columnDefinition = "text")
    private String     recordTitle;

    /**
     * 情況說明
     */
    @Column(columnDefinition = "text")
    private String     recordMemo;

    /**
     * 記錄時間
     */
    private Date recordTime;//
    /**
     * 狀態（未上呈, 上呈中, 已完成)
     */
    @Column(length = 10)
    private String isDeal;

    /**
     * 記錄人員
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private User recordUser;
    /**
     * 提醒人員
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private User remindUser;


    /**
     * 違規記錄類型（01、02、033、034）
     */
    private String     btype;
    /**
     * 02生效日期
     */
    private Date inureDate;
    /**
     * 跟進同事
     */
    @ElementCollection
    private List<String>  inureRemind;


    public Remind(RemindVO vo) {
        BeanUtils.copyProperties(vo, this, IGNORE_PROPERTIES);
        if (null != vo.getActivityId()) {
            Activity activity = new Activity();
            activity.setId(vo.getActivityId());
            this.setActivity(activity);
        }
        if (null != vo.getInstitutionId()) {
            Institution institution=new Institution();
            institution.setId(vo.getInstitutionId());
            this.setInstitution(institution);
        }

        if (null != vo.getRecordUserId()) {
            User applicant = new User();
            applicant.setId(vo.getRecordUserId());
            this.setRecordUser(applicant);
        }
        if (null != vo.getRemindUserId()) {
            User remind = new User();
            remind.setId(vo.getRemindUserId());
            this.setRemindUser(remind);
        }

    }

    public RemindVO toVO() {
        RemindVO vo = new RemindVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);
        if (null != vo.getActivityId()) {
            Activity activity = new Activity();
            activity.setId(vo.getActivityId());
            this.setActivity(activity);
        }
        if (null != vo.getInstitutionId()) {
            Institution institution=new Institution();
            institution.setId(vo.getInstitutionId());
            this.setInstitution(institution);
        }
        Activity activity = this.getActivity();
        if (null != activity) {
            vo.setActivityId(activity.getId());
            vo.setActivityName(activity.getNameZh());
            vo.setActivity(activity.toSimpleVO());
        }
        // 机构
        Institution institution = this.getInstitution();
        if (null != institution) {
            vo.setInstitutionId(institution.getId());
            vo.setInstutionName(institution.getNameZh());
            vo.setInstitution(institution.toSimpleVO());
        }

        //記錄人員
        User applicant = this.getRecordUser();
        if (null != applicant) {
            vo.setRecordUserId(applicant.getId());
            vo.setRecordUserName(applicant.getName()+applicant.getLastName()+"("+applicant.getAccount()+")");
            vo.setRecordUserAccount(applicant.getAccount());
        }
        //提醒人員
        User remind = this.getRemindUser();
        if (null != remind) {
            vo.setRemindUserId(remind.getId());
            vo.setRemindUserName(remind.getName()+remind.getLastName()+"("+remind.getAccount()+")");
            vo.setRemindUserAccount(remind.getAccount());
        }
        return vo;
    }
}
