package com.exhibition.domain.member;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.apply.Encourage;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.BatchSize;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Data
@NoArgsConstructor
@Entity
@Table(name = "member_integral")
@BatchSize(size = 20)
@JsonView(Encourage.EncourageSimpleView.class)
public class Integral extends BaseEntity {
    /**
     * 积分
     */
    private Integer integral;

    /**
     * 等级
     */
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private MemberOrder mberOrder;
}
