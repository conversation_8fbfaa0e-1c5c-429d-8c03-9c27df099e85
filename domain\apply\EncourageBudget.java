package com.exhibition.domain.apply;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Embeddable;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 預算
 * @date 2020-06-09
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Embeddable
public class EncourageBudget implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = -6061407637471819440L;
    /**
     * 支出項目名稱
     */
    private String                name;
    /**
     * 服務提供者名稱
     */
    private String                providerName;
    /**
     * 單價
     */
    private Double                price;
    /**
     * 數量
     */
    private Integer               amount;
    /**
     * 項目金額
     */
    private Double                totalPrice;
}