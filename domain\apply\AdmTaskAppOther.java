package com.exhibition.domain.apply;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @desc
 * @date 2020-03-03
 * @since 1.0.0
 */

@Data
@NoArgsConstructor
@Entity
@DiscriminatorValue(Flowtpl.APPOTHER)
public class AdmTaskAppOther extends AdmTask {


    @ManyToOne(optional = true, cascade = { CascadeType.ALL }, fetch = FetchType.LAZY)
    private AppOther          appother;
}
