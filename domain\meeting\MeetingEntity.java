package com.exhibition.domain.meeting;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class MeetingEntity {
    private String bventId="b64c0000-e8bb-62e6-7d26-08d9caa7e538";

    @JSONField(name="AttendeeTypeId")
    private String AttendeeTypeId="b64c0000-e8bb-62e6-8182-08d9cc079097";

    @JSONField(name="Attendee")
    private Attendee Attendee;

    @JSONField(name="Items")
    private List<Map<String,Object>> Items;
}
