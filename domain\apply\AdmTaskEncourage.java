package com.exhibition.domain.apply;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.CascadeType;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 * <AUTHOR>
 * @desc
 * @date 2020-03-03
 * @since 1.0.0
 */

@Data
@NoArgsConstructor
@Entity
@DiscriminatorValue(Flowtpl.ENCOURAGE)
public class AdmTaskEncourage extends AdmTask {


    @ManyToOne(optional = true, cascade = { CascadeType.ALL }, fetch = FetchType.LAZY)
    private Encourage           encourage;
}
