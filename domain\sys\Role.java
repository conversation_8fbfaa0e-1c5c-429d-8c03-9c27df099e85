package com.exhibition.domain.sys;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.apply.Encourage;
import com.exhibition.vo.sys.RoleVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@Entity
@Table(name = "role")
@BatchSize(size = 20)
@JsonView(Encourage.EncourageSimpleView.class)
public class Role extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -8069536927898616524L;
    /**
     * 复核者
     */
    public static final String CHECKER = "checker";
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /**
     * 角色代码
     */
    @Column(nullable = false, unique = true, length = 100)
    private String code;
    /**
     * 角色名称
     */
    @Column(nullable = false)
    private String name;
    /**
     * 角色说明
     */
    @Column(name = "_desc")
    private String desc;

    /**
     * 賦予角色權限
     */
    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "role_2_purview", joinColumns = {@JoinColumn(name = "roles_id")},
            inverseJoinColumns = {@JoinColumn(name = "purviews_id")})
    @org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    private List<Purview> purviews;


    public Role(RoleVO role) {
        BeanUtils.copyProperties(role, this);
    }

    public RoleVO toVO() {
        return toVO(false);
    }

    public RoleVO toVO(boolean includeLazy) {
        RoleVO role = new RoleVO();

        if (includeLazy){
            if (!CollectionUtils.isEmpty(purviews)){
                role.setCodes(purviews.stream().map(p -> p.getCode()).collect(Collectors.toList()));
            }else {
                role.setCodes(Collections.emptyList());
            }
        }

        //formPurview(this);

        BeanUtils.copyProperties(this, role,"purviews");

        if (includeLazy) {
            if (!CollectionUtils.isEmpty(purviews)){
                role.setPurviews(purviews.stream().map(purview -> purview.toVO(true)).collect(Collectors.toList()));
            }else {
                role.setPurviews(Collections.emptyList());
            }
        }

        return role;
    }


    /**
     * 获取角色的树状权限
     * @param role
     */
    public void formPurview(Role role) {
        List<Purview> distinctPurview = role.getPurviews();
        //父级权限
        if (!CollectionUtils.isEmpty(distinctPurview)){
            List<Purview> parentPurview = distinctPurview.stream()
                    .filter(p -> null == p.getParent())
                    .peek(c -> c.setChild(Collections.emptyList()))
                    .collect(Collectors.toList());
            //子级权限
            List<Purview> childPurview = distinctPurview.stream().filter(c -> null != c.getParent()).collect(Collectors.toList());

            //赋值拥有的子级权限
            List<Purview> havePurview = parentPurview.stream()
                    .map(p -> {
                        List<Purview> child = childPurview.stream()
                                .filter(c -> null != c.getParent() && Long.compare(p.getId(), c.getParent().getId()) == 0)
                                .collect(Collectors.toList());
                        p.setChild(child);
                        return p;
                    }).collect(Collectors.toList());
            //权限路由排序
            sortPurview(havePurview);
            for (Purview purview : havePurview) {
                List<Purview> child = purview.getChild();
                if (!CollectionUtils.isEmpty(child)){
                    sortPurview(child);
                }
            }
           if (!CollectionUtils.isEmpty(havePurview)){
               role.setPurviews(havePurview);
           }else {
               role.setPurviews(Collections.emptyList());
           }
        }else {
            role.setPurviews(Collections.emptyList());
        }
    }

    /**
     * 权限根据id升序
     * @param havePurview
     */
    private void sortPurview(List<Purview> havePurview) {
        Collections.sort(havePurview, new Comparator<Purview>() {
            @Override
            public int compare(Purview o1, Purview o2) {
                int result = o1.getId() > o2.getId() ? 1 : -1 ;
                return result;
            }
        });
    }

    @Override
    public String toString() {
        return "Role{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", desc='" + desc + '\'' +
//                ", purviews=" + purviews +
                '}';
    }
}
