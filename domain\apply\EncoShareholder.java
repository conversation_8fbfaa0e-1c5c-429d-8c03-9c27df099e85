package com.exhibition.domain.apply;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Embeddable;

/**
 * @desc 股东信息
 */
@Data
@NoArgsConstructor
@Embeddable
public class EncoShareholder {

    /**
     * 支出項目名稱
     */
    private String                name;
    /**
     * 身份證號碼/公司納稅代碼
     */
    private String                taxCodeOrID;
    /**
     * 控股比例
     */
    private Double                holdingPercent;
    /**
     * 公司任職
     */
    private String               jobTitle;
}