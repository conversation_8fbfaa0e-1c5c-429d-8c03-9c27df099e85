package com.exhibition.domain.sys;

import com.exhibition.domain.apply.Encourage;
import com.exhibition.exception.RenException;
import com.fasterxml.jackson.annotation.JsonView;

import java.util.Arrays;

@JsonView(Encourage.EncourageSimpleView.class)
public enum Onlineoroffline {
    /**
     * 線上
     */
    online("線上"),
    /**
     * 線下
     */
    offline("線下"),
    /**
     * 線上線下
     */
    line("線上線下");

    private final String name;

    Onlineoroffline(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static Onlineoroffline getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }

    public static Onlineoroffline getEnumFromString(String string) {
        if (string != null) {
            try {
                return Enum.valueOf(Onlineoroffline.class, string.trim());
            } catch (IllegalArgumentException e) {
                throw new RenException(e.getMessage());
            }
        }
        return null;
    }
}
