package com.exhibition.domain.sys;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.sys
 * @ClassName: SerializeNumber
 * @description: 審批流水號
 * @author: ShiXin
 * @create: 2020-03-17 18:04
 **/
@Data
@NoArgsConstructor
@Entity
@Table(name = "serialize_number")
public class SerializeNumber implements Serializable {

    private static final long serialVersionUID = -2516208816452291499L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 流水key
     */
    @Column(name = "key_", nullable = false, unique = true, length = 100)
    private String key;

    /**
     * 流水号
     */
    private int number;

}
