package com.exhibition.domain.thirdparty.FSS.model.res;

import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/29 13:38
 * @describe 接受类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class FssRes {
    private String requestId;
    private String message;
    private Boolean success;
    private Integer StatusCode;
    private Data data;

    @Setter
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString
    public static class Data{
        private String username;
        private String dept;
        private String dsfNo;
        private String fssEmpNo;
        private Integer strYQ;
        private Integer endYQ;
        private String fssEmpChnName;
        private String fssEmpName;
        private String locationNo;
        private String contrib;
    }
}
