package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.activity.Activity;
import com.exhibition.domain.activity.ActivityScope;
import com.exhibition.domain.sys.Institution;
import com.exhibition.domain.sys.Liaison;
import com.exhibition.domain.sys.Status;
import com.exhibition.domain.sys.User;
import com.exhibition.vo.apply.ApplyPictureVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.BatchSize;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "apply_picture")
public class ApplyPicture extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -7619390990661871854L;

    private static final String[] IGNORE_PROPERTIES =
            new String[]{"activity", "institution", "liaison", "applicant", "images", "tasks"};

    /**
     * 展會
     */
    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    private Activity                activity;
    /**
     * 機構
     */
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private Institution             institution;
    /**
     * 機構聯絡人
     */
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private Liaison                 liaison;
    /**
     * 发起人
     */
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private User                    applicant;
    /**
     * 申请日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date                    applyTime;
    /**
     * 審核狀態
     */
    @Enumerated(EnumType.STRING)
    private Status                  status;
    /**
     * 申请的图片数
     */
    private Integer                 imageNum;
    /**
     * 申請圖片
     */
    @OneToMany(mappedBy = "applyPicture", fetch = FetchType.LAZY, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @BatchSize(size = 20)
    private List<ApplyPictureImage> images;
    /**
     * 審批操作記錄
     */
    @OneToMany(mappedBy = "picture", fetch = FetchType.LAZY)
    private List<AdmTaskPicture>    tasks;
    /**
     * 参展的展會範圍
     */
    @Enumerated(EnumType.STRING)
    private ActivityScope           participateScope;
    /**
     * 参展记录id
     */
    private Long                    participateId;
    /**
     * 当前审批人id
     */
    private Long                    currentApproverId;


    public ApplyPicture(ApplyPictureVO v) {
        BeanUtils.copyProperties(v, this, IGNORE_PROPERTIES);

        if (null != v.getActivityId()) {
            Activity activity = new Activity();
            activity.setId(v.getActivityId());
            this.setActivity(activity);
        }

        if (null != v.getInstitutionId()) {
            Institution institution = new Institution();
            institution.setId(v.getInstitutionId());
            this.setInstitution(institution);
        }

        if (null != v.getApplicantId()) {
            User applicant = new User();
            applicant.setId(v.getApplicantId());
            this.setApplicant(applicant);
        }

        if (null != v.getLiaisonId()) {
            Liaison liaison = new Liaison();
            liaison.setId(v.getLiaisonId());
            this.setLiaison(liaison);
        }

        if (!CollectionUtils.isEmpty(v.getImages())) {
            images = v.getImages().stream().map(ApplyPictureImage::new).peek(i -> i.setApplyPicture(this)).collect(Collectors.toList());
        }
    }

    public ApplyPictureVO toVO() {
        return toVO(false);
    }

    public ApplyPictureVO toVO(boolean includeLazy) {
        ApplyPictureVO vo = new ApplyPictureVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);

        if (null != activity) {
            vo.setActivityId(activity.getId());
            vo.setActivity(activity.toVO());
        }

        if (null != institution) {
            vo.setInstitutionId(institution.getId());
            vo.setInstitution(institution.toVO());
        }

        if (null != liaison) {
            vo.setLiaisonId(liaison.getId());
            vo.setLiaison(liaison.toVO());
        }

        if (null != applicant) {
            vo.setApplicantId(applicant.getId());
            vo.setApplicantName(applicant.getName());
        }

        //審批流
        List<AdmTaskPicture> tasks = this.getTasks();
        if (null != tasks) {
            vo.setTasks(tasks.stream().map(AdmTask::toVO).collect(Collectors.toList()));
            // 当前审批节点
            vo.setCurrentTask(vo.getTasks().stream()
                    .filter(t -> t.getStatus() == Status.approving)
                    .findFirst().orElse(null));
        }

        if (includeLazy) {

            //申请的图片
            if (!CollectionUtils.isEmpty(images)) {
                vo.setImages(images.stream().map(ApplyPictureImage::toVO).collect(Collectors.toList()));
            }
        }

        return vo;
    }

}
