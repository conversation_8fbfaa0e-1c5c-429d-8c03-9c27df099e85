package com.exhibition.domain.roll;

import com.exhibition.domain.activity.ActivityStatus;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/8/9 17:44
 * @describe 新闻类型
 */
public enum NewsType {

    LATEST_INFORMATION("最新咨詢"),

    EXHIBITION_YEAR("展會年歷"),

    LEISURE_INFORMATION("休閑咨詢"),

    ENTERPRISE_PAIRING("企業配對"),

    ELECTRIC_NEWSPAPERS("電子刊物"),

    EXCLUSIVE_OFFERS("專屬優惠"),

    HOMEPAGE("主頁");

    private final String name;

    NewsType( String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static NewsType getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
