package com.exhibition.domain.activity;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @desc  展会类别枚举
 * @date 2020-03-14
 * @since 1.0.0
 */
public enum ActivityTrade {
    /**
     * 一般會議
     */


    GENERAL_MEET("會議"),
    /**
     * 國際性會議
     */
    INT_MEET("國際性會議"),
    /**
     * 展覽會
     */
    EXPO("展覽會"),
    /**
     * 展銷會
     */
    SELL("展銷會"),

    /**
     * 國際性展覽會
     */
    INT_EXHIBIT("國際性展覽會"),
    /**
     * 專業展銷會
     */
    CSFAE("專業展銷會"),
    /**
     * 路演
     */
    ROADSHOW("路演"),
    /**
     * 一般展覽
     */
    GENERAL_EXHIBIT("一般展覽"),
    /**
     * 專業展覽
     */
    SPECIALTY_EXHIBIT("專業展覽"),
    /**
     * 會議及展覽
     */
    MEET_EXHIBIT("會議及展覽"),
    /**
     * 保薦現職雇員參與課程及考試
     */
    EXAM("保薦現職雇員參與課程及考試"),
    /**
     * 籌辦課程
     */
    COURSE("籌辦課程"),
    /**
     * 線上展
     */
    ONLINE_EXHIBIT("線上展"),
    ONLINE_MEET("線上會議"),
    TRAIN("培訓"),
    /**
     * 其他
     */
    OTHER("其他");

    private final String name;

    ActivityTrade( String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ActivityTrade getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
