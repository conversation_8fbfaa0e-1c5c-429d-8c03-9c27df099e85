package com.exhibition.domain.member;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.apply.Encourage;
import com.exhibition.domain.sys.IntegralType;
import com.exhibition.domain.sys.User;
import com.exhibition.vo.member.IntegralRecordVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Where;
import org.springframework.beans.BeanUtils;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Entity
@Table(name = "integral_record")
@BatchSize(size = 20)
@JsonView(Encourage.EncourageSimpleView.class)
@Where(clause = "is_delete != 1")
public class IntegralRecord extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @ManyToOne(fetch = FetchType.LAZY)
    private User user;
    //积分
    private Integer integral;
    //创建人
    private String createName;
    //备注
    private String remark;
    //积分类型
    @Enumerated(EnumType.STRING)
    private IntegralType integralType;
    //积分规则
    @ManyToOne(fetch = FetchType.LAZY,cascade = {CascadeType.REMOVE})
    private Member member;

    //删除
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isDelete=false;

    public IntegralRecord(IntegralRecordVO vo) {
        BeanUtils.copyProperties(vo, this);
        if (vo.getUserId() != null) {
            User user=new User();
            user.setId(vo.getUserId());
            this.setUser(user);
        }
        if (vo.getMemberId() != null) {
            Member member = new Member();
            member.setId(vo.getMemberId());
            this.setMember(member);
        }
    }

    public IntegralRecordVO toVO() {
        IntegralRecordVO vo = new IntegralRecordVO();
        if (user != null) {
            vo.setUser(user.toVO());
        }
        BeanUtils.copyProperties(this, vo);
        return vo;
    }
}
