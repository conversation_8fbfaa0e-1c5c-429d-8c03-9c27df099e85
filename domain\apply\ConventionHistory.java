package com.exhibition.domain.apply;

import com.exhibition.vo.apply.ConventionHistoryVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: ConventionHistory
 * @description: 會議
 * @author: ShiXin
 * @create: 2020-03-11 16:07
 **/
@Data
@NoArgsConstructor
@Embeddable
public class ConventionHistory implements Serializable {

    private static final long serialVersionUID = -69387188889760781L;
    /**
     * 日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date date;
    /**
     * 地點
     */
    private String venue;

    public ConventionHistory(ConventionHistoryVO vo) {
        BeanUtils.copyProperties(vo,this);
    }

    public ConventionHistoryVO toVO() {
        ConventionHistoryVO v = new ConventionHistoryVO();
        BeanUtils.copyProperties(this,v);
        return v;
    }
}
