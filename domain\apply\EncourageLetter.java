package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.sys.User;
import com.exhibition.vo.apply.EncourageLetterVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;

import javax.persistence.CascadeType;
import javax.persistence.CollectionTable;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 活動組織架構
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_letter")
public class EncourageLetter extends BaseEntity implements Serializable {
    private static final String[] IGNORE_PROPERTIES = new String[]{"encourageConvention","user"};

    /**
     * 上傳時間
     */
    private String type;

    /**
     * 上傳時間
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date uploadTime;

    /**
     * 查看時間
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date openTime;

    /**
     * 上傳人
     */
    @OneToOne(fetch = FetchType.LAZY)
    private User user;

    @ManyToOne(cascade= CascadeType.ALL)
    @JoinColumn(name = "encourage_id", referencedColumnName = "id")
    private Encourage           encourage;

    /**
     * 回函附件
     */
    @ElementCollection
    @CollectionTable(name = "encourage_letter_file")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> letterFiles;

    public EncourageLetter(EncourageLetterVO vo) {
        BeanUtils.copyProperties(vo, this,IGNORE_PROPERTIES);
        if (null != vo.getUserId()) {
            User u = new User();
            u.setId(vo.getUserId());
            this.setUser(u);
        }
    }

    public EncourageLetterVO toVO(boolean includeLazy) {
        EncourageLetterVO vo = new EncourageLetterVO();
        BeanUtils.copyProperties(this, vo,IGNORE_PROPERTIES);
        User u = this.user;
        if (u != null) {
            vo.setUserVO(u.toVO());
            vo.setUserId(u.getId());
        }
//        if (this.encourage != null) {
//            vo.setEncourageId(this.encourage.getId());
//        }
        return vo;
    }

    public EncourageLetterVO toVO() {

        return toVO(false);
    }

}
