package com.exhibition.domain.sys;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @desc log標題拼接字段
 * @date 2020-04-23
 * @since 1.0.0
 */
public enum LogTitle {
    /**
     * 查看
     */
    LOOK("查看"),
    /**
     * 列表
     */
    PAGE("列表"),
    /**
     * 詳情
     */
    DETAIL("詳情"),
    /**
     * 新增
     */
    ADD("新增"),
    /**
     * 修改
     */
    UPDATE("修改"),
    /**
     * 刪除
     */
    DELETE("刪除"),
    /**
     * 申請
     */
    APPLY("申請"),
    /**
     * 審批
     */
    APPROVE("審批"),
    /**
     * 操作
     */
    OPERATE("操作"),
    /**
     * 導入
     */
    IMPORT("導入"),
    /**
     * 修改密碼
     */
    RESET("修改密碼"),
    /**
     * 忘記密碼
     */
    FORGET("忘記密碼"),
    /**
     * 查看本局主辦之展會審批列表
     */
    LOCALPAGE("查看本局主辦之展會審批列表"),
    /**
     * 查看本局組織之境外展會審批列表
     */
    OUTLANDSPAGE("查看本局組織之境外展會審批列表"),
    /**
     * 打印回函
     */
    PRINT("打印回函"),
    /**
     * 本局主辦之展會申請記錄
     */
    LOCAL_IMPORT("本局主辦之展會申請記錄"),
    /**
     * 本局組織之境外參展申請記錄（022境外活動-參展）
     */
    ATTEND_IMPORT("本局組織之境外參展申請記錄"),
    /**
     * 本局組織之境外參加代表團申請記錄（021參加代表團）
     */
    DELEGATION_IMPORT("本局組織之境外參加代表團申請記錄"),
    /**
     * 申請記錄
     */
    ATTEND_RECORD("申請記錄"),
    /**
     * 過往記錄
     */
    CONTRACT_RECORD("過往記錄"),
    /**
     * 商業配對
     */
    BM_RECORD("商業配對"),
    /**
     * 031非牟利社團赴境外展會設置展位
     */
    ENCOURAGE_ATTEND("非牟利社團赴境外展會設置展位"),
    /**
     * 032非牟利社團組織代表團參與境外展會
     */
    ENCOURAGE_MISSION("非牟利社團組織代表團參與境外展會"),
    /**
     * 033企業參與本地或境外展會
     */
    ENCOURAGE_ENTERPRISE("企業參與本地或境外展會"),
    /**
     * 034會議及展覽資質計劃
     */
    ENCOURAGE_CONVENTION("會議及展覽資質計劃");

    private final String name;

    LogTitle( String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static LogTitle getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
