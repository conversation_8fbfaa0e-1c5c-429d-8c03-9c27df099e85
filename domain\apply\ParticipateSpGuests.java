package com.exhibition.domain.apply;

import com.exhibition.vo.apply.ParticipateSpGuestsVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Embeddable
public class ParticipateSpGuests implements Serializable {

    //姓名
    private String name;
    //職位
    private String position;
    //機構
    private String institutionName;
    //國籍
    private String nationality;

    public ParticipateSpGuests(ParticipateSpGuestsVO vo) {
        BeanUtils.copyProperties(vo,this);

    }

    public ParticipateSpGuestsVO toVO() {
        return toVO(false);
    }

    public ParticipateSpGuestsVO toVO(boolean includeLazy) {
        ParticipateSpGuestsVO vo = new ParticipateSpGuestsVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }
}
