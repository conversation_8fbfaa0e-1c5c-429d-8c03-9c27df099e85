package com.exhibition.domain.roll;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.vo.roll.AnswerVO;
import com.exhibition.vo.roll.ExamineAnswerVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.BatchSize;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "examine_answer")
public class ExamineAnswer extends BaseEntity implements Serializable {

    private static final String[] IGNORE_PROPERTIES = new String[]{"examine", "answers"};

    /**
     * 提交时间
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date submitTime;

    /**
     * 展会问卷
     */
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private Examine examine;

    /**
     * 题目结果
     */
    @OneToMany(mappedBy = "examineAnswer", fetch = FetchType.LAZY, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @BatchSize(size = 20)
    private List<Answer> answers;

    public ExamineAnswer(ExamineAnswerVO v) {
        BeanUtils.copyProperties(v, this,IGNORE_PROPERTIES);

        Long examineId = v.getExamineId();
        if (null != examineId) {
            Examine examine = new Examine();
            examine.setId(examineId);
            this.setExamine(examine);
        }

        List<AnswerVO> answers = v.getAnswers();
        if (!CollectionUtils.isEmpty(answers)) {
            List<Answer> collect = answers.stream().map(Answer::new)
                    .peek(a -> a.setExamineAnswer(this))
                    .collect(Collectors.toList());

            this.setAnswers(collect);
        } else {
            this.setAnswers(Collections.emptyList());
        }
    }

    public ExamineAnswerVO toVO() {
      return toVO(false);
    }
    public ExamineAnswerVO toVO(boolean includeLazy) {
        ExamineAnswerVO vo = new ExamineAnswerVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);

        if (null != examine) {
            vo.setExamineName(examine.getName());
        }

        if (includeLazy) {

            if (null != examine) {
                vo.setExamine(examine.toSimpleVO());
            }

            if (!CollectionUtils.isEmpty(answers)) {
                vo.setAnswers(answers.stream().map(Answer::toVO).collect(Collectors.toList()));
            }
        }

        return vo;
    }
}
