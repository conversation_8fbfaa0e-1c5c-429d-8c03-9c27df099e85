package com.exhibition.domain.apply;

import com.exhibition.exception.RenException;
import com.fasterxml.jackson.annotation.JsonView;

import java.util.Arrays;

@JsonView(Encourage.EncourageSimpleView.class)
public enum ParticipatingSp {

    /**
     * 機構的未認證狀態
     */
    ac("參會"),
    /**
     * 待定
     */
    sp("S&P"),

    localMini("中小企業"),
    /**
     * 簽注函
     */
    endorsement("簽注函"),
    /**
     * 簽注函
     */
    meetingGroup("代表團住宿"),

    other("其它");

    private final String name;

    ParticipatingSp(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ParticipatingSp getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }

    public static ParticipatingSp getEnumFromString(String string) {
        if (string != null) {
            try {
                return Enum.valueOf(ParticipatingSp.class, string.trim());
            } catch (IllegalArgumentException e) {
                throw new RenException(e.getMessage());
            }
        }
        return null;
    }
}
