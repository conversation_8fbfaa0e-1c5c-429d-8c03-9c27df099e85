package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.sys.Institution;
import com.exhibition.domain.sys.Status;
import com.exhibition.domain.sys.User;
import com.exhibition.vo.apply.AdmTaskSimpleVO;
import com.exhibition.vo.apply.AdmTaskVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Where;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.DiscriminatorColumn;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.Inheritance;
import javax.persistence.InheritanceType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @desc
 * @date 2020-03-03
 * @since 1.0.0
 */

@Data
@NoArgsConstructor
@Entity
@Table(name = "task")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type")
@Where(clause = "status != 'invalid'")
public class AdmTask extends BaseEntity implements Serializable {
    private static final long    serialVersionUID = 8189862237211664565L;
    /**
     * 流程模版
     */
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(updatable = false)
    private              Flowtpl flowtpl;
    /**
     * 审批編號
     */
    private              String  code;
    /**
     * 审批标题
     */
    private              String  title;
    /**
     * 类型;本局主办之展会审批:ACTIVITY,本局组织之境外展会审批:DEPUTATION,支持及鼓励措施审批:ENCOURAGE,资助申请；FUNDING，机构认证审批：INSTITUTION，展会图片审批：PICTURE，支持及鼓励措施展后报告：DEPUTATION_REPORT
     */
    @Column(nullable = false, insertable = false, updatable = false)
    private              String  type;
    /**
     * 业务id
     */
    @Column(nullable = false, updatable = false)
    private              Long    entityId;
    /**
     * 审批顺序（步骤）
     */
    @Column(nullable = false, updatable = false)
    private              Integer step;
    /**
     * 审批人
     */
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private              User    approver;
    /**
     * 审批时间
     */
    @Temporal(TemporalType.TIMESTAMP)
    private              Date    approveTime;
    /**
     * 审批意见
     */
    @Column(columnDefinition = "text")
    private              String  opinion;
    /**
     * 状态
     */
    @Enumerated(EnumType.STRING)
    private              Status  status;

    /**
     * 機構
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private Institution institution;

    /**
     * 0102
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private Participate participate;

    /**
     * 033
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private Encourage encourage;
    /**
     * 035
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private AppOther appOther;

    @Column(columnDefinition = "tinyint default 0")
    private Boolean choose;

    /**
     * 層級
     * @param vo
     */
    private int tier;


    public AdmTask(AdmTaskVO vo) {
        BeanUtils.copyProperties(vo, this, "approver");
        if (null != vo.getApproverId()) {
            User user = new User();
            user.setId(vo.getApplicantId());
            this.setApprover(user);
        }
    }


    public AdmTaskVO toVO() {
        AdmTaskVO vo = new AdmTaskVO();
        BeanUtils.copyProperties(this, vo, "approver");
        if (null != approver) {
            vo.setApproverId(approver.getId());
            vo.setApproverName(approver.getName());
            vo.setApproverEmail(approver.getEmail());
        }
        return vo;
    }

    public AdmTaskSimpleVO toSimpleVO() {
        AdmTaskSimpleVO vo = new AdmTaskSimpleVO();
        BeanUtils.copyProperties(this, vo, "approver", "flowtpl");
        if (null != approver) {
            vo.setApproverId(approver.getId());
            vo.setApproverName(approver.getName());
            vo.setApproverEmail(approver.getEmail());
        }
        return vo;
    }
}
