package com.exhibition.domain.sys;

import com.exhibition.domain.apply.Encourage;
import com.exhibition.exception.RenException;
import com.fasterxml.jackson.annotation.JsonView;

import java.util.Arrays;

@JsonView(Encourage.EncourageSimpleView.class)
public enum Status {

    /**
     * 機構的未認證狀態
     */
    uncertified("機構的未認證狀態"),
    /**
     * 待定
     */
    pending("待定"),
    /**
     * 審批中
     */
    approving("處理中"),
    /**
     * 獲批
     * 
     */
    passed("獲批"),
    /**
     * 已完成
     */
    finish("已完成"),
    /**
     * 不獲批
     */
    rejected("不獲批"),
    /**
     * 作廢
     */
    invalid("作廢"),
    /**
     * 取消申請(不计申请次数）
     */
    withdraw("取消申請（不計申請次數）"),
    /**
     * 取消申請
     */
    withdrawCalc("取消申請（計算申請次數）"),
    /**
     * 補齊資料
     */
    supplementinfo("需補充資料"),

    /**
     * 上傳申請表
     */
    uploadform("上傳申請表"),

    /**
     * 已上傳
     */
    editbyplatinum("已上傳"),

    /**
     * 待提交
     */
    tosubmit("待提交"),

    /**
     * 資料庫端查詢條件（查詢除了待提交）
     */
    condition("資料庫端查詢條件"),
    /**
     * 資料庫端查詢條件（查詢除了取消不計次數）
     */
    Validcondition("查询是否重复状态"),

    /**
     * 待付款
     */
    obligation("待付款"),

    /**
     * 書面聽證(預審)
     */
    inquiry("書面聽證(預審)"),
    /**
     * 書面聽證(結算)
     */
    settlement("已結算（支票）"),
    /**
     * 已結算
     */
    clsd("已結算"),
    /**
     * 已付款
     */
    alreadypaid("已結算（轉賬）"),
    settledpaid("已結算（待支付）"),
    /**
     * 接納申請
     */
    fill("接納申請"),
    /**
     * 駁回聲明異議 (預審)
     */
    reapprove("駁回聲明異議(預審)"),
    /**
     * 取消鼓勵
     */
    cannelMoney("取消鼓勵"),
    /**
     * 書面聽證 (預審)
     */
    preapprove("書面聽證(預審)"),
    /**
     * 書面聽證 (結算)
     */
    preSettlement("書面聽證(結算)"),
    /**
     * 駁回聲明異議 (結算)
     */
    reSettlement("駁回聲明異議(結算)"),
    /**
     * 結算中
     */
    settlementing("結算中"),
    /**
     * 需補充資料 (結算)
     */
    setsupplementinfo("需補充資料 (結算)"),
    /**
     * 同步中
     */
    Syncing("同步中"),
    /**
     * 同步中
     */
    Synced("已同步"),
    /**
     * 同步失敗
     */
    SynchronizationFailed("同步失敗"),
    /**
     * 駁回聲明異議 (結算)
     */
    setrejected("申請不獲接納(結算)"),
    /**
     * 逾期取消
     */
    overdue("逾期取消");


    private final String name;

    Status(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static Status getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }

    public static Status getEnumFromString(String string) {
        if (string != null) {
            try {
                return Enum.valueOf(Status.class, string.trim());
            } catch (IllegalArgumentException e) {
                throw new RenException(e.getMessage());
            }
        }
        return null;
    }
}
