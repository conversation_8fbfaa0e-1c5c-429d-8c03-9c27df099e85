package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.sys.User;
import com.exhibition.vo.apply.EncourageEnterpriseActivityReceiptVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/9/3 11:44
 * @describe 033活动报告收据
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_enterprise_activity_receipt")
public class EncourageEnterpriseActivityReceipt extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 618151878864931826L;
    private static final String[] IGNORE_PROPERTIES =
            new String[]{ "encourageEnterprise"};
    /*
    活動報告表(正本)
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile1;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe1;
    /*
    * (本地)照片-至少4張 (包含：參展場館出、入口、現場及展位)
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile2;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe2;
    /*
    * (境外)照片-至少10張(包含：參展場館出、入口、現場及展位)
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile3;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe3;
    /*第二部分：*/
    /*
    * 展位租金/線上參展費用收據
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile4;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe4;
    /*
    * 展位製作/線上虛擬展位設計收據
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile5;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe5;
    /*
    * 大會場刊/網站廣告費收據
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile6;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe6;
    /*
    * 產品或樣板運輸費收據：
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile7;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe7;
    /*
    *  參展人員往來澳門交通費用：
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile8;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe8;
    /*
    * A)機票(登機証、支付證明(如:匯款單/月結單/支付截圖)
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile81;
    /*
    * B)車票/船票 (收據正本 / 相關證明文件)
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile82;
    /*第三部分：*/
    //製作印刷品費用收據
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile9;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe9;
    //o	完成品2份
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile91;
    /*-----*/
    /*
    * 製作宣傳片費用收據
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile10;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe10;
    /*
    * 完電子宣傳板版面截圖等
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile101;
    /*--第三排---*/
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe11;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile11;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile111;
    /*
    * 第四排
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile12;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe12;
    private String other1;
    /*
    * 第五排
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile13;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe13;
    private String other2;
    /**
     * 記錄日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date recordTime;
    private String                   memo;
    /**
     * 管理员
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private User recordUser;
    /**
     * 展會实体
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "encourage_enterprise_id", referencedColumnName = "id")
    private EncourageEnterprise encourageEnterprise;

    public EncourageEnterpriseActivityReceipt(EncourageEnterpriseActivityReceiptVO v) {
        BeanUtils.copyProperties(v, this, IGNORE_PROPERTIES);
        if (v.getEncourageEnterpriseId() != null) {
            EncourageEnterprise ee = new EncourageEnterprise();
            ee.setId(v.getEncourageEnterpriseId());
            this.setEncourageEnterprise(ee);
        }
        if (null != v.getRecordUserId()) {
            User applicant = new User();
            applicant.setId(v.getRecordUserId());
            this.setRecordUser(applicant);
        }
    }

    public EncourageEnterpriseActivityReceiptVO toVO() {
        EncourageEnterpriseActivityReceiptVO vo = new EncourageEnterpriseActivityReceiptVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);

        // 展後報告
        EncourageEnterprise encourageEnterprise = this.getEncourageEnterprise();
        if (null != encourageEnterprise) {
            vo.setEncourageEnterpriseVO(encourageEnterprise.toVO());
            vo.setEncourageEnterpriseId(encourageEnterprise.getId());
        }
        //发起人
        User recordUser = this.getRecordUser();
        if (null != recordUser) {
            vo.setRecordUserId(recordUser.getId());
            vo.setRecordUserName(recordUser.getName()+recordUser.getLastName()+"("+recordUser.getAccount()+")");
            vo.setRecordUserAccount(recordUser.getAccount());
        }
        return vo;
    }
}
