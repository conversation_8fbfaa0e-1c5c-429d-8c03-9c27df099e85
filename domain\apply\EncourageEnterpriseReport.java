package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.sys.Status;
import com.exhibition.vo.apply.EncourageEnterpriseReportVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;

import javax.persistence.CollectionTable;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 033企業參與本地或境外展會展後報告
 * @date 2020-06-05
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_enterprise_report")
public class EncourageEnterpriseReport extends BaseEntity implements Serializable {
  
    private static final long serialVersionUID = 618151878864931826L;
  
    private static final String[] IGNORE_PROPERTIES =
            new String[]{"tasks", "encourageEnterprise"};

    /**
     * 展位租金
     */
    private Double              exhibitRent;

    /**
     * 大會場刊廣告費
     */
    private Double              catalogueCost;
    /**
     * 大會網站廣告費
     */
    private Double              websiteCost;
    /**
     * 產品運輸費
     */
    private Double              productFreight;

    /**
     * 展會製作費
     */
    private Double              makeCost;
    /**
     * 交通費
     */
    private Double              trafficCost;
    /**
     * 印刷宣傳品費用
     */
    private Double              leafletCost;
    /**
     * 相關綫下宣傳廣告費用
     */
    private Double              advertCost;
    /**
     * 縂申請金額
     */
    private Double              totalAmount;
    /**
     * 建议
     */
    private String              advice;
     /**
     * 声明
     */
    private Boolean             stateAgree;
    /**
     * 展會及展位相片
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_photo")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> photoFiles;
    /**
     * 審核狀態
     */
    @Enumerated(EnumType.STRING)
    private Status              status;
    /**
     * 申请日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date                   applyTime;
    /**
     * 審批操作記錄
     */
    @OneToMany(mappedBy = "encourageEnterpriseReport", fetch = FetchType.LAZY)
    private List<AdmTaskEncourageEnterpriseReport> tasks;


    /**
     * 涉及的政府部門、機構的資助
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_report_government")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<Government> governments;

    /**
     * 預期展會收入
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_report_revenue")
    @Fetch(FetchMode.SELECT)
    private List<SourceRevenue> revenues;
    /**
     * 預期展會支出
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_report_expenses")
    @Fetch(FetchMode.SELECT)
    private List<SourceRevenue> expenses;
    /**
     * 洽談場次
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_report_bmtalks")
    @Fetch(FetchMode.SELECT)
    private List<SourceRevenue> bmTalks;
    /**
     * 上傳附件
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_report_accessoryfiles")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> accessoryFiles;
    //配對及洽談場次
    private Double bmCounts;
    //達成交易宗數
    private Double saleCounts;

    //展會類別
    private String exhiCategory;
    /**4、於其他政府、機構就參與《參與展覽資助計劃》涉及資助項目申請或受惠資助*/
    /**
     * 是否有涉及其他政府部門、機構的申請
     */
    private Boolean haveGovernment;

    /**
     * 請提交商業洽談或現場銷售的詳細紀錄，以及銷售單據副本
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_report_bmtalkfiles")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> bmtalkFiles;
    /**
     * 請提交由電子平台營辦商提供之活動進行期間的訪客／下載量的地域分析數據
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_report_emcommefiles")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> emcommeFiles;

    /**
     * 展會实体
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "encourage_enterprise_id", referencedColumnName = "id")
    private EncourageEnterprise encourageEnterprise;

    public EncourageEnterpriseReport(EncourageEnterpriseReportVO v) {
        BeanUtils.copyProperties(v, this, IGNORE_PROPERTIES);
        if (v.getEncourageEnterpriseId() != null) {
            EncourageEnterprise ee = new EncourageEnterprise();
            ee.setId(v.getEncourageEnterpriseId());
            this.setEncourageEnterprise(ee);
        }
    }

    public EncourageEnterpriseReportVO toVO() {
        EncourageEnterpriseReportVO vo = new EncourageEnterpriseReportVO();
        if (this.encourageEnterprise != null) {
            vo.setTaxpayerType(this.encourageEnterprise.getTaxpayerType());
        }
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);
        if (vo.getTasks() == null && this.getTasks() != null) {
            vo.setTasks(this.getTasks().stream().map(AdmTask::toSimpleVO).collect(Collectors.toList()));
        }
        // 展後報告
        EncourageEnterprise encourageEnterprise = this.getEncourageEnterprise();
        if (null != encourageEnterprise) {
            vo.setEncourageEnterpriseVO(encourageEnterprise.toVO());
            vo.setEncourageEnterpriseId(encourageEnterprise.getId());
        }

        // 展後報告
//        EncourageEnterprise encourageEnterprise = this.getEncourageEnterprise();
//        if (null != encourageEnterprise) {
//            vo.setEncourageEnterpriseVO(encourageEnterprise.toVO());
//            vo.setEncourageEnterpriseId(encourageEnterprise.getId());
//        }
        return vo;
    }

}