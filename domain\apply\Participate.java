package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.boc.trade.model.req.BocCashierReq;
import com.exhibition.domain.activity.Activity;
import com.exhibition.domain.activity.ActivityApplyType;
import com.exhibition.domain.activity.ActivityParticipationType;
import com.exhibition.domain.sys.Institution;
import com.exhibition.domain.sys.Liaison;
import com.exhibition.domain.sys.Onlineoroffline;
import com.exhibition.domain.sys.SourceType;
import com.exhibition.domain.sys.Status;
import com.exhibition.domain.sys.User;
import com.exhibition.util.CopyUtils;
import com.exhibition.vo.apply.ParticipateVO;
import com.exhibition.vo.apply.participateConventionDateQueryVO;
import com.exhibition.vo.apply.participateDateQueryVO;
import com.exhibition.vo.sys.InstitutionSimpleVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 申請參展
 * @date 2020-02-25
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "participate")
@Inheritance(strategy = InheritanceType.JOINED)
@JsonView(Encourage.EncourageSimpleView.class)
public class Participate<T extends Participate, V extends ParticipateVO> extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -2953701377129093362L;

    private static final String[] IGNORE_PROPERTIES =
            new String[]{"activity", "institution", "liaison", "applicant", "tasks", "liaison"};
    //tag
    private String tag;

    /**
     * 參加類型
     */
    @Column(columnDefinition = "text")
    @Enumerated(EnumType.STRING)
    private ActivityParticipationType participationType;
    /**
     * 申請編號
     */
    private String code;
    /**
     * 展会申请类型
     * MIECF("澳門國際環保合作發展論壇及展覽 -MIECF"),
     * IICF("國際基礎設施投資與建設高峰論墰 -IIICF"),
     * MFE("澳門國際品牌連鎖加盟展 -MFE"),
     * GMBPF("粵澳名優商品展 -GMBPF"),
     * MIF("澳門國際貿易展覽會 -MIF"),
     * PLPEX("葡語國家產品及服務展 -PLPEX"),
     * PARTICIPATE("參展"),
     * MISSION("參加代表團");
     */
    @Enumerated(EnumType.STRING)
    private ActivityApplyType type;
    /**
     * 展會
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private Activity activity;
    /**
     * 機構
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private Institution institution;
    /**
     * 組團機構
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private Institution institutionCluster;
    /**
     * 機構聯絡人
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private Liaison liaison;
    /**
     * 直接与本人联系
     */
    private Boolean contactMe;
    /**
     * 发起人
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private User applicant;
    /**
     * 申请日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date applyTime;
    /**
     * 審核狀態
     */
    @Enumerated(EnumType.STRING)
    private Status status;
    /**
     * 審批操作記錄
     */
    @OneToMany(mappedBy = "participate", fetch = FetchType.LAZY)
    private List<AdmTaskParticipate> tasks;
    /**
     * 图片申请id
     */
    private Long applyPictureId;
    /**
     * 图片申请状态
     */
    @Enumerated(EnumType.STRING)
    private Status applyPictureStatus;
    /**
     * 展会问卷结果id
     */
    private Long questionnaireAnswerId;
    /**
     * 參展方式
     */
    @Enumerated(value = EnumType.STRING)
    private ParticipateMethod method;
    /**
     * 当前审批人id
     */
    private Long currentApproverId;

    /**
     * 记录类型
     */
    @Enumerated(EnumType.STRING)
    private RecordType recordType;
    /**
     * 記錄參會費用
     */
    private String exhibitionCost;

    /**
     * 總的獲批金额
     */
    private Double approvedAmount;
    /**
     * 總的實際金额
     */
    private Double actualAmount;
    /**
     * 申请金额
     */
    private Double applyAmount;

    /**
     * 来源類型
     */
    @Enumerated(EnumType.STRING)
    private SourceType source;

    /**
     * 02部分收遞資料
     */
    /**
     * 收遞附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_accessory")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> accessoryFiles;

    /**
     * 收遞編號
     */
    private String putNo;

    /**
     * 豁免費用
     */
    private Boolean savingCost;

    /**
     * 線上或者線下
     */
    @Enumerated(EnumType.STRING)
    private Onlineoroffline onlineoroffline;

    /**
     * 申請類別及展位類別
     */
    private String applicationCategory;

    /**
     * 展館名稱
     */
    private String pavilionName;
    /**
     * 組織單位
     */
    private String organizationUnit;

    /**
     * 参会&SP
     */
    @Enumerated(EnumType.STRING)
    private ParticipatingSp participatingSp;
    /**
     * 支付狀態判斷
     */
    private String bocPay;
    /**
     * 備註
     */
    @Column(columnDefinition = "text")
    private String memo;
    /**
     * 参团者姓名
     */
    private String participant;

    /**
     * 多語言
     */
    @Column(columnDefinition = "varchar(20) default 'zh'")
    private String language;


    public Participate(Long id, String code,Double applyAmount, ActivityApplyType type, Activity activity, Institution institution,Institution institutionCluster,Liaison liaison, User applicant, Date applyTime, Status status, Long applyPictureId,
                       Status applyPictureStatus, Long questionnaireAnswerId, ParticipateMethod method, Long currentApproverId,
                       RecordType recordType, Onlineoroffline onlineoroffline,String applicationCategory,  String pavilionName,
                       String organizationUnit, ParticipatingSp participatingSp,String bocPay,String memo,String participant) { // add by lwc , ParticipatingSp participatingSp
        this.setId(id);
        this.code = code;
        this.applyAmount=applyAmount;
        this.type = type;
        this.activity = activity;
        this.institution = institution;
        this.institutionCluster=institutionCluster;
        this.liaison = liaison;
        this.applicant = applicant;
        this.applyTime = applyTime;
        this.status = status;
        this.applyPictureId = applyPictureId;
        this.applyPictureStatus = applyPictureStatus;
        this.questionnaireAnswerId = questionnaireAnswerId;
        this.method = method;
        this.currentApproverId = currentApproverId;
        this.recordType = recordType;
        this.onlineoroffline = onlineoroffline;
        this.applicationCategory=applicationCategory;
        this.pavilionName = pavilionName;
        this.organizationUnit = organizationUnit;
        this.participatingSp = participatingSp; // add by lwc
        this.bocPay=bocPay;
        this.memo=memo;
        this.participant = participant;
    }

    public Participate(ParticipateVO v) {
        BeanUtils.copyProperties(v, this, IGNORE_PROPERTIES);
        if (null != v.getActivityId()) {
            Activity activity = new Activity();
            activity.setId(v.getActivityId());
            this.setActivity(activity);
        }

        if (null != v.getInstitutionId()) {
            Institution institution = new Institution();
            institution.setId(v.getInstitutionId());
            this.setInstitution(institution);
        }
        if (null != v.getInstitutionClusterId()) {
            Institution institution = new Institution();
            institution.setId(v.getInstitutionClusterId());
            this.setInstitutionCluster(institution);
        }
        if (null != v.getApplicantId()) {
            User applicant = new User();
            applicant.setId(v.getApplicantId());
            this.setApplicant(applicant);
        }

        if (null != v.getLiaisonId()) {
            Liaison liaison = new Liaison();
            liaison.setId(v.getLiaisonId());
            this.setLiaison(liaison);
        }

    }

    public participateConventionDateQueryVO datecVO() {
        participateConventionDateQueryVO d = new participateConventionDateQueryVO();
        User applicant = this.getApplicant();
        if (this.getInstitution() != null) {
            if (this.getInstitution().getNameZh() != null) {
                d.setInstitutionName(this.getInstitution().getNameZh());
            }
        }
        if (applicant != null) {
            d.setUserConventionVO(applicant.tocVO());
        }
        return d;
    }

    public participateDateQueryVO dateVO() {
        participateDateQueryVO d = new participateDateQueryVO();
        User applicant = this.getApplicant();
        d.setActivityName(this.getActivity().getNameZh());
        d.setApplyTime(this.getApplyTime());
        d.setCode(this.getCode());
        if (this.getInstitution() != null) {
            if (this.getInstitution().getCountryZh() != null) {
                d.setCountryZh(this.getInstitution().getCountryZh());
            }
            if (this.getInstitution().getNameZh() != null) {
                d.setInstitutionName(this.getInstitution().getNameZh());
            }
        }
        if (this.getLiaison() != null) {
            if (this.getLiaison().getEmail() != null) {
                d.setEmail(this.getLiaison().getEmail());
            }
            if (this.getLiaison().getNameEnOrPt() != null) {
                d.setNameEnOrPt(this.getLiaison().getNameEnOrPt());
            }
            if (this.getLiaison().getPhone() != null) {
                d.setPhone(this.getLiaison().getPhone());
            }
            if (this.getLiaison().getNameZh() != null) {
                d.setNameZh(this.getLiaison().getNameZh());
            }
        }
        if (null != applicant) {
            d.setSource(applicant.getSource());
            d.setSign(applicant.getSign());
            d.setId(applicant.getId());
        }
        return d;
    }



    public ParticipateVO toVO() {
        ParticipateVO v = new ParticipateVO();
        BeanUtils.copyProperties(this, v, IGNORE_PROPERTIES);
        // 展会
        Activity activity = this.getActivity();
        if (null != activity) {
            v.setActivityId(activity.getId());
            v.setActivityName(activity.getNameZh());
            v.setActivityScope(activity.getActivityScope());
            v.setActivity(activity.toSimpleVO());
        }
        // 机构
        Institution institution = this.getInstitution();
        if (null != institution) {
            v.setInstitution(institution.toSimpleVO());
            v.setInstitutionId(institution.getId());
            v.setInstitutionName(institution.getNameZh());
        }
        // 组团机构
        Institution institutionCluster = this.getInstitutionCluster();
        if (null != institutionCluster) {
            v.setInstitutionCluster(institutionCluster.toSimpleVO());
            v.setInstitutionClusterId(institutionCluster.getId());
            v.setInstitutionClusterName(institutionCluster.getNameZh());
        }

        //審批流
        List<AdmTaskParticipate> tasks = this.getTasks();
        if (null != tasks) {
            v.setTasks(tasks.stream().map(AdmTask::toVO).collect(Collectors.toList()));
            // 当前审批节点
            v.setCurrentTask(v.getTasks().stream()
                    .filter(t -> t.getStatus() == Status.approving)
                    .findFirst().orElse(null));
        }

        // 发起人
        User applicant = this.getApplicant();
        if (null != applicant) {
            v.setApplicantId(applicant.getId());
            v.setApplicantName(applicant.getName()+applicant.getLastName()+"("+applicant.getAccount()+")");
            v.setApplicantAccount(applicant.getAccount());
            v.setApplicantPhone(applicant.getPhone());
        }
        // 联络人
        Liaison liaison = this.getLiaison();
        if (null != liaison) {
            v.setLiaisonId(liaison.getId());
            v.setLiaison(liaison.toVO());
        }
        return v;
    }

    public InstitutionSimpleVO toInVO() {
        InstitutionSimpleVO i = new InstitutionSimpleVO();
        // 机构
        Institution institution = this.getInstitution();
        if (null != institution) {
            i = institution.toSimpleVO();
        }
        return i;
    }

    protected void copyProperties(T t, V v, String... ignoreProperties) {
        if (ignoreProperties.length == 0) {
            BeanUtils.copyProperties(t, v, IGNORE_PROPERTIES);
        } else {
            String[] ignores = CopyUtils.concat(IGNORE_PROPERTIES, ignoreProperties);
            BeanUtils.copyProperties(t, v, ignores);
        }
        // 展会
        Activity activity = this.getActivity();
        if (null != activity) {
            v.setActivityId(activity.getId());
            v.setActivityName(activity.getNameZh());
            v.setActivityScope(activity.getActivityScope());
            v.setActivity(activity.toSimpleVO());
        }
        // 机构
        Institution institution = this.getInstitution();
        if (null != institution) {
            v.setInstitutionId(institution.getId());
            v.setInstitutionName(institution.getNameZh());
            v.setInstitution(institution.toSimpleVO());
        }
        // 组团机构
        Institution institutionCluster = this.getInstitutionCluster();
        if (null != institutionCluster) {
            v.setInstitutionClusterId(institutionCluster.getId());
            v.setInstitutionClusterName(institutionCluster.getNameZh());
            v.setInstitutionCluster(institutionCluster.toSimpleVO());
        }
        //審批流
        List<AdmTaskParticipate> tasks = this.getTasks();
        if (!CollectionUtils.isEmpty(tasks)) {
            v.setTasks(tasks.stream().map(AdmTask::toSimpleVO).collect(Collectors.toList()));
            // 当前审批节点
            v.setCurrentTask(v.getTasks().stream()
                    .filter(e -> e.getStatus() == Status.approving)
                    .findFirst().orElse(null));
        }

        // 发起人
        User applicant = this.getApplicant();
        if (null != applicant) {
            v.setApplicantId(applicant.getId());
            v.setApplicantName(applicant.getName()+applicant.getLastName()+"("+applicant.getAccount()+")");
            v.setApplicantAccount(applicant.getAccount());
            v.setApplicantPhone(applicant.getPhone());
            v.setUserVo(applicant.toVO());
        }
        // 联络人
        Liaison liaison = this.getLiaison();
        if (null != liaison) {
            v.setLiaisonId(liaison.getId());
            v.setLiaisonName(liaison.getNameZh());
            v.setLiaison(liaison.toVO());
        }

    }

    protected void copyProperties(V v, T t, String... ignoreProperties) {
        if (ignoreProperties.length == 0) {
            BeanUtils.copyProperties(v, t, IGNORE_PROPERTIES);
        } else {
            String[] ignores = CopyUtils.concat(IGNORE_PROPERTIES, ignoreProperties);
            BeanUtils.copyProperties(v, t, ignores);
        }
        if (null != v.getActivityId()) {
            Activity activity = new Activity();
            activity.setId(v.getActivityId());

            this.setActivity(activity);
        }

        if (null != v.getInstitutionId()) {
            Institution institution = new Institution();
            institution.setId(v.getInstitutionId());
            this.setInstitution(institution);
        }
        if (null != v.getInstitutionClusterId()) {
            Institution institution = new Institution();
            institution.setId(v.getInstitutionClusterId());
            this.setInstitutionCluster(institution);
        }
        if (null != v.getApplicantId()) {
            User applicant = new User();
            applicant.setId(v.getApplicantId());
            this.setApplicant(applicant);
        }

        if (null != v.getLiaisonId()) {
            Liaison liaison = new Liaison();
            liaison.setId(v.getLiaisonId());
            this.setLiaison(liaison);
        }
    }
}
