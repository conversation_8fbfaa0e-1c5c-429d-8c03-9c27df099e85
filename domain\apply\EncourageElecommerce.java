/**
 * Copyright (C), 2020-2020, 珠海联创有限公司
 * FileName: ParticipateAlone
 * Author:   liang
 * Date:     20-3-4 下午3:04
 * Description: 独立参展
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.exhibition.domain.apply;

import com.exhibition.domain.activity.ActivityApplyType;
import com.exhibition.domain.sys.InstitutionShareholderEleCommerce;
import com.exhibition.vo.apply.EncourageElecommerceVO;
import com.exhibition.vo.apply.EncourageEnterpriseVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 電子商務推廣資助計劃
 * @date 2020-05-27
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_elecommerce")
public class EncourageElecommerce extends Encourage<EncourageElecommerce, EncourageElecommerceVO> implements Serializable {

    private static final long serialVersionUID = 491253432545512189L;
    /**
     * 企業類型
     */
    @Enumerated(EnumType.STRING)
    private InstitutionType institutionType;
    /**
     * 申請項目
     */
    @Enumerated(EnumType.STRING)
    private ActivityApplyType applyType;
    /**
     * 補齊資料時間
     */
    private Date supplementinfoTime;
    /**
     * 納稅人編號
     */
    @Column(length = 20)
    private String taxpayerNo;
    /**
     * 行業
     */
    @ElementCollection
    private List<String>  industry;
    /**
     * 成立日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateOfEstablishment;
    /**
     * 場所登記名稱
     */
    @Column(length = 100)
    private String siteRegistrationName;
    /**
     * 場所登記編號
     */
    @Column(length = 50)
    private String siteRegistrationCode;
    /**
     * 營業地址
     */
    @Column(length = 200)
    private String streetZh;
    /**
     * 商業登記證
     */
    @Column(length = 20)
    private String registrationNumber;
    /**
     * 股東比例
     */
    @Column(columnDefinition = "tinyint default 0")
    private Double percents;

    /**
     * 股东成分
     */
    @ElementCollection
    @CollectionTable(name = "ele_commerce_shareholder")
    @Fetch(FetchMode.SELECT)
    private List<InstitutionShareholderEleCommerce> shareholderEleCommerces;
    /**
     * 电商平台名称
     */
    @Column(length = 50)
    private String eleCommercePlatform;

    private String fspno;
    /**
     * 申请金额
     */
    private Double applyAmount;
    /**
     * 批准金額
     */
    private Double approvedAmount;
    /**
     * IP地址
     */
    private String ip;

    /**
     * 展會活動説明(中文)
     */
    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(name = "content", nullable = true)
    private String content;

    @ElementCollection
    @CollectionTable(name = "encourage_elecommerce_products_declaration_file")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> productsDeclaration;

    @ElementCollection
    @CollectionTable(name = "encourage_elecommerce_settlement_declaration_file")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> settlementDeclaration;
    /**
     * 澳門居民身份證明(中文)
     */
    @ElementCollection
    @CollectionTable(name = "encourage_elecommerce_id_file")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> idFile;
    /**
     * 報價單-擬選用之本局認可B2B/B2C電商平台於申請日前90天內發出
     */
    @ElementCollection
    @CollectionTable(name = "encourage_elecommerce_quotation_file")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> quotationFile;
    /**
     * 公司簡介資料及擬於本局認可B2B/B2C電商平台推廣之產品資料；
     */
    @ElementCollection
    @CollectionTable(name = "encourage_elecommerce_company_file")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> companyFile;

    /**
     * 最近一個財政年度向財政局遞交的所得補充稅 A/B 組收益申報書副本(A組收益申報書包括其附列文件)；
     */
    @ElementCollection
    @CollectionTable(name = "encourage_elecommerce_finance_file")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> financeFile;
    /**
     * 最近一季由社會保障基金發出的供款記錄副本；如申請者屬沒有聘用員工之情況，須提交補充聲明書；
     */
    @ElementCollection
    @CollectionTable(name = "encourage_elecommerce_social_file")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> socialFile;
    /**
     *申請者有在澳門實質營運的證明，包括購貨單及銷貨單等相關證明文件。證明文件上須清晰顯示交易雙方名稱、交易項目及金額等重要資料；
     */
    @ElementCollection
    @CollectionTable(name = "encourage_elecommerce_operation_file")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> operationFile;
    /**
     * 通訊地址證明文件-須提供具申請者名稱的任一單據，如水費、電費、電訊費或銀行月結單據；
     */
    @ElementCollection
    @CollectionTable(name = "encourage_elecommerce_address_file")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> addressFile;

    /**
     * 是否同意
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isAgreeTreaty;

    public EncourageElecommerce(EncourageElecommerceVO vo) {
        copyProperties(vo, this);
    }

    public EncourageElecommerce(Encourage e) {
        BeanUtils.copyProperties(e, this);
    }

    @Override
    public EncourageElecommerceVO toVO() {
        return toVO(false);
    }


    public EncourageElecommerceVO toVO(boolean includeLazy) {
        EncourageElecommerceVO vo = new EncourageElecommerceVO();
        copyProperties(this, vo);
        return vo;
    }
}
