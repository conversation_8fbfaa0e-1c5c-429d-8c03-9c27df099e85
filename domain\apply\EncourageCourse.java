package com.exhibition.domain.apply;

import com.exhibition.vo.apply.EncourageCourseVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import javax.persistence.CollectionTable;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 036培訓課程或公開考試-籌辦課程
 * @date 2020-06-12
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_course")
public class EncourageCourse extends Encourage<EncourageCourse, EncourageCourseVO> implements Serializable {

    private static final long serialVersionUID = 1753231676328032171L;

    /**
     * 培訓課程或公開考試名稱（中文）
     */
    private String                    nameZh;
    /**
     * 培訓課程或公開考試名稱（英文）
     */
    private String                    nameEn;
    /**
     * 用於取得
     */
    private CourseType                courseType;
    /**
     * 報讀條件
     */
    private String                    signUpCondition;
    /**
     * 取得有關修讀證明書或專業證照的條件
     */
    private String                    passCondition;
    /**
     * 活動舉辦地址
     */
    private String                    address;
    /**
     * 培訓活動或公開考試的類別
     */
    private TrainingTargetType        targetType;
    /**
     * 培訓活動或公開考試的範圍
     */
    @ElementCollection
    private List<String>              scopes;
    /**
     * 預計學員人數
     */
    private Integer                   totalStudents;
    /**
     * 開始時間
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date                       startDate;
    /**
     * 結束時間
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date                       endDate;
    /**
     * 培訓課程的形式及内容簡介
     */
    @ElementCollection
    @CollectionTable(name = "encourage_course_training")
    @Fetch(FetchMode.SELECT)
    private List<TrainingDesc>         trainings;
    /**
     * 授課實體的名稱
     */
    private String                     entityName;
    /**
     * 申請者於培訓活動中擔當的職務
     */
    private String                     position;
    /**
     * 申請者於培訓活動中 的職責內容
     */
    private String                     duty;
    /**
     * 培訓活動主要負責人和成員資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_course_principal")
    @Fetch(FetchMode.SELECT)
    private List<CoursePrincipal>      principals;

    /**
     * 申請參加培訓課程或公開考試的財務預算
     */
    @ElementCollection
    @CollectionTable(name = "encourage_course_budget")
    @Fetch(FetchMode.SELECT)
    private List<EncourageBudget>      budgets;
    /**
     * 申請參加培訓課程或公開考試的財務支出
     */
    @ElementCollection
    @CollectionTable(name = "encourage_course_income")
    @Fetch(FetchMode.SELECT)
    private List<EncourageBudget>      incomes;
    /**
     * 总支出
     */
    private Integer                    totalBudget;
    /**
     * 總收入
     */
    private Integer                    totalIncome;
    /**
     * 申請財務支持金額
     */
    private Double                     applyAmount;
    /**
     * 保證金制度
     */
    private String                     marginSystem;
    /**
     * 附件
     */
    @ElementCollection
    @CollectionTable(name = "encourage_course_attachment")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCertAttachment> attachments;
    /**
     * 於其他政府機關就同一活動之支持申請
     */
    @ElementCollection
    @CollectionTable(name = "encourage_course_gov_support")
    @Fetch(FetchMode.SELECT)
    private List<GovSupport>          govSupports;
    /**
     * 声明
     */
    private Boolean             stateAgree;

    public EncourageCourse(EncourageCourseVO vo) {
        copyProperties(vo, this);
    }

    @Override
    public EncourageCourseVO toVO() {
        EncourageCourseVO vo = new EncourageCourseVO();
        copyProperties(this, vo);
        return vo;
    }
}