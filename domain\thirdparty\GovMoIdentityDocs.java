package com.exhibition.domain.thirdparty;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Embeddable;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Data
@NoArgsConstructor
@Embeddable
public class GovMoIdentityDocs {
    private String nameCn;
    private String namePt;
    private String gender;

    @Temporal(TemporalType.TIMESTAMP)
    private Date birthday;
    private String identityNo;
    private String identityType;
    private String identityIssuePlace;

    @Temporal(TemporalType.TIMESTAMP)
    private Date identityValidDate;
}