package com.exhibition.domain.apply;

import com.exhibition.domain.sys.Liaison;
import com.exhibition.vo.apply.ParticipateEndorsementConVO;
import com.exhibition.vo.apply.ParticipateSpGuestsVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@Embeddable
public class ParticipateEndorsementCon implements Serializable {

    /**
     * 機構聯絡人
     */
    private Long liaisonId;
    //姓名
    private String name;
    //性別
    @Column(columnDefinition = "tinyint default 0")
    private Boolean sex;
    //職位
    private String position;
    //大陸證件
    private String idNo;
    //澳門證件
    private String macaoIdNo;
    //备注
    private String remark;
    //單位
    private String insname;
    /**
     * 出生日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date personDate;
    /**
     * 抵澳日期
     */
    @ApiModelProperty("抵澳日期")
    /**
     * 展會開始日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date arriveDate;
    /**
     * 離澳日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date outDate;
    /**
     * 从事活动
     */
    private String campaign;
    /*
     * 訪問澳門次數
     * */
    private MacaoInterview macaoInterview;

    public ParticipateEndorsementCon(ParticipateEndorsementConVO vo) {
        BeanUtils.copyProperties(vo,this);


    }

    public ParticipateEndorsementConVO toVO() {
        return toVO(false);
    }

    public ParticipateEndorsementConVO toVO(boolean includeLazy) {
        ParticipateEndorsementConVO vo = new ParticipateEndorsementConVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }
}
