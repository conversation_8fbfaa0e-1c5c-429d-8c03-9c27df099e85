

package com.exhibition.domain.apply;
import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.activity.SetAttributeConverter;
import com.exhibition.domain.sys.Status;
import com.exhibition.domain.sys.User;
import com.exhibition.vo.apply.EncourageConventionGradeVO;
import com.exhibition.vo.apply.EncourageStatusLogVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.springframework.beans.BeanUtils.copyProperties;


@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_statuslog")
@BatchSize(size = 20)
public class EncourageStatusLog extends BaseEntity<EncourageStatusLog, EncourageStatusLogVO> implements Serializable {

    private static final long serialVersionUID = -6952423751920495341L;
    private static final String[] IGNORE_PROPERTIES = new String[]{""};

    /**
     * 展會申請实体
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "encourage_id", referencedColumnName = "id")
    private Encourage encourage;

    /**
     * 過往狀態
     */
    @Column
    private Status oldStatus;
    /**
     * 當前狀態
     */
    @Column
    private Status curStatus;
    /**
     * 修改人
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private User applicant;

    /**
     * 修改日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date appTime;


    public EncourageStatusLog(EncourageStatusLogVO vo) {
        copyProperties(vo, this, IGNORE_PROPERTIES);

    }

    public EncourageStatusLogVO toVo(boolean includeLazy) {
        EncourageStatusLogVO v = new EncourageStatusLogVO();
        copyProperties(this, v, IGNORE_PROPERTIES);
        if (includeLazy) {
            Encourage encourage = this.getEncourage();
            if (null != encourage) {
                v.setEncourageVO(encourage.toVO());
                v.setEncourageId(encourage.getId());

            }
            if(null!=this.getApplicant()){
                v.setApplicantId(this.getApplicant().getId());
                v.setApplicantName(this.getApplicant().getName());
                v.setApplicantAccount(this.getApplicant().getAccount());
            }
        }
        return v;
    }

    public EncourageStatusLogVO toVo() {
        return toVo(false);
    }
}
