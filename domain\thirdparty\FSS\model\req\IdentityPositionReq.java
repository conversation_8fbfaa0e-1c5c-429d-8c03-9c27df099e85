package com.exhibition.domain.thirdparty.FSS.model.req;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/***
 * <AUTHOR>
 * @date 2024/12/18 13:38
 * @describe 身份证明局接口
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class IdentityPositionReq implements Serializable {

    @JsonProperty("user_id")
    private String user_id;
    @JsonProperty("request_data")
    private RequestDataDTO request_data;
    @JsonProperty("sign")
    private String sign;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString
    public static class RequestDataDTO implements Serializable {
        @JsonProperty("id")
        private String id;
        @JsonProperty("asso_no")
        private String asso_no;
        @JsonProperty("asso_safp_no")
        private String asso_safp_no;
        @JsonProperty("id_type")
        private String id_type;
    }
}
