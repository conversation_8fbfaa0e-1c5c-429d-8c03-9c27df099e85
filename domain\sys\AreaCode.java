package com.exhibition.domain.sys;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.apply.Encourage;
import com.exhibition.domain.enums.Confirm;
import com.exhibition.vo.sys.AreaCodeVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "area_code")
@JsonView(Encourage.EncourageSimpleView.class)
public class AreaCode extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -975921931534540596L;

    /**
     * 國家地區城市中文
     */
    private String nameZh;
    /**
     * 國家地區城市英文
     */
    private String nameEn;
    /**
     * 國家地區城市葡文
     */
    private String namePt;
    /**
     * 區號
     */
    private String code;
    /**
     * 顯示區號
     */
    private String showCode;
    /**
     * 數據來源
     */
    private String       source;

    /**
     * 是否只读
     */
    @Enumerated(EnumType.STRING)
    private Confirm readonly;
    /**
     * 是否禁用
     */
    @Enumerated(EnumType.STRING)
    private Confirm      disabled;
    /**
     * 是否删除
     */
    @Enumerated(EnumType.STRING)
    private Confirm      deleted;
    public AreaCode(AreaCodeVO vo) {
        BeanUtils.copyProperties(vo, this);
    }

    public AreaCodeVO toVO() {
        AreaCodeVO vo = new AreaCodeVO();
        BeanUtils.copyProperties(this, vo);
        return vo;
    }
}
