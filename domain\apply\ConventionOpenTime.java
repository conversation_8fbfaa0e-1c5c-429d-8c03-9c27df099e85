package com.exhibition.domain.apply;

import com.exhibition.vo.apply.ConventionOpenTimeVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: ConventionOpenTime
 * @description: 預計展覽會開放時間
 * @author: ShiXin
 * @create: 2020-03-11 16:19
 **/
@Data
@NoArgsConstructor
@Embeddable
public class ConventionOpenTime implements Serializable {

    private static final long    serialVersionUID = -3610746876486794915L;
    /**
     * 第几天
     */
    private              Integer day;
    /**
     * 預計展會開始時間
     */
    @Temporal(TemporalType.TIMESTAMP)
    private              Date    openFrom;
    /**
     * 預計展會結束時間
     */
    @Temporal(TemporalType.TIMESTAMP)
    private              Date    openTo;
    /**
     * 總時間
     */
    private              Double  total;

    public ConventionOpenTime(ConventionOpenTimeVO vo) {
        BeanUtils.copyProperties(vo,this);
    }

    public ConventionOpenTimeVO toVO() {
        ConventionOpenTimeVO v = new ConventionOpenTimeVO();
        BeanUtils.copyProperties(this,v);
        return v;
    }
}
