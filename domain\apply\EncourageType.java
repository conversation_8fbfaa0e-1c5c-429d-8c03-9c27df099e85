package com.exhibition.domain.apply;

import java.util.Arrays;
import java.util.Locale;

/**
 * <AUTHOR>
 * @desc 支持及鼓勵措施的類型
 * @date 2020-03-26
 * @since 1.0.0
 */
public enum EncourageType {
    /**
     * 非牟利社團赴境外展會設置展位
     */
    ATTEND("非牟利社團赴境外展會設置展位"),
    /**
     * 非牟利社團組織代表團參與境外展會
     */
    EN_MISSION("非牟利社團組織代表團參與境外展會"),
    /**
     * 企業參與本地或境外展會
     */
    ENTERPRISE("企業參與本地或境外展會"),
    /**
     * 會議及展覽資質計劃
     */
    CONVENTION("會議及展覽資質計劃"),
    /**
     *
     */
    CONFERENCE("國際專業會議募集計劃"),

    /**
     * 展會及商務旅游展支持計劃
     */
    TOUR("展會及商務旅游展支持計劃"),
    /**
     * 展會專業人才培訓支援計劃
     */
    TRAIN("展會專業人才培訓支援計劃"),
    /**
     * 籌辦課程
     */
    COURSE("籌辦課程"),
    /**
     * 電子商務B2B推廣
     */
    ECB2B("電子商務B2B推廣"),
    /**
     * 電子商務B2C推廣
     */
    ECB2C("電子商務B2C推廣"),
    /**
     * Mainland
     */
    MAINLAND("内地簽注"),

    ELECOMMERCE("电子商务"),

    /**
     * 澳門國際環保合作發展論壇及展覽 -MIECF
     */
    MIECF("澳門國際環保合作發展論壇及展覽 -MIECF"),
    /**
     * 澳門國際環保合作發展論壇及展覽 -MIECF
     */
    WRITTENCONSENT("同意書");




    private final String name;

    EncourageType(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static EncourageType getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
