package com.exhibition.domain.apply;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 * <AUTHOR>
 * @desc 034會議及展覽資助計劃展後報告審批
 * @date 2020-06-07
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Entity
@DiscriminatorValue(Flowtpl.ENCOURAGE_CONVENTION_REPORT)
public class AdmTaskEncourageConventionReport extends AdmTask {
    
    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    private EncourageConventionReport  encourageConventionReport;
}