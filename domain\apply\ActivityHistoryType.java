package com.exhibition.domain.apply;

import java.util.Arrays;

public enum ActivityHistoryType {
    /**
     * 超過兩屆
     */
    TWO_EDITIONS("超過兩屆"),
    /**
     * 第二屆舉辦
     */
    SECOND_EDITION("第二屆舉辦"),
    /**
     * 第一屆
     */
    FIRST_EDITION("第一屆");

    private final String name;

    ActivityHistoryType(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ActivityHistoryType getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
