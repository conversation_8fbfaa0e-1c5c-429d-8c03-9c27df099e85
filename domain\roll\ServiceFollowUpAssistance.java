package com.exhibition.domain.roll;

import java.util.Arrays;

/**
 */
public enum ServiceFollowUpAssistance {
    A1("尋找本地供應商"),
    A2("聯繫本地商協會"),
    A3("協調政府部門"),
    A4("活動宣傳"),
    A5("活動組織協調"),
    A6("社區活動組織協調"),
    A7("作為支持單位"),
    A8("內地參展參會人員赴澳便利措施"),
    A9("輔助「會展扶助專項申請」");

    private final String name;

    ServiceFollowUpAssistance(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ServiceFollowUpAssistance getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
