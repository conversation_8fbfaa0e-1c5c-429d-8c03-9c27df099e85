package com.exhibition.domain.apply;

import java.util.Arrays;

public enum ParticipateBusinessMatching {

    /**
     * 政府及公共事業
     */
    GOVERNMENT_PUBLIC("政府及公共事業"),
    /**
     * 酒店及綜合度假村
     */
    HOTEL("酒店及綜合度假村"),
    /**
     * 建築業及樓宇
     */
    BUILDING("建築業及樓宇"),
    /**
     * 一般辦公室
     */
    GENERAL_OFFICE("一般辦公室"),
    /**
     * 教育
     */
    EDUCATION("教育"),
    /**
     * 建造業
     */
    CONSTRUCTION("建造業"),
    /**
     * 制造业
     */
    MANUFACTURING("制造业"),
    /**
     * 其它工业
     */
    OTHERINDUSTRIAL("其它工业"),
    /**
     * 无配对需要
     */
    NOMATCHING("无配对需要"),
    A01("製造商"),
    A02("進/出口商"),
    A03("批發商/分銷商/零售商"),
    A04("政府部門/公共機構"),
    A05("商/協會"),
    A06("研究機構/大學"),
    A07("媒體"),
    A08("顧問機構"),
    A09("服務提供商"),
    /**
     * 其他
     */
    OTHER("其他");


    private final String name;

    ParticipateBusinessMatching(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ParticipateBusinessMatching getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
