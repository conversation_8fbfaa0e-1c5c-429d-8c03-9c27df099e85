package com.exhibition.domain.apply;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;


@Data
@NoArgsConstructor
@Entity
@DiscriminatorValue(Flowtpl.ENCOURAGE_MISSION_REPORT)
public class AdmTaskEncourageMissionReport extends AdmTask {
    
    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    private EncourageMissionReport  encourageMissionReport;

}