package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.vo.apply.AttachmentVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

import static org.springframework.beans.BeanUtils.copyProperties;

/**
 * @description: 附件
 * @author: Janice
 * @date: 2020/5/20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "attachment")
public class Attachment extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 2088793965987945983L;

    /**
     * 关联记录id
     */
    private Long relatedId;
    /**
     * 附件的类型
     */
    @Enumerated(EnumType.STRING)
    private AttachType type;
    /**
     * uid
     */
    private String uid;
    /**
     * 文件名
     */
    private String name;
    /**
     * url
     */
    private String url;

    public Attachment(AttachmentVO vo) {
        copyProperties(vo, this);
    }
    public List<Attachment> attachmentList(List<AttachmentVO> voList) {
       return voList.stream().map(Attachment::new).collect(Collectors.toList());
    }

    public AttachmentVO toVO() {
        AttachmentVO vo = new AttachmentVO();
        copyProperties(this, vo);
        return vo;
    }
}
