package com.exhibition.domain.pay;

import com.exhibition.vo.pay.StatementDetailVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Embeddable
public class StatementDetail  {
    // 订单字段
    private String orderDate;                // 订单日期
    private String orderType;                // 订单类型
    private String orderTime;                // 订单时间
    private String merchantOrderNumber;      // 商户订单编号
    private String merchantNumber;           // 商户编号
    private String storeNumber;              // 门店编号
    private String terminalNumber;           // 终端编号
    private String paymentMethod;            // 支付方式
    private String orderStatus;              // 订单状态
    private BigDecimal transactionAmount;           // 交易金额
    private String transactionFee;              // 交易手续费
    private String thirdPartyOrderNumber;    // 第三方订单号
    private String transactionAccessChannel;  // 交易接入渠道
    private BigDecimal orderAmount;                 // 订单金额
    private BigDecimal activityDiscountAmount;      // 活动优惠金额
    private String marketingActivityNumber;  // 營銷活動編號
    private String bankOrderNumber;          // 中銀訂單號
    private String originalTransactionOrderNumber; // 原交易订单号
    private String currency;                 // 币别
    private String cardNumber;               // 卡号
    private String authorizationNumber;       // 授权号
    private String batchNumber;              // 批次号
    private String terminalSerialNumber;     // 终端流水号
    private String transactionMethod;        // 交易方式
    private String installmentPeriod;        // 分期期数
    private String attribute;                // 属性
    private String orderDescription;         // 订单描述


    public StatementDetail(StatementDetailVO vo) {
        BeanUtils.copyProperties(vo, this);
    }

    public StatementDetailVO toVO() {
        StatementDetailVO vo = new StatementDetailVO();
        BeanUtils.copyProperties(this, vo);
        return vo;
    }

}
