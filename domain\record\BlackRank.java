package com.exhibition.domain.record;

import com.exhibition.domain.activity.ActivityTrade;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/5/8 15:27
 * @describe 黑名单等级
 */
public enum BlackRank {
    A("過往有違規記錄"),
    B("正處理有關違規記錄"),
    C("違規記錄生效，但是不影響申請"),
    D("禁止申請");
    private final String name;
    BlackRank(String name){this.name=name;}
    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static BlackRank getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
