package com.exhibition.domain.roll;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.vo.roll.QuestionItemVO;
import com.exhibition.vo.roll.QuestionVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.BatchSize;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.sys
 * @ClassName: Question
 * @description: 题目
 * @author: ShiXin
 * @create: 2020-03-06 17:36
 **/
@Data
@NoArgsConstructor
@Entity
@Table(name = "question")
public class Question extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -1868377642834750442L;

    private static final String[] IGNORE_PROPERTIES = new String[]{"questionnaire", "questionItems"};

    /**
     * 所属展会问卷
     */
    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    private Questionnaire questionnaire;

    /**
     * 所属普通问卷
     */
    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    private Examine examine;

    /**
     * 题目类型
     */
    @Enumerated(EnumType.STRING)
    private QuestionType type;

    /**
     * 顺序
     */
    private Integer sequence;

    /**
     * 题干
     */
    private String title;

    /**
     * 题目说明
     */
    private String note;

    /**
     * 选择題選項
     */
    @OneToMany(mappedBy = "question", fetch = FetchType.LAZY, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @BatchSize(size = 20)
    private List<QuestionItem> questionItems;

    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOther=false;
    /**
     * 提示
     */
    @Column(columnDefinition = "text")
    private String tips;
    /**
     * 是否必输
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isInput=false;


    public Question(QuestionVO vo) {
        BeanUtils.copyProperties(vo, this,IGNORE_PROPERTIES);

        List<QuestionItemVO> items = vo.getQuestionItems();
        if (!CollectionUtils.isEmpty(items)){
            List<QuestionItem> collect = items.stream()
                    .map(QuestionItem::new)
                    .peek(q -> q.setQuestion(this))
                    .collect(Collectors.toList());
            this.setQuestionItems(collect);
        }else {
            this.setQuestionItems(Collections.emptyList());
        }
    }

    public QuestionVO toVO() {
        return toVO(false);
    }


    public QuestionVO toVO(boolean includeLazy) {
        QuestionVO vo = new QuestionVO();
        BeanUtils.copyProperties(this, vo,IGNORE_PROPERTIES);

        if (includeLazy){
            if (!CollectionUtils.isEmpty(questionItems)){
                vo.setQuestionItems(questionItems.stream().map(QuestionItem::toVO).collect(Collectors.toList()));
            }
        }

        return vo;
    }

}
