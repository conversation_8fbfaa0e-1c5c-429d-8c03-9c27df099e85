package com.exhibition.domain.sys;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.apply.Encourage;
import com.exhibition.domain.member.Integral;
import com.exhibition.domain.thirdparty.GovMo;
import com.exhibition.domain.thirdparty.GovMoEntity;
import com.exhibition.swagger.IgnoreSwaggerParameter;
import com.exhibition.vo.sys.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@Entity
@Table(name = "user")
@BatchSize(size = 20)
@JsonView(Encourage.EncourageSimpleView.class)
public class User extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 2153461917767419813L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /**
     * 用户类型
     */
    @Enumerated(value = EnumType.STRING)
    @Column(nullable = false, length = 100)
    private UserType type;
    /**
     * 姓名
     */
    private String name;
    /**
     * 头像
     */
    private String pic;
    /**
     * 账号，唯一
     */
    @Column(nullable = false, unique = true, length = 100)
    private String account;
    /**
     * 座机
     */
    private String tel;
    /**
     * 電話區號
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private AreaCode phoneAreaCode;
    /**
     * 电话
     */
    private String phone;
    /**
     * 设备类型:android/ios
     */
    private String agent;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 设备id
     */
    private String deviceid;
    /**
     * 华为设备token
     */
    private String deviceToken;
    /**
     * 密码 原有@Column(updatable = false)
     */
    private String pwd;
    /**
     * 职位
     */
    private String job;
    /**
     * 系统角色
     */
    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "user_role", joinColumns = @JoinColumn(name = "users_id"),
            inverseJoinColumns = @JoinColumn(name = "roles_id"))
    private List<Role> roles;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 隐藏操作记录
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean hideTrends;
    /**
     * 接收本局消息的方式
     */
    @ElementCollection
    @CollectionTable(name = "user_receive")
    @Fetch(FetchMode.SELECT)
    @Enumerated(EnumType.STRING)
    private List<Receive> receives;
    /**
     * 权限
     */
    @Transient
    private List<Purview> purviews;
    /**
     * 按钮权限
     */
    @Transient
    private List<Purview> buttons;
    /**
     * 用户的机构
     */
    @Transient
    private List<Institution> institutions;
    /**
     * 子账号
     */
    @Transient
    private List<UserSubAccount> subAccounts;
    /**
     * 父账号
     */
    @Transient
    private List<UserSubAccount> parentAccounts;

    /**
     * 来源類型
     */
    @Enumerated(EnumType.STRING)
    private SourceType source;

    /**
     * 批量導入
     */
    private String sign;

    /**
     * 登錄錯誤次數
     */
    @Column(columnDefinition = "int default 0")
    private int locks;

    /**
     * 錯誤時間
     *
     * @param vo
     */
    private Date wrongTime;

    /**
     * 是否初始賬號(0代表false為初始賬號，1代表true為修改過後的)
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean initials;

    /**
     * 密碼過期時間
     *
     * @param vo
     */
    private Date expirationDate;

    /**
     * 一戶通自然人
     *
     * @param vo
     */
    @OneToOne(optional = true, fetch = FetchType.LAZY)
    private GovMo govMo;

    /**
     * 一戶通實體
     * @param vo
     */

    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<GovMoEntity> govMoEntity;
    /**
     * 擴展字段1（BM）
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField1;
    /**
     * 擴展字段2（BM）
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField2;
    /**
     * 擴展字段3（BM）
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField3;
    /**
     * 擴展字段4（BM）
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField4;
    /**
     * 擴展字段5（BM）
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField5;
    /**
     * 擴展字段6（BM）
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField6;
    /**
     * 擴展字段7（BM）
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField7;
    /**
     * 擴展字段8（BM）
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField8;
    /**
     * 擴展字段9（BM）
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField9;
    /**
     * 擴展字段10（BM）
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField10;

    /**
     * 积分
     * @param vo
     */
    @OneToOne(optional = true, fetch = FetchType.LAZY)
    private Integral integral;
    /**
     * 稱呼
     */
    @JsonView(Encourage.EncourageSimpleView.class)
    @Enumerated(value = EnumType.STRING)
    private Call calls;
    /**
     * 名
     */
    private String lastName;
    /**
     * 会展通电子码
     */
    private String barCode;

    /**
     * 简写
     */
    private String shortName;

    /**
     *
     * 感興趣的產品/服務
     */
    @ElementCollection
    private List<String> interestedProducts;
    /**
     *
     * 感興趣的國家/地區
     */
    @ElementCollection
    private List<String> interestedCountries;

    /**
     *
     * 感興趣的內容
     */
    @ElementCollection
    private List<String> interestingContent;


    /**
     * 一戶通同步(0代表false默認為同步，1代表true為同步後的)
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean informac;

    /**
     * 客户语言偏好
     */
    @Column(columnDefinition = "varchar(10) default 'zh'")
    private String language;

    public User(UserVO vo) {
        BeanUtils.copyProperties(vo, this, "phoneAreaCode", "roles", "purviews", "institutions", "buttons","govMoEntity");
        Long phoneAreaCodeId = vo.getPhoneAreaCodeId();
        if (null != phoneAreaCodeId) {
            AreaCode code = new AreaCode();
            code.setId(phoneAreaCodeId);
            this.setPhoneAreaCode(code);
        }
        if (!CollectionUtils.isEmpty(vo.getRoles())) {
            List<Role> _$roles = vo.getRoles().stream().map(Role::new).collect(Collectors.toList());
            this.setRoles(_$roles);
        }
    }


    public UserConventionVO tocVO() {
        UserConventionVO userConventionVO = new UserConventionVO();
        userConventionVO.setId(this.getId());
        userConventionVO.setType(this.getType());
        userConventionVO.setName(this.getName());
        userConventionVO.setPic(this.getPic());
        userConventionVO.setAccount(this.getAccount());
        userConventionVO.setTel(this.getTel());
        userConventionVO.setPhoneAreaCodeId(this.getPhoneAreaCode().getId());
        userConventionVO.setPhoneAreaCode(this.getPhoneAreaCode().toVO());
        userConventionVO.setPhone(this.getPhone());
        userConventionVO.setJob(this.getJob());
        userConventionVO.setEmail(this.getEmail());
        userConventionVO.setSource(this.getSource());
        userConventionVO.setSign(this.getSign());
        userConventionVO.setCreateAt(this.getCreateAt());
        userConventionVO.setUpdateAt(this.getUpdateAt());

        return userConventionVO;
    }

    public UserVO toVO() {

        return toVO(false);
    }

    public UserVO toGovVO(boolean includeRole) {
        UserVO vo = new UserVO();
        BeanUtils.copyProperties(this, vo,  "pwd", "purviews", "institutions", "buttons","govMoEntity");

        if (null != roles) {
            vo.setRoles(roles.stream().map(r -> r.toVO()).collect(Collectors.toList()));
        }
        if(govMo!=null){
            vo.setGovMoId(govMo.getId());
        }

        if (includeRole) {
            if (null != phoneAreaCode) {
                vo.setPhoneAreaCodeId(phoneAreaCode.getId());
                vo.setPhoneAreaCode(phoneAreaCode.toVO());
            }
            if (!CollectionUtils.isEmpty(purviews)) {
                vo.setPurviews(purviews.stream().map(p -> p.toVO(true)).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(govMoEntity)) {
                vo.setPurviews(govMoEntity.stream().map(p -> p.toVO(true)).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(buttons)) {
                vo.setButtons(buttons.stream().map(p -> p.toVO(true)).collect(Collectors.toList()));
            }

            if (!CollectionUtils.isEmpty(institutions)) {
                vo.setInstitutions(institutions.stream().map(Institution::toVO).collect(Collectors.toList()));
            } else {
                vo.setInstitutions(Collections.emptyList());
            }

            if (!CollectionUtils.isEmpty(subAccounts)) {
                vo.setSubAccounts(subAccounts.stream().map(UserSubAccount::toVO).collect(Collectors.toList()));
            } else {
                vo.setSubAccounts(Collections.emptyList());
            }

            if (!CollectionUtils.isEmpty(parentAccounts)) {
                vo.setParentAccounts(parentAccounts.stream().map(UserSubAccount::toVO).collect(Collectors.toList()));
                List<InstitutionVO> institutions = parentAccounts.stream().map(parent -> {
                    UserSubAccountVO accountVO = parent.toVO(true);
                    return accountVO.getInstitution();
                }).collect(Collectors.toList());
                //去重
                institutions = cleanDuplicates(institutions);

                List<InstitutionVO> institutions1 = vo.getInstitutions();

                if (!CollectionUtils.isEmpty(institutions1)) {
                    institutions1.addAll(institutions);
                    //去重
                    vo.setInstitutions(cleanDuplicates(institutions1));
                } else {
                    vo.setInstitutions(institutions);
                }
            } else {
                vo.setParentInstitutions(Collections.emptyList());
            }
        }
        return vo;
    }

    public UserVO toVO(boolean includeRole) {
        UserVO vo = new UserVO();
        BeanUtils.copyProperties(this, vo, "pwd", "roles", "purviews", "institutions", "buttons","govMoEntity");

        if (null != roles) {
            vo.setRoles(roles.stream().map(r -> r.toVO()).collect(Collectors.toList()));
        }
        if (null != phoneAreaCode) {
            vo.setPhoneAreaCodeId(phoneAreaCode.getId());
            vo.setPhoneAreaCode(phoneAreaCode.toVO());
        }
        if (integral != null) {
            vo.setIntegral(integral);
        }
        if(govMo!=null){
            vo.setGovMoId(govMo.getId());
        }
        if (includeRole) {
            if (govMo != null) {
                vo.setGovMo(govMo);
            }
            if (!CollectionUtils.isEmpty(govMoEntity)) {
                vo.setPurviews(govMoEntity.stream().map(p -> p.toVO(true)).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(purviews)) {
                vo.setPurviews(purviews.stream().map(p -> p.toVO(true)).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(buttons)) {
                vo.setButtons(buttons.stream().map(p -> p.toVO(true)).collect(Collectors.toList()));
            }

            if (!CollectionUtils.isEmpty(institutions)) {
                vo.setInstitutions(institutions.stream().map(Institution::toVO).collect(Collectors.toList()));
            } else {
                vo.setInstitutions(Collections.emptyList());
            }

            if (!CollectionUtils.isEmpty(subAccounts)) {
                vo.setSubAccounts(subAccounts.stream().map(UserSubAccount::toVO).collect(Collectors.toList()));
            } else {
                vo.setSubAccounts(Collections.emptyList());
            }


            if (!CollectionUtils.isEmpty(parentAccounts)) {
                vo.setParentAccounts(parentAccounts.stream().map(UserSubAccount::toVO).collect(Collectors.toList()));
                List<InstitutionVO> institutions = parentAccounts.stream().map(parent -> {
                    UserSubAccountVO accountVO = parent.toVO(true);
                    return accountVO.getInstitution();
                }).collect(Collectors.toList());
                //去重
                institutions = cleanDuplicates(institutions);

                List<InstitutionVO> institutions1 = vo.getInstitutions();

                if (!CollectionUtils.isEmpty(institutions1)) {
                    institutions1.addAll(institutions);
                    //去重
                    vo.setInstitutions(cleanDuplicates(institutions1));
                } else {
                    vo.setInstitutions(institutions);
                }
            } else {
                vo.setParentInstitutions(Collections.emptyList());
            }
        }
        return vo;
    }

    public UserRep toVO(User user){
        UserRep userRep = new UserRep();
        userRep.setId(user.getId());
        userRep.setEmail(user.getEmail());
        return userRep;
    }

    /**
     * 机构去重
     *
     * @param institutions
     */
    private ArrayList<InstitutionVO> cleanDuplicates(List<InstitutionVO> institutions) {
        Set<InstitutionVO> set = new HashSet<>();
        set.addAll(institutions);
        return new ArrayList<>(set);
    }

    /**
     * 获取用户所有角色的权限
     *
     * @param roles
     */
    public List<Purview> userPurview(List<Role> roles) {
        if (!CollectionUtils.isEmpty(roles)) {
            List<Purview> purviews = roles.stream().flatMap(r -> {
                List<Purview> temp = r.getPurviews();
                if (!CollectionUtils.isEmpty(temp)) {
                    return temp.stream();
                }
                return null;
            }).collect(Collectors.toList());

            //多角色权限重叠去重
            Set<Purview> set = new HashSet<>();
            set.addAll(purviews);
            return new ArrayList<>(set);
        }
        return Collections.emptyList();
    }

    /**
     * 获取用户的按钮权限
     *
     * @param roles
     * @return
     */
    public List<Purview> formButtons(List<Role> roles) {
        List<Purview> purviews = this.userPurview(roles);
        if (!CollectionUtils.isEmpty(purviews)) {
            return purviews.stream().filter(p -> PurviewType.BUTTON.equals(p.getType())).collect(Collectors.toList());
        }
        return Collections.emptyList();

    }

    /**
     * 获取用户菜单路由
     *
     * @param roles
     */
    public List<Purview> formMenus(List<Role> roles) {
        List<Purview> purviews = this.userPurview(roles);
        if (!CollectionUtils.isEmpty(purviews)) {
            List<Purview> menus = purviews.stream().filter(p -> PurviewType.MENU.equals(p.getType())).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(menus)) {
                return menus;
            }
        }
        return Collections.emptyList();
    }

    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", type=" + type +
                ", name='" + name + '\'' +
                ", pic='" + pic + '\'' +
                ", account='" + account + '\'' +
                ", tel='" + tel + '\'' +
                ", phoneAreaCode=" + phoneAreaCode +
                ", phone='" + phone + '\'' +
                ", agent='" + agent + '\'' +
                ", brand='" + brand + '\'' +
                ", deviceid='" + deviceid + '\'' +
                ", deviceToken='" + deviceToken + '\'' +
                ", pwd='" + pwd + '\'' +
                ", job='" + job + '\'' +
//                ", roles=" + roles +
                ", email='" + email + '\'' +
                ", hideTrends=" + hideTrends +
                ", receives=" + receives +
                ", purviews=" + purviews +
                ", buttons=" + buttons +
                ", institutions=" + institutions +
                ", subAccounts=" + subAccounts +
                ", parentAccounts=" + parentAccounts +
                ", source=" + source +
                ", sign='" + sign + '\'' +
                '}';
    }

}
