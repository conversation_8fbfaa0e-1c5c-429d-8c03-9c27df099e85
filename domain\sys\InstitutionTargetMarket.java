package com.exhibition.domain.sys;

import java.util.Arrays;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: Targetmarket
 * @description: 目標市場
 * @author: ShiXin
 * @create: 2020-03-17 16:55
 **/
public enum InstitutionTargetMarket {

    /**
     * 澳門
     */
    MACAO("中国澳門"),
    /**
     * 粵港澳大灣區
     */
    GBA("粵港澳大灣區"),

    /**
     * 中國內地
     */
    MAINLAND_CHINA("中國內地"),
    /**
     * 香港
     */
    HONGKONG("中国香港"),
    /**
     * 美国
     */
    THE_UNITED_STATES("美国"),
    /**
     * 歐洲
     */
    EUROPE("歐洲"),
    /**
     * 葡語國家
     */
    PORTUGUESE_SPEAKING_COUNTRIES_REGIONS("葡語國家"),
    /**
     * 亞洲
     */
    ASIA_AREA("亞洲"),
    /**
     * 非洲
     */
    AFRICA("非洲"),
    /**
     * 南美洲
     */
    SOUTH_AMERICA("南美洲"),
    /**
     * 北美洲
     */
    NORTH_AMERICA("北美洲"),
    /**
     * 其他
     */
    OTHER("其他");

    private final String name;
    InstitutionTargetMarket(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static InstitutionTargetMarket getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }

    public static InstitutionTargetMarket getByValue(String value) {
        if (null == value || "".equals(value)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.toString().equals(value))
                .findFirst()
                .orElse(null);
    }
}
