package com.exhibition.domain.thirdparty.FSS.model.vo;

import cn.hutool.core.util.ObjectUtil;
import com.exhibition.base.vo.BaseVO;
import com.exhibition.domain.sys.Institution;
import com.exhibition.domain.thirdparty.DockingType;
import com.exhibition.domain.thirdparty.FSS.model.entity.FinancialComplementarDate;
import com.exhibition.domain.thirdparty.FSS.model.entity.FinancialIndustrial;
import com.exhibition.domain.thirdparty.FSS.model.req.FssReq;
import com.exhibition.vo.sys.InstitutionVO;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/11/29 11:48
 * @describe: 猫出局接口视图层
 */
@Data
@NoArgsConstructor
@Api("社保局接口")
public class FssVo extends BaseVO implements Serializable {

    private static final long serialVersionUID = 12319112314L;
    /*用户名称*/
    private InstitutionVO institution;
    /*部門名稱*/
    private String dept;
    /*財務納稅人編號*/
    private String dsfNo;
    /*社保雇主注冊編號（必須是10為長度數字符號）*/
    private String fssEmpNo;
    /*開始季度（YYYY,Q=1,2,3,4）*/
    private Integer strYQ;
    /*結束季度（YYYY,Q=1,2,3,4）*/
    private Integer endYQ;
    /*場所登記碼*/
    private String locationRegistrationCode;
    /*財務場所編號*/
    private String financialPremisesCode;
    /*營業情況*/
    private String businessSituation;
    /*所得補充款*/
    private String acquireSupplements;
    /*債務情況*/
    private String debt;
    /*是否已經驗證*/
    private Boolean isVerify;
    /*是否有繳納社保*/
    private Boolean isPaySocialSecurity;
    /*接口請求回來的狀態碼*/
    private String statusCode;
    /*機構id*/
    private Long institutionId;
    /*機構名稱*/
    private String institutionName;
    /*accessToken*/
    private String accessToken;
    // financial专用
    private FinancialIndustrial industrial;
    // financial专用
    private FinancialComplementarDate complementarDate;
    // financial专用
    private String financialStatus;
    //类型
    private DockingType type;
    /*账号*/
    private String account;

}
