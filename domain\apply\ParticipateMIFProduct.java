package com.exhibition.domain.apply;

import java.util.Arrays;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: ExhibitMethod
 * @description: 參展產品
 * @author: ShiXin
 * @create: 2020-03-19 16:22
 **/

public enum ParticipateMIFProduct {


    /**
     * 澳門製造商品
     */
    MADE_IN_MACAO("澳門製造商品"),
    /**
     * 澳門品牌商品
     */
    MACAO_BRAND("澳門品牌商品"),
    /**
     * 本澳企業代理之海外品牌之商品
     */
    MACAO_AGENCY("本澳企業代理之海外品牌之商品"),
    /**
     * 投資環境/項目展示/服務/地產
     */
    INVESTMENT_ENVIRONMENT("投資環境/項目展示/服務/地產"),
    /**
     * 文化創意
     */
    CURTURAL_INNOVATION("文化創意"),
    /**
     * 傢俱
     */
    FURNITURE("傢俱"),
    /**
     * 食品及飲品
     */
    FOOD_AND_DRINK("食品及飲品"),
    /**
     * 電子產品
     */
    ELECTRONIC_PRODUCT("電子產品"),
    /**
     * 中醫藥健康產業
     */
    CHINESE_MEDICINE("中醫藥健康產業"),
    /**
     * 酒店業
     */
    HOTEL_INDUSTRY("酒店業"),
    /**
     * 酒店業
     */
    TOY("玩具、禮品及贈品"),
    /**
     * 政府/公共服務
     */
    GOVERNMENT_SERVICES("政府/公共服務"),
    /**
     * 會展及商務旅遊
     */
    MICE_BUSINESS_TRAVEL("會展及商務旅遊"),
    /**
     * 連鎖加盟
     */
    FRANCHISE("連鎖加盟"),
    /**
     * 數碼影像及攝影器材
     */
    DIGITAL_IMAGING("數碼影像及攝影器材"),
    /**
     * 環境保護
     */
    ENVIRONMENTAL_PROTECTION("環境保護"),
    /**
     * 服務及服飾配件
     */
    ACCESSORIES("服務及服飾配件"),
    /**
     * 電子商務
     */
    E_COMMERCE("電子商務"),
    /**
     * 工藝品及飾物
     */
    CRAFTS_AND_ACCESSORIES("工藝品及飾物"),
    /**
     * 禮品
     */
    GIFT("禮品"),
    /**
     * 食品
     */
    FOOD("食品"),
    /**
     *商匯館之商品
     */
    MACAO_one("商匯館之商品"),
    /**
     *葡語國家食品展示中心之商品
     */
    MACAO_two("葡語國家食品展示中心之商品"),
    /**
     *本澳企業代理境外國家/地區之商品
     */
    MACAO_three("本澳企業代理境外國家/地區之商品"),
    /**
     *橫琴粵澳深度合作區之商品
     */
    MACAO_four("橫琴粵澳深度合作區之商品"),
    /**
     *餐飲業
     */
    ENERGY_EFFICIENCY("餐飲業"),
    /**
     *品牌代理中介
     */
    WASTE_MANAGEMENT_SOLUTIONS("品牌代理中介"),
    /**
     *休閒娛樂業
     */
    WATER("休閒娛樂業"),
    /**
     *零售貿易
     */
    AIR_QUALITY("零售貿易"),
    /**
     *知識產權產業
     */
    ENVIRONMENTAL_PRODUCTS_AND_SERVICES("知識產權產業"),
    /**
     *品牌顧問及設計
     */
    SOIL_REMEDIATION("品牌顧問及設計"),
    /**
     *新零售
     */
    ENVIRONMENTAL_MONITORING("新零售"),
    /**
     *新零售
     */
    GREEN_BUILDING("新零售"),
    /**
     *新零售
     */
    RENEWABLE_ENERGY("新零售"),
    /**
     *新零售
     */
    GREEN_TRANSPORTATION("新零售"),


    /**
     * 其他
     */
    OTHER("其他");


    private final String name;

    ParticipateMIFProduct(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ParticipateMIFProduct getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
