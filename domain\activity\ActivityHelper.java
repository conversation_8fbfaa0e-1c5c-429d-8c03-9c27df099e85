package com.exhibition.domain.activity;

import com.exhibition.domain.apply.Encourage;
import com.exhibition.vo.activity.ActivityHelperVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 活動協助單位
 * @date 2020-02-23
 * @since 1.0.0
 */

@Data
@NoArgsConstructor
@Embeddable
@JsonView(Encourage.EncourageSimpleView.class)
public class ActivityHelper implements Serializable {
    /**
     * 展會活動的協辦單位
     */
    private String      name;

    /**
     * 協辦單位，A；承辦單位,B；支持單位,C
     */
    @Enumerated(EnumType.STRING)
    private ActivityHelperType activityHelperType;

    public ActivityHelper(ActivityHelperVO vo) {
        BeanUtils.copyProperties(vo, this,"activity");
    }

    public ActivityHelperVO toVO() {
       return toVO(false);
    }
    public ActivityHelperVO toVO(boolean includeLazy) {
        ActivityHelperVO vo = new ActivityHelperVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }
}
