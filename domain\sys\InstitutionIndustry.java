package com.exhibition.domain.sys;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.apply.Encourage;
import com.exhibition.vo.sys.InstitutionIndustryVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.sys
 * @ClassName: Industry
 * @description: 行业
 * @author: ShiXin
 * @create: 2020-02-21 14:40
 **/
@Data
@NoArgsConstructor
@Entity
@Table(name = "industry")
public class InstitutionIndustry extends BaseEntity implements Serializable {


    private static final long serialVersionUID = -7232421493167763765L;
    /**
     * 所属机构
     */
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private Institution institution;

    /**
     * 行业名称
     */
    @JsonView(Encourage.EncourageSimpleView.class)
    private String name;



    /**
     * 行业范畴
     */
    @JsonView(Encourage.EncourageSimpleView.class)
    private String category;

    public InstitutionIndustry(InstitutionIndustryVO vo) {
        if (null != vo) {
            BeanUtils.copyProperties(vo, this);
            if (null != vo.getInstitutionId()) {
                institution = new Institution();
                institution.setId(vo.getInstitutionId());
            }
        }
    }

    public InstitutionIndustryVO toVO() {
        return toVO(false);
    }

    public InstitutionIndustryVO toVO(boolean includeLazy) {
        InstitutionIndustryVO vo = new InstitutionIndustryVO();
        BeanUtils.copyProperties(this, vo);
        if (null != institution) {
            vo.setInstitutionId(institution.getId());
            vo.setInstitutionName(institution.getNameZh());
        }
        return vo;
    }
}
