package com.exhibition.domain.apply;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 * <AUTHOR>
 * @desc 031attend參展審批
 * @date 2020-06-07
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Entity
@DiscriminatorValue(Flowtpl.ENCOURAGE_ATTEND_REPORT)
public class AdmTaskEncourageAttendReport extends AdmTask {
    
    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    private EncourageAttendReport  encourageAttendReport;

}