package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.sys.Status;
import com.exhibition.vo.apply.EncourageMissionReportVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;

import javax.persistence.CollectionTable;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_mission_report")
public class EncourageMissionReport extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 64334665435673584L;

    private static final String[] IGNORE_PROPERTIES =
            new String[]{"tasks"};

    /**
     * 接收資助方式
     */
    private EncourageReceive            receive;

    /**
     * 活動成效總結
     */
    @ElementCollection
    @CollectionTable(name = "encourage_mission_report_sumfiles")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert>         summaryFiles;
    /**
     * 收支賬目總結
     */
    @ElementCollection
    @CollectionTable(name = "encourage_mission_report_budgetfiles")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert>         budgetFiles;
    /**
     * 日程
     */
    @ElementCollection
    @CollectionTable(name = "encourage_mission_report_schedulefiles")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert>         scheduleFiles;
    /**
     * 活動實際參團名單
     */
    @ElementCollection
    @CollectionTable(name = "encourage_mission_report_namelist")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert>         nameListFiles;
    /**
     * 展會及展位相片
     */
    @ElementCollection
    @CollectionTable(name = "encourage_mission_report_photofiles")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert>         photoFiles;
    /**
     * 代表名片
     */
    @ElementCollection
    @CollectionTable(name = "encourage_mission_report_cardfiles")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert>         cardFiles;
    /**
     * 潛在客戶
     */
    @ElementCollection
    @CollectionTable(name = "encourage_mission_report_customers")
    @Fetch(FetchMode.SELECT)
    private List<PotentialCustomer>     customers;
    /**
     * 組織成效意向: VSAT 非常滿意,　SAT 滿意,　NOR 普通,　UNSAT 不滿意,　VUNSAT 非常不滿意
     */
    private String                      opinionEffect;
    /**
     * 問卷調查-再次參加意向 选择: 0 会，1 不会， 2 不确定
     */
    private Integer                      choiceOpinionAttendAgain;
    /**
     * 再次參加意向
     */
    private String                      opinionAttendAgain;

    /**
     * 其他意見、建議或希望本局提供之協助
     */
    private String                      otherOpinion;
    /**
     * 其他資助
     */
    private Boolean                      haveOtherSupport;
    /**
     * 其他資助
     */
    private String                      otherSupport;
    /**
     * 代表收費
     */
    private Boolean                      haveChargeExplain;
    /**
     * 代表收費
     */
    private String                      chargeExplain;

    /**
     * 声明 確認
     */
    private Boolean             stateAgree;

    /**
     * 申请日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date                applyTime;
    /**
     * 審核狀態
     */
    @Enumerated(EnumType.STRING)
    private Status              status;
    /**
     * 審批操作記錄
     */
    @OneToMany(mappedBy = "encourageMissionReport", fetch = FetchType.LAZY)
    private List<AdmTaskEncourageMissionReport> tasks;
    /**
     * 展會实体
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "encourage_mission_id", referencedColumnName = "id")
    private EncourageMission              encourageMission;

    public EncourageMissionReport(EncourageMissionReportVO v) {
        BeanUtils.copyProperties(v, this, IGNORE_PROPERTIES);
        if (v.getEncourageMissionId() != null) {
            EncourageMission ea = new EncourageMission();
            ea.setId(v.getEncourageMissionId());
            this.setEncourageMission(ea);
        }
    }

    public EncourageMissionReportVO toVO() {
        EncourageMissionReportVO vo = new EncourageMissionReportVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);
        if (vo.getTasks() == null && this.getTasks() != null) {
            vo.setTasks(this.getTasks().stream().map(AdmTask::toSimpleVO).collect(Collectors.toList()));
        }
        return vo;
    }
}