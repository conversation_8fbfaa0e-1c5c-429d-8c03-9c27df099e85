package com.exhibition.domain.apply;

import com.exhibition.vo.apply.EncourageConventionRelatedTradeVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Embeddable
public class EncourageConventionRelatedTrade implements Serializable {

    //供應商名稱
    @Column(length = 500)
    private String vendorName;

    //供應商與申請者之關係
    @Column(length = 20)
    private String vendorAndApplicantsRelated;

    //採購產品或服務內容
    @Column(length = 500)
    private String serviceContents;

    @Column(length = 500)
    private String explaination;

    @Column(length = 500)
    private String address;

    @Column(length = 500)
    private String accountingCategory;

    @Column(length = 500)
    private Double amount;

    @Column(length = 500)
    private String invoiceNo;

    @Column(length = 500)
    private String relatedPartyPositionOnCounterparty;

    @Column(length = 500)
    private String relatedPartyName;

    public EncourageConventionRelatedTrade(EncourageConventionRelatedTradeVO vo) {
        BeanUtils.copyProperties(vo,this);

    }

    public EncourageConventionRelatedTradeVO toVO() {
        return toVO(false);
    }

    public EncourageConventionRelatedTradeVO toVO(boolean includeLazy) {
        EncourageConventionRelatedTradeVO vo = new EncourageConventionRelatedTradeVO();
        BeanUtils.copyProperties(this, vo);
        return vo;
    }
}
