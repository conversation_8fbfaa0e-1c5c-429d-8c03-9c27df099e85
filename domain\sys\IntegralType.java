package com.exhibition.domain.sys;

import com.exhibition.domain.apply.Encourage;
import com.exhibition.exception.RenException;
import com.fasterxml.jackson.annotation.JsonView;

import java.util.Arrays;

@JsonView(Encourage.EncourageSimpleView.class)
public enum IntegralType {

    /**
     * 增加
     */
    increase("增加"),
    /**
     * 扣除
     */
    deduct("扣除");


    private final String name;

    IntegralType(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static IntegralType getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }

    public static IntegralType getEnumFromString(String string) {
        if (string != null) {
            try {
                return Enum.valueOf(IntegralType.class, string.trim());
            } catch (IllegalArgumentException e) {
                throw new RenException(e.getMessage());
            }
        }
        return null;
    }
}
