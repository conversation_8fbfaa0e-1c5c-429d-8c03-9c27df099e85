package com.exhibition.domain.activity;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.apply.Encourage;
import com.exhibition.domain.apply.ParticipateCert;
import com.exhibition.domain.apply.ParticipateMethod;
import com.exhibition.domain.sys.CommonConfig;
import com.exhibition.domain.sys.Institution;
import com.exhibition.domain.sys.InstitutionShareholder;
import com.exhibition.util.dataInit.DataInitializer;
import com.exhibition.vo.activity.ActivityOperateVO;
import com.exhibition.vo.activity.ActivitySimpleVO;
import com.exhibition.vo.activity.ActivityVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 活動
 * @date 2020-02-23
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "activity")
@JsonView(Encourage.EncourageSimpleView.class)
public class Activity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -649343549215892898L;

    private static final String[] IGNORE_PROPERTIES =
            new String[]{"organizers", "helpers", "liaisons", "follow","expenses", "cancels", "pictures",
                    "methods"};
    private static final String[] IGNORE_PROPERTIES_CONTENT =
            new String[]{"organizers", "helpers", "liaisons", "follow","expenses", "cancels", "pictures", "methods",
                    "contentZh", "contentEn", "contentPt"};
    /**
     * 展會編號（唯一）
     */
    //@Column(nullable = false, unique = true, length = 100)
    @Column(length = 10)
    private String code;
    /**
     * 展會活動名稱（中文）
     */
    private String nameZh;
    /**
     * 展會活動名稱（英文）
     */
    private String nameEn;
    /**
     * 展會活動名稱（葡文）
     */
    private String namePt;
    /**
     * 展會類别
     */
    @ElementCollection
    @CollectionTable(name = "activity_trade")
    @Fetch(FetchMode.SELECT)
    @Enumerated(EnumType.STRING)
    private List<ActivityTrade> trades;
    /**
     * 展會活動類別：其他的輸入類別
     */
    private String otherText;
    /**
     * LOCAL:本局主辦之展會,OUTLANDS:本局組織之境外展會,ENCOURAGE:支持及鼓勵措施
     */
    @Enumerated(EnumType.STRING)
    private ActivityScope activityScope;
    /**
     * 展會申請類型
     */
    @ElementCollection
    @CollectionTable(name = "activity_apply_type")
    @Enumerated(EnumType.STRING)
    private List<ActivityApplyType> types;
    /**
     * 舉辦地點
     */
    private String place;
    /**
     * 舉辦城市
     */
    private String city;
    /**
     * 報名截止日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date expiryTime;
    /**
     * 是否截止報名
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean expiry;
    /**
     * 是否可以報名
     */
    @Transient
    private Boolean participate;

    /**
     * 展會開始日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date startTime;
    /**
     * 展會結束日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date endTime;

    /**
     * 主辦官方網站
     */
    private String website;
    /**
     * 展费
     */
    private Double cost;
    /**
     * 主辦單位
     */
    @ElementCollection
    @CollectionTable(name = "activity_organizer")
    @Fetch(FetchMode.SELECT)
    private List<ActivityOrganizer> organizers;
    /**
     * 協助單位
     */
    @ElementCollection
    @CollectionTable(name = "activity_helper")
    @Fetch(FetchMode.SELECT)
    private List<ActivityHelper> helpers;


    /**
     * 联络电话
     */
    private String tel;
    /**
     * 展會活動説明(中文)
     */
    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(name = "contentZh", nullable = true)
    private String contentZh;
    /**
     * 展會活動説明（英文）
     */
    @Lob
    @Basic(fetch = FetchType.LAZY)
    private String contentEn;
    /**
     * 展會活動説明（葡文）
     */
    @Lob
    @Basic(fetch = FetchType.LAZY)
    private String contentPt;


    /**
     * 参会须知(中文)
     */
    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(name = "attendingZh", nullable = true)
    private String attendingZh;
    /**
     * 参会须知(英文)
     */
    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(name = "attendingEn", nullable = true)
    private String attendingEn;
    /**
     * 参会须知(葡文)
     */
    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(name = "attendingPT", nullable = true)
    private String attendingPt;
    /**
     * 展會備注
     */
    @Column(columnDefinition = "text")
    private String comment;
    /**
     * 展會聯絡人
     */
    @ElementCollection
    @CollectionTable(name = "activity_liaison")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<ActivityLiaison> liaisons;

    /**
     * 登記人電話
     */
    @Column(columnDefinition = "text")
    private String regPersonPhone;

    /**
     * 登記人姓名
     */
    @Column(columnDefinition = "text")
    private String regPersonName;
    /**
     * 登記人机构
     */
    @Column(columnDefinition = "text")
    private String regPersonInsitution;
    /**
     * 登記人zhiwei
     */
    @Column(columnDefinition = "text")
    private String regPersonTitle;

    /**
     * 机构id
     */
    private Long institutionId;

    /**
     * 招展書/展會簡介
     */
    @ElementCollection
    @CollectionTable(name = "activity_brief_appendix")
    @Fetch(FetchMode.SELECT)
    private List<ActivityAppendix> brief;


    /**
     * 招商條件
     */
    @ElementCollection
    @CollectionTable(name = "activity_requirement_appendix")
    @Fetch(FetchMode.SELECT)
    private List<ActivityAppendix> requirement;

    /**
     * 展會規模
     */
    @ElementCollection
    @CollectionTable(name = "activity_scope_appendix")
    @Fetch(FetchMode.SELECT)
    private List<ActivityAppendix> scopeAppendix;
    /**
     * 展會性質聲明書
     */
    @ElementCollection
    @CollectionTable(name = "activity_statement")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> statement;

    /**
     * 展會平面圖
     */
    @ElementCollection
    @CollectionTable(name = "activity_exhibition_plan")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> exhibitionPlan;

    /**
     * 展商名單
     */
    @ElementCollection
    @CollectionTable(name = "activity_exhibition_list")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> exhibitorList;

    /**
     * 活動日程
     */
    @ElementCollection
    @CollectionTable(name = "activity_schedule")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> schedule;



    /**
     * 展位價格
     */
    @ElementCollection
    @CollectionTable(name = "activity_price_appendix")
    @Fetch(FetchMode.SELECT)
    private List<ActivityAppendix> price;
    /**
     * 线上展览的网址、附有列印日期及网址的展览界面
     */
    @ElementCollection
    @CollectionTable(name = "activity_online_files")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> onlineFiles;
    /**
     * 往届访客/下载量的地域分析数据
     */
    @ElementCollection
    @CollectionTable(name = "activity_download_files")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> downloadFiles;


    /**
     * 展會登記用戶id
     */
    private Long adminId;

    /**
     * 展會配圖
     */
    private String scope;
    /**
     * 是否用戶端重點活動
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean momentous;
    /**
     * 申請機構數
     */
    private Integer applyNum;
    /**
     * 展会狀態(仅用于展示和查询)，UNPUBLISH:未發佈,NOTSTART:未開始,PROGRESS:進行中,END:已結束,CANCEL:已取消
     */
    @Transient
    private ActivityStatus showStatus;
    /**
     * 展会狀態，UNPUBLISH:不發佈,PUBLISH:發佈,CANCEL:取消
     */
    @Enumerated(EnumType.STRING)
    private ActivityStatus status;
    /**
     * 展會发布时间
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date publishTime;
    /**
     * 取消憑證
     */
    @ElementCollection
    @CollectionTable(name = "activity_cancel")
    @Fetch(FetchMode.SELECT)
    private List<ActivityCancel> cancels;
    /**
     * 展會相冊
     */
    @OneToMany
    @BatchSize(size = 20)
    private List<ApproveImage> pictures;
    /**
     * 展会活动的商家
     */
    @Transient
    private List<Institution> institutions;

    /**
     * 标识展会有参展方式级展会问卷的参展方式（仅前端新建展会问卷选择展会使用）
     */
    @Transient
    private List<ParticipateMethod> methods;

    /**
     * 以下字段仅用于资料库端
     * @param vo
     */
    /**
     * 展場面積（平方米）
     */
    @Column(columnDefinition = "text")
    private String activityArea;

    /**
     * 展位數目
     */
    @Column(columnDefinition = "text")
    private String activityAmount;

    /**
     * 展場面積（平方米）
     */
    private String displayArea;

    /**
     * 展位數目
     */
    private String displayAmount;

    /**
     * 參展商數目
     */
    @Column(columnDefinition = "text")
    private String exhibitorAmount;

    /**
     * 參展商地區
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String exhibitorRegion;

    /**
     * 入場人次
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String entrancePerson;


    /**
     * 副部級以上官員參與人數
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String officerPerson;

    /**
     * 具決策力人士（泛指以高管等級以上人士計算）
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String policymaker;

    /**
     * 專業觀眾人數
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String majorAudience;

    /**
     * 商業配對場次、商務會談（基建適用）
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String businessTalk;

    /**
     * 簽約項目數
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String signedProject;

    /**
     * 參會者留澳平均消費金額
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String averageConsumption;

    /**
     * 活動平均滿意度
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String degreeOfSatisfaction;

    /**
     * 預算金額（澳門元）
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String budget;


    /**
     * 最終結算金額（澳門元）
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String finalBudget;

    /**
     * 總收入（澳門元）
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String income;

    /**
     * 參展費用
     *
     * @param vo
     */
    private Double exhibitionCost;

    /**
     * 參展報名截止日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date exhibitionEndTime;

    /**
     * 參團報名截止日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date uxedoEndTime;

    /**
     * 參展補交文件日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date exhibitionFileEndTime;

    /**
     * 參展產品/服務類別
     */
    @ElementCollection
    private List<String> exhibit;
    /**
     * 參團費用
     *
     * @param vo
     */
    private Double tuxedoCost;

    /**
     * 參展費用說明（中）
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String exhibitionCostExplain;

    /**
     * 參展費用說明（英）
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String exhibitionCostExplainEn;

    /**
     * 參展費用說明（葡）
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String exhibitionCostExplainPn;

    /**
     * 导数据专用数据
     */
    //参展商数量
    private String numberOfExhibitors;
    //代理葡语国家产品
    private String agentCountries;
    //澳门制造澳门品牌
    private String madeInMacao;
    //参展企业类puyuguoji
    private String typesOfExhibitors;
    //葡语国家食品展示中心展品数量
    private String numberOfExhibits;
    //展品涉及企业数量
    private String numberOfEnterprises;
    //产品扫码总次数
    private String numberOfProductCodeSweeps;
    //回收问卷数量
    private String numberOfQuestionnairesReturned;
    //流动满意度评分
    private String flowSatisfactionScore;
    //预计销售总金额
    private String estimatedTotalSalesAmount;
    //现场实际销售总金额
    private String amountOfActualSalesOnSite;
    //现场实际交易宗数
    private String actualNumberOfTransactionsOnSite;
    //洽谈企业数量
    private String numberOfEnterprisesToNegotiate;
    //洽谈涉及金额
    private String amountInvolvedInNegotiation;
    //建立新商务联系数量
    private String businessRelation;
    //经贸代表团人数
    private String numberOfTradeDelegations;
    //回收问卷数量(代表团)
    private String numberByDelegation;
    //流动满意度评分(代表团)
    private String flowByDelegation;
    //投资者一站式服务查询数量
    private String theNumberOfQueries;
    //会展竞投及支援一站式服务查询数量
    private String exhibitionBidding;
    //推介活动名称1
    private String promotionActivityName;
    //出席人数
    private String attendance;
    //达成洽谈场次
    private String reachNegotiationSessions;
    //推介活动名称2
    private String promotionActivityNameTwo;
    //出席人数2
    private String attendanceTwo;
    //达成洽谈场次2
    private String reachNegotiationSessionsTwo;
    //本局线上展台浏览量
    private String pageView;
    //本局线上展台接获查询次数
    private String numberOfQueries;
    //参展企业线上展台浏览总量
    private String browseTheTotal;
    //参展企业线上展台接获查询总次数
    private String totalNumberOfQueries;
    //澳门馆展位租金
    private String boothRentalOfMacauPavilion;
    //澳门馆设计及搭建服务金额
    private String theServiceAmount;
    //参展商统筹服务金额
    private String aggregateServiceAmount;
    //活动整体费用
    private String overallCostOfTheEvent;

    /**
     * 国家/地区（中文）
     */
    private String countryZh;
    /**
     * 省份（中文）
     */
    private String provinceZh;
    /**
     * 城市（中文）
     */
    private String cityZh;

    /**
     * 是否组团
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isCluster;
    /**
     * 簽注函
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean existQ;
    /**
     * 是否重複活動
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isRepeat;

    /**
     * 限定受邀企业参展
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean limitedEnterprise;
    /**
     * 是否本地（true:本地\false:境外)
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean fairType;
    /**
     * 01參會
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean localMeeting;
    /**
     * 01S&P計劃
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean localSP;
    /**
     * 中小企業
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean localMini;


    @Column(columnDefinition = "text")
    private String placeEn;
    @Column(columnDefinition = "text")
    private String sposor;
    @Column(columnDefinition = "text")
    private String sposorEn;
    @Column(columnDefinition = "text")
    private String organizer;
    @Column(columnDefinition = "text")
    private String organizerEn;
    @Column(columnDefinition = "text")
    private String coorganizer;
    @Column(columnDefinition = "text")
    private String coorganizerEn;
    @Column(columnDefinition = "text")
    private String support;
    @Column(columnDefinition = "text")
    private String supportEn;
    /**
     * 展覽形式
     */
 /*   @ElementCollection
    private List<String> activityline;*/
    /**
     * 年
     */
    private Integer onestepyear;
    /**
     * 序號
     */
    private Integer serialNumber;
    /**
     * 活動名稱涉及地區
     */
    @Column(columnDefinition = "text")
    private String region;
    /**
     * 活動名稱涉及地區
     */
    @ElementCollection
    private List<String> activityregion;
    /**
     * aff
     * 主辦方屬性
     */
    @Column(columnDefinition = "text")
    private String sponsorPrope;
    /**
     * 國際認證
     */
    @Column(columnDefinition = "text")
    private String interCert;
    /**
     * 跟進途徑
     */
    @Column(columnDefinition = "text")
    private String followPath;
    /**
     * 活動狀態
     */
    @Column(columnDefinition = "text")
    private String activeState;
    /**
     * 取消/延期
     */
    @Column(columnDefinition = "text")
    private String cancel;
    /**
     * 狀態更新日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date stateUpdDate;
    /**
     * 活動類別
     */
    @Column(columnDefinition = "text")
    private String type;
    /**
     * 產業類別
     */
    @Column(columnDefinition = "text")
    private String industry;
    /**
     * 主題類別
     */
    @Column(columnDefinition = "text")
    private String theme;
    /**
     * 跟進來源
     */
    @ElementCollection
    private List<String> followSour;
    /**
     * 本局身分
     */
    @ElementCollection
    private List<String> localIcardIPIM;
    /**
     * 引進在澳舉辦形式(一站式跟進活動)
     */
    @Column(columnDefinition = "text")
    private String introduce;
    /**
     * 預計規模/具體規模
     */
    @Column(columnDefinition = "text")
    private String scale;
    /**
     * 會議規模
     */
    @Column(columnDefinition = "text")
    private String meetingSize;
    /**
     * 本局提供之協議
     */
    @Column(columnDefinition = "text")
    private String agreement;
    /**
     * 跟进情况
     */
    @Column(columnDefinition = "text")
    private String followsituation;
    /**
     * 展會跟進
     */
    @ElementCollection
    @CollectionTable(name = "activity_follow")
    @Fetch(FetchMode.SELECT)
    private List<ActivityFollow> follow;
    /**
     * 展會跟進
     */
    @ElementCollection
    @CollectionTable(name = "activity_venue")
    @Fetch(FetchMode.SELECT)
    private List<ActivityVenue> venue;
    /**
     * googleDriveplaceName
     */
    private Boolean googleDrive;
    /**
     * 一会展两地
     */
    private Boolean exhibitionPlaces;
    /**
     * 活動方式：線上、線下、線上及線下
     */
    @Column(columnDefinition = "text")
    private String activityLine;
    /**
     * 會議及展覽資助計劃
     */
    @Column(columnDefinition = "text")
    private String fundScheme;
    /**
     * 參展財務鼓勵
     */
    @Column(columnDefinition = "text")
    private String hearten;
    /**
     * 特別資助活動
     */
    @Column(columnDefinition = "text")
    private String fundActivity;
    /**
     * 主/承/協辦
     */
    @Column(columnDefinition = "text")
    private String sponsor;
    /**
     * 支持單位
     */
    @Column(columnDefinition = "text")
    private String supportUnit;
    /**
     * 出席活動
     */
    @Column(columnDefinition = "text")
    private String events;
    /**
     * 其他
     */
    @Column(columnDefinition = "text")
    private String rest;
    /**
     * 架上位置
     */
    @Column(columnDefinition = "text")
    private String seat;
    /**
     * 檔案形態
     */
    @Column(columnDefinition = "text")
    private String fileForm;
    /**
     * 語音
     */
    @Column(columnDefinition = "text")
    private String language;
    /**
     * 涵蓋日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date coverDate;
    /**
     * 卷/冊數
     */
    @Column(columnDefinition = "text")
    private String reel;
    /**
     * 檔案分類號
     */
    @Column(columnDefinition = "text")
    private String fileTypeNum;
    /**
     * 保存期
     */
    @Column(columnDefinition = "text")
    private String saveDate;
    /**
     * 保存期屆滿日期
     */
    @Column(columnDefinition = "text")
    private String saveFull;
    /**
     * 檔案保管人
     */
    @Column(columnDefinition = "text")
    private String archivist;
    /**
     * 進入社區安排 (是/否)
     */
    private Boolean enteringthecommunity;

    /**
     * 進入社區目的 (觀光、團隊建設活動、其他) 下拉選項
     */
    @Column(columnDefinition = "text")
    private String enteringPurpose;

    /**
     * 進入社區人數 (數字格式)
     */
    @Column(columnDefinition = "text")
    private String enteringNumber;

    /**
     * 本局參與落區 (是/否)
     */
    private Boolean enteringIpim;
    /**
     * 啟用中銀線上支付
     */
    private Boolean isBocPay;
    /**
     * 舉辦地點詳情
     */
    @Column(columnDefinition = "VARCHAR(100)")
    private String venueDetail;

    /**
     * 本局提供之協助
     */
    @ElementCollection
    private List<String> localProvideAssist;
    /**
     * 關聯展會ID
     */
    private Long associateExhibitionId;
    /**
     * 關聯展會名稱
     */
    @Column(columnDefinition = "VARCHAR(200)")
    private String associateExhibitionName;

    @ElementCollection
    @CollectionTable(name = "activity_expenses")
    @Fetch(FetchMode.SELECT)
    private List<ActivityExpenses> expenses;

    /**
     * 參加單位
     */
    @ElementCollection
    @CollectionTable(name = "activity_participation")
    @Fetch(FetchMode.SELECT)
    private List<ActivityParticipation> participation;

    /**
     * 是否使用邀请码
     */
    private Boolean isInvite;

    /**
     * 是否為CEPA
     */
    private Boolean isCEPA;

    /**
     * 是不是會議大使跟進活動
     */
    private Boolean isConferenceMessenger;

    @ElementCollection
    private List<String> activityConferenceMessenger;

    public Activity(ActivityVO vo) {
        BeanUtils.copyProperties(vo, this, IGNORE_PROPERTIES);

        if (!CollectionUtils.isEmpty(vo.getOrganizers())) {
            List<ActivityOrganizer> collect =
                    vo.getOrganizers().stream().map(ActivityOrganizer::new).collect(Collectors.toList());
            this.setOrganizers(collect);
        }

        if (!CollectionUtils.isEmpty(vo.getHelpers())) {
            List<ActivityHelper> collect =
                    vo.getHelpers().stream().map(ActivityHelper::new).collect(Collectors.toList());
            this.setHelpers(collect);
        }
        if (!CollectionUtils.isEmpty(vo.getExpenses())){
            List<ActivityExpenses> collect =
                    vo.getExpenses().stream().map(ActivityExpenses::new).collect(Collectors.toList());
            this.setExpenses(collect);
        }
        if (!CollectionUtils.isEmpty(vo.getFollow())) {
            List<ActivityFollow> collect =
                    vo.getFollow().stream().map(ActivityFollow::new).collect(Collectors.toList());
            this.setFollow(collect);
        }

        if (!CollectionUtils.isEmpty(vo.getVenue())) {
            List<ActivityVenue> collect =
                    vo.getVenue().stream().map(ActivityVenue::new).collect(Collectors.toList());
            this.setVenue(collect);
        }

        if (!CollectionUtils.isEmpty(vo.getLiaisons())) {
            List<ActivityLiaison> collect =
                    vo.getLiaisons().stream().map(ActivityLiaison::new).collect(Collectors.toList());
            this.setLiaisons(collect);
        }

        if (!CollectionUtils.isEmpty(vo.getCancels())) {
            List<ActivityCancel> collect =
                    vo.getCancels().stream().map(ActivityCancel::new).collect(Collectors.toList());
            this.setCancels(collect);
        }

        if (!CollectionUtils.isEmpty(vo.getBrief())) {
            List<ActivityAppendix> collect =
                    vo.getBrief().stream().map(ActivityAppendix::new).collect(Collectors.toList());
            this.setBrief(collect);
        }

        if (!CollectionUtils.isEmpty(vo.getRequirement())) {
            List<ActivityAppendix> collect =
                    vo.getRequirement().stream().map(ActivityAppendix::new).collect(Collectors.toList());
            this.setRequirement(collect);
        }

        if (!CollectionUtils.isEmpty(vo.getScopeAppendix())) {
            List<ActivityAppendix> collect =
                    vo.getScopeAppendix().stream().map(ActivityAppendix::new).collect(Collectors.toList());
            this.setScopeAppendix(collect);
        }

        if (!CollectionUtils.isEmpty(vo.getPrice())) {
            List<ActivityAppendix> collect =
                    vo.getPrice().stream().map(ActivityAppendix::new).collect(Collectors.toList());
            this.setPrice(collect);
        }
        if (!CollectionUtils.isEmpty(vo.getExhibitionPlan())) {
            List<ParticipateCert> collect =
                    vo.getExhibitionPlan().stream().map(ParticipateCert::new).collect(Collectors.toList());
            this.setExhibitionPlan(collect);
        }
        if (!CollectionUtils.isEmpty(vo.getExhibitorList())) {
            List<ParticipateCert> collect =
                    vo.getExhibitorList().stream().map(ParticipateCert::new).collect(Collectors.toList());
            this.setExhibitorList(collect);
        }
        if (!CollectionUtils.isEmpty(vo.getSchedule())) {
            List<ParticipateCert> collect =
                    vo.getSchedule().stream().map(ParticipateCert::new).collect(Collectors.toList());
            this.setSchedule(collect);
        }
        if (!CollectionUtils.isEmpty(vo.getParticipation())) {
            List<ActivityParticipation> collect =
                    vo.getParticipation().stream().map(ActivityParticipation::new).collect(Collectors.toList());
            this.setParticipation(collect);
        }


    }

    public Activity(ActivityOperateVO vo) {
        BeanUtils.copyProperties(
                vo, this);

        if (!CollectionUtils.isEmpty(vo.getCancels())) {
            List<ActivityCancel> collect =
                    vo.getCancels().stream().map(ActivityCancel::new).collect(Collectors.toList());
            this.setCancels(collect);
        } else {
            this.setCancels(Collections.emptyList());
        }
    }

    public ActivityVO toVO() {
        return toVO(false);
    }

    public ActivityVO toVO(boolean includeLazy) {
        ActivityVO vo = new ActivityVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);
        if (vo != null && org.apache.commons.lang3.StringUtils.isNotEmpty(vo.getPlace())) {
            List<CommonConfig> venue = DataInitializer.getConfigByCode(vo.getPlace());
            CommonConfig commonConfig = CollectionUtils.isNotEmpty(venue) ? venue.get(0) : null;
            vo.setPlaceName(commonConfig != null ? commonConfig.getName() : vo.getPlace());
            vo.setPlaceNameEn(commonConfig != null ? commonConfig.getNameEn() : vo.getPlace());
            vo.setPlaceNamePt(commonConfig != null ? commonConfig.getNamePt() : vo.getPlace());
        }
        if (!CollectionUtils.isEmpty(participation)) {
            vo.setParticipation(participation.stream().map(ActivityParticipation::toVO).collect(Collectors.toList()));
        }
        if (includeLazy) {
            if (!CollectionUtils.isEmpty(organizers)) {
                vo.setOrganizers(
                        organizers.stream().map(ActivityOrganizer::toVO).collect(Collectors.toList()));
            }else{

                ActivityOrganizer activityOrganizer = new ActivityOrganizer();
                activityOrganizer.setName("");
                organizers.add(activityOrganizer);
            }

            if (!CollectionUtils.isEmpty(helpers)) {
                vo.setHelpers(helpers.stream().map(ActivityHelper::toVO).collect(Collectors.toList()));
            }else{

                ActivityHelper activityHelper = new ActivityHelper();
                activityHelper.setName("");
                activityHelper.setActivityHelperType(ActivityHelperType.A);
                helpers.add(activityHelper);
            }
            if (!CollectionUtils.isEmpty(exhibitionPlan)) {
                vo.setExhibitionPlan(exhibitionPlan.stream().map(ParticipateCert::toVO).collect(Collectors.toList()));

            }
            if (!CollectionUtils.isEmpty(exhibitorList)) {
                vo.setExhibitorList(exhibitorList.stream().map(ParticipateCert::toVO).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(schedule)){
                vo.setSchedule(schedule.stream().map(ParticipateCert::toVO).collect(Collectors.toList()));
            }

            if (!CollectionUtils.isEmpty(follow)) {
                vo.setFollow(follow.stream().map(ActivityFollow::toVO).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(expenses)) {
                vo.setExpenses(expenses.stream().map(ActivityExpenses::toVO).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(venue)) {
                vo.setVenue(venue.stream().map(ActivityVenue::toVO).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(liaisons)) {
                vo.setLiaisons(liaisons.stream().map(ActivityLiaison::toVO).collect(Collectors.toList()));
            }

            if (!CollectionUtils.isEmpty(cancels)) {
                vo.setCancels(cancels.stream().map(ActivityCancel::toVO).collect(Collectors.toList()));
            }

            if (!CollectionUtils.isEmpty(pictures)) {
                vo.setPictures(pictures.stream().map(ApproveImage::toVO).collect(Collectors.toList()));
            }

            if (!CollectionUtils.isEmpty(institutions)) {
                vo.setInstitutions(institutions.stream().map(Institution::toSimpleVO).collect(Collectors.toList()));
            }

            if (StringUtils.isNotEmpty(contentZh)) {
                vo.setContentZh(contentZh);
            }

            if (StringUtils.isNotEmpty(contentEn)) {
                vo.setContentEn(contentEn);
            }

            if (StringUtils.isNotEmpty(contentPt)) {
                vo.setContentPt(contentPt);
            }

        }

        return vo;
    }

    public ActivitySimpleVO toSimpleVO() {
        ActivitySimpleVO vo = new ActivitySimpleVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES_CONTENT);

        if (!CollectionUtils.isEmpty(participation)) {
            vo.setParticipation(participation.stream().map(ActivityParticipation::toVO).collect(Collectors.toList()));

        }
        if (!CollectionUtils.isEmpty(cancels)) {
            vo.setCancels(
                    cancels.stream().map(ActivityCancel::toVO).collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(exhibitionPlan)) {
            vo.setExhibitionPlan(exhibitionPlan.stream().map(ParticipateCert::toVO).collect(Collectors.toList()));

        }
        if (!CollectionUtils.isEmpty(exhibitorList)) {
            vo.setExhibitorList(exhibitorList.stream().map(ParticipateCert::toVO).collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(schedule)){
            vo.setSchedule(schedule.stream().map(ParticipateCert::toVO).collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(methods)) {
            vo.setMethods(methods);
        } else {
            vo.setMethods(Collections.emptyList());
        }
        if (!CollectionUtils.isEmpty(organizers)) {
            String organizerName = "";
            for (ActivityOrganizer organizer : organizers) {
                organizerName += organizer.getName() + ",";
            }
            vo.setOrganizerStrs(organizerName.substring(0, organizerName.length() - 1).trim());
        }
        if (!CollectionUtils.isEmpty(helpers)) {
            String helpName = "";
            for (ActivityHelper helper : helpers) {
                helpName += helper.getName() + ",";
            }
            vo.setHelperStrs(helpName.substring(0, helpName.length() - 1).trim());
        }

        if (vo != null && org.apache.commons.lang3.StringUtils.isNotEmpty(vo.getPlace())) {
            List<CommonConfig> venue = DataInitializer.getConfigByCode(vo.getPlace());
            CommonConfig commonConfig = CollectionUtils.isNotEmpty(venue) ? venue.get(0) : null;
            vo.setPlaceName(commonConfig != null ? commonConfig.getName() : vo.getPlace());
            vo.setPlaceNameEn(commonConfig != null ? commonConfig.getNameEn() : vo.getPlace());
            vo.setPlaceNamePt(commonConfig != null ? commonConfig.getNamePt() : vo.getPlace());
        }

        return vo;
    }
}
