package com.exhibition.domain.apply;

import java.util.Arrays;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: ExhibitMethod
 * @description: 參展方式
 * @author: ShiXin
 * @create: 2020-03-19 16:22
 **/
public enum ParticipateExhibitBoothPreference {

    /**
     * 光地
     */
    BARELY("光地"),

    /**
     * 標準展位
     */
    STANDARD_BOOTH("標準展位"),
    /**
     * 標準展位
     */
    IP("6平方展位");

    private final String name;

    ParticipateExhibitBoothPreference(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ParticipateExhibitBoothPreference getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
