package com.exhibition.domain.sys;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Data
@NoArgsConstructor
@Entity
@Table(name = "loginlog")
public class Loginlog {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long   id;
    /**
     * 用户
     */
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private User   user;
    /**
     * 登录令牌
     */
    private String token;
    /**
     * 当前登录端
     */
    private String agent;
    /**
     * 登录时间
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date   loginTime;
    /**
     * 退出时间
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date   logoutTime;
}
