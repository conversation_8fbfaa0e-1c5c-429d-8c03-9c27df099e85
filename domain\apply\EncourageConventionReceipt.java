package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.sys.User;
import com.exhibition.vo.apply.EncourageConventionReceiptVO;
import com.exhibition.vo.apply.EncourageEnterpriseReceiptVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @desc 034會議及展覽資助計劃
 * @date 2023-10-21
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_convention_receipt")
public class EncourageConventionReceipt extends BaseEntity implements Serializable {
  
    private static final long serialVersionUID = 6181518784344931826L;
  
    private static final String[] IGNORE_PROPERTIES =
            new String[]{ "encourageConvention"};

    @Column(length = 10)
    private String file1;
    @Column(length = 10)
    private String file21;
    @Column(length = 10)
    private String file22;
    @Column(length = 10)
    private String file23;
    @Column(length = 10)
    private String file24;
    @Column(length = 10)
    private String file25;
    @Column(length = 10)
    private String file31;
    @Column(length = 10)
    private String file32;
    @Column(length = 10)
    private String file33;
    @Column(length = 10)
    private String file41;
    @Column(length = 10)
    private String file42;
    @Column(length = 10)
    private String file43;
    @Column(length = 10)
    private String file5;
    @Column(length = 10)
    private String file6;
    @Column(length = 10)
    private String file7;
    @Column(length = 10)
    private String file8;
    @Column(length = 10)
    private String file9;
    @Column(length = 10)
    private String file10;
    @Column(length = 10)
    private String file11;
    @Column(length = 10)
    private String file12;
    @Column(length = 10)
    private String file13;
    @Column(length = 10)
    private String file14;
    @Column(length = 10)
    private String file15;
    @Column(length = 10)
    private String file16;
    @Column(length = 10)
    private String file17;
    @Column(length = 10)
    private String file18;
    @Column(length = 10)
    private String file19;


    /**
     * 記錄日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date                   recordTime;
    private String                   memo;
    /**
     * 管理员
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private User recordUser;
    /**
     * 展會实体
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "encourage_convention_id", referencedColumnName = "id")
    private EncourageConvention encourageConvention;

    /**
     * 備注字段
     */
    @Column(columnDefinition = "VARCHAR(500) default ''")
    private String remark;

    public EncourageConventionReceipt(EncourageConventionReceiptVO v) {
        BeanUtils.copyProperties(v, this, IGNORE_PROPERTIES);
        if (v.getEncourageConventionId() != null) {
            EncourageConvention ee = new EncourageConvention();
            ee.setId(v.getEncourageConventionId());
            this.setEncourageConvention(ee);
        }
        if (null != v.getRecordUserId()) {
            User applicant = new User();
            applicant.setId(v.getRecordUserId());
            this.setRecordUser(applicant);
        }
    }

    public EncourageConventionReceiptVO toVO() {
        EncourageConventionReceiptVO vo = new EncourageConventionReceiptVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);

        // 展後報告
        EncourageConvention encourageConvention = this.getEncourageConvention();
        if (null != encourageConvention) {
            vo.setEncourageConventionVO(encourageConvention.toVO());
            vo.setEncourageConventionId(encourageConvention.getId());
        }
         //发起人
        User recordUser = this.getRecordUser();
        if (null != recordUser) {
            vo.setRecordUserId(recordUser.getId());
            vo.setRecordUserName(recordUser.getName()+recordUser.getLastName()+"("+recordUser.getAccount()+")");
            vo.setRecordUserAccount(recordUser.getAccount());
        }
        return vo;
    }

}