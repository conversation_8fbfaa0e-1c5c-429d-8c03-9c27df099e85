package com.exhibition.domain.apply;

import com.exhibition.domain.sys.Call;
import com.exhibition.domain.sys.Onlineoroffline;
import com.exhibition.vo.apply.ParticipateSpInvitedSpeakerVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Embeddable
public class ParticipateSpInvitedSpeaker implements Serializable {

    //姓名
    private String name;
    //職位
    private String position;
    //機構
    private String institutionName;
    //國籍
    private String nationality;
    /**
     * 稱呼：先生/女士
     */
    @Enumerated(value = EnumType.STRING)
    private Call calls;
    /**
     * 演講模式 ：線上或者線下
     */
    @Enumerated(EnumType.STRING)
    private Onlineoroffline speechMode;
    //演講語言
    private String speechLanguage;
    //演講題目
    private String speechTopic;
    //演講内容簡介
    @Column(columnDefinition = "text")
    private String speechContent;

    public ParticipateSpInvitedSpeaker(ParticipateSpInvitedSpeakerVO vo) {
        BeanUtils.copyProperties(vo,this);

    }

    public ParticipateSpInvitedSpeakerVO toVO() {
        return toVO(false);
    }

    public ParticipateSpInvitedSpeakerVO toVO(boolean includeLazy) {
        ParticipateSpInvitedSpeakerVO vo = new ParticipateSpInvitedSpeakerVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }
}
