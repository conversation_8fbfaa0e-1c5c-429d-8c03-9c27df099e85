/**
 * Copyright (C), 2020-2020, 珠海联创有限公司
 * FileName: ParticipateAlone
 * Author:   liang
 * Date:     20-3-4 下午3:04
 * Description: 独立参展
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.exhibition.domain.apply;

import cn.hutool.core.util.ObjectUtil;
import com.exhibition.vo.apply.EncourageEnterpriseVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 033企業參與本地或境外展會
 * @date 2020-05-27
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_enterprise")
public class EncourageEnterprise extends Encourage<EncourageEnterprise, EncourageEnterpriseVO> implements Serializable {

    private static final long serialVersionUID = 4965312432545512189L;

    /**
     * 接收資助方式
     */
    private EncourageReceive receive;
    /**
     * 銀行轉載 提供銀行記錄
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_bank_record")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> bankRecordFiles;
    /**
     * 銀行轉載  銀行授權書
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_bank_authorize")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> bankAuthorizeFiles;

    /**
     * 1.2 機構類型/企業類型
     */
    @Enumerated(EnumType.STRING)
    private InstitutionType institutionType;

    /**
     * 傳真
     private String      fax;
     *//**
     * 地址
     *//*
    private String      address;*/


    /**
     * 身份證明局設立之登記號碼
     */
    private String identityCode;
    /**
     * 負責人姓名
     */
    private String principalName;
    /**
     * 參與展會的產品、提供之服務
     */
    private String productServe;
    /**    申请项目及明細     */


    /*申請金額*/
    /**
     * 大會場刊廣告費
     */
    private Double catalogueCost=0.00;
    /**
     * 展會租金（汇率）
     */
    private Double catalogueCostRate=0.00;
    /**
     * 展會租金（汇率类型）
     */
    private String catalogueCostRateType;
    /**
     * 展會租金报价
     */
    private Double catalogueCostQuote=0.00;
    /**
     * 大會網站廣告費
     */
    private Double websiteCost=0.00;
    /**
     * 產品運輸費
     */
    private Double productFreight=0.00;
    /**
     * 展會租金（汇率）
     */
    private Double productFreightRate=0.00;
    /**
     * 展會租金（汇率类型）
     */
    private String productFreightRateType;
    /**
     * 展會租金报价
     */
    private Double productFreightQuote=0.00;
    /**
     * 展會租金（澳门币）
     */
    private Double exhibitRent=0.00;
    /**
     * 展會租金（汇率）
     */
    private Double exhibitRate=0.00;
    /**
     * 展會租金（汇率类型）
     */
    private String exhibitRateType;
    /**
     * 展會租金报价
     */
    private Double exhibitRentQuote=0.00;
    /**
     * 展會参展面积
     */
    private Double exhibitArea=0.00;
    /**
     * 展會参展展位数量
     */
    private Double exhibitBoothNum=0.00;
    /**
     * 展會製作費
     */
    private Double makeCost=0.00;
    /**
     * 展會租金（汇率）
     */
    private Double makeCostRate=0.00;
    /**
     * 展會租金（汇率类型）
     */
    private String makeCostRateType;
    /**
     * 展會租金报价
     */
    private Double makeCostQuote=0.00;
    /**
     * 交通費
     */
    private Double trafficCost=0.00;
    /**
     * 展會租金（汇率）
     */
    private Double trafficCostRate=0.00;
    /**
     * 展會租金（汇率类型）
     */
    private String trafficCostRateType;
    /**
     * 展會租金报价
     */
    private Double trafficCostQuote=0.00;
    /**
     * 印刷宣傳品費用
     */
    private Double leafletCost=0.00;
    /**
     * 展會租金（汇率）
     */
    private Double leafletCostRate=0.00;
    /**
     * 展會租金（汇率类型）
     */
    private String leafletCostRateType;
    /**
     * 展會租金报价
     */
    private Double leafletCostQuote=0.00;
    /**
     * 相關宣傳推廣及製作費用
     */
    private Double advertCost=0.00;
    /**
     * 展會租金（汇率）
     */
    private Double advertCostRate=0.00;
    /**
     * 展會租金（汇率类型）
     */
    private String advertCostRateType;
    /**
     * 展會租金报价
     */
    private Double advertCostQuote=0.00;

    /**
     * 參展項目費用
     */
    private Double exhibitionprojectcost=0.00;


    /*獲批金額*/
    /**
     * 大會場刊廣告費
     */
    private Double approvedcatalogueCost=0.00;
    /**
     * 大會網站廣告費
     */
    private Double approvedwebsiteCost=0.00;
    /**
     * 產品運輸費
     */
    private Double approvedproductFreight=0.00;
    /**
     * 展會租金
     */
    private Double approvedexhibitRent=0.00;
    /**
     * 展會製作費
     */
    private Double approvedmakeCost=0.00;
    /**
     * 交通費
     */
    private Double approvedtrafficCost=0.00;
    /**
     * 印刷宣傳品費用
     */
    private Double approvedleafletCost=0.00;
    /**
     * 相關宣傳推廣及製作費用
     */
    private Double approvedadvertCost=0.00;

    /**
     * 參展項目費用
     */
    private Double approvedexhibitionprojectcost=0.00;

    /**
     * 獲批金額  总
     private Double approvedtotalAmount;*/

    /*實際支出金額*/
    /**
     * 實際支出金額  总
     private Double actualtotalAmount;*/
    /**
     * 大會場刊廣告費
     */
    /*實際支出金額*/
    /**
     * 大會場刊廣告費
     */
    private Double actualcatalogueCost=0.00;
    /**
     * 大會網站廣告費
     */
    private Double actualwebsiteCost=0.00;
    /**
     * 產品運輸費
     */
    private Double actualproductFreight=0.00;
    /**
     * 展會租金
     */
    private Double actualexhibitRent=0.00;
    /**
     * 展會製作費
     */
    private Double actualmakeCost=0.00;
    /**
     * 交通費
     */
    private Double actualtrafficCost=0.00;
    /**
     * 印刷宣傳品費用
     */
    private Double actualleafletCost=0.00;
    /**
     * 相關宣傳推廣及製作費用
     */
    private Double actualadvertCost=0.00;

    /**
     * 參展項目費用
     */
    private Double actualexhibitionprojectcost=0.00;

    /**
     * 展後報告
     * 申請金額  总
     */
    private Double reporttotalAmount=0.00;
    /*展后报告金额*/
    /**
     * 大會場刊廣告費
     */
    private Double reportcatalogueCost=0.00;
    /**
     * 大會網站廣告費
     */
    private Double reportwebsiteCost=0.00;
    /**
     * 產品運輸費
     */
    private Double reportproductFreight=0.00;
    /**
     * 展會租金
     */
    private Double reportexhibitRent=0.00;
    /**
     * 展會製作費
     */
    private Double reportmakeCost=0.00;
    /**
     * 交通費
     */
    private Double reporttrafficCost=0.00;
    /**
     * 印刷宣傳品費用
     */
    private Double reportleafletCost=0.00;
    /**
     * 相關宣傳推廣及製作費用
     */
    private Double reportadvertCost=0.00;

    /**
     * 參展項目費用
     */
    private Double reportexhibitionprojectcost=0.00;

    /**
     * 审批 建议书编号
     */
    private String proposalNo;
    /**
     * 审批日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date proposalDate;
    /**
     * 结算 内部通讯编号
     */
    private String interiorNo;
    /**
     * 结算 内部通讯发出日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date interiorDate;
    /**
     * 公函編號
     */
    private String officiaLetterNo;
    /**
     * 公函序号
     */
    private String officiaLetterSN;

    /**
     * 類別(書面聽證/ 聲明異議)
     */
    private String writtenType;
    /**
     * 書面聽證原因
     */
    private String writtenReason;
    /**
     * 跟進同事
     */
    private String writtenFllowColleagues;
    /**
     * 跟進同事
     */
    private String writtenHearingColleagues;
    /**
     * 書面聽證類別 預審/ 結算
     */
    private String writtenHearingType;
    /**
     * 書面聽證建議書編號
     */
    private String writtenHearingNo;
    /**
     * 書面聽證狀態
     */
    private String writtenHearingStatus;

    /**
     * 預審同事
     */
    private String declarationPre;
    /**
     * 結算同事
     */
    private String declarationSettlement;
    /**
     * 結算同事
     */
    private String declarationFollow;
    /**
     * 聲明異議原因
     */
    private String declarationReason;
    /**
     * 聲明異議建議書編號
     */
    private String declarationNo;
    /**
     * 聲明異議狀態
     */
    private String declarationStatus;


    /**
     * 縂申請金額
     */
    private Double totalAmount=0.00;
    /**
     * 縂申請金額
     */
    private Double totalAmountQuote=0.00;

    /**4、於其他政府、機構就參與《參與展覽資助計劃》涉及資助項目申請或受惠資助*/
    /**
     * 是否有涉及其他政府部門、機構的申請
     */
    private Boolean haveGovernment;
    /**
     * 涉及的政府部門、機構的資助
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_government")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<Government> governments;


    /**
     * 声明
     */
    private Boolean stateAgree;

    /**
     * 是否與場所地址相同（默認相同）
     */
    @Column(columnDefinition = "tinyint default 1")
    private Boolean addressSame;
    /**
     * 地址不同，其他地址
     */
    private String liaisonOtherAddress;
    /**
     * 個人企業主/具有50%控股權之澳門居民身份證明文件副本
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_identity")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> identityFiles;
    /**
     * 申請單位簡介
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_company_profile")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> companyProfile;
    /**
     * 展會資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_exhibit_data")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> exhibitData;
    /**
     * 所得補充稅/收益申報書副本
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_income")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> incomeFiles;
    /**
     * 最近一季社會保障基金供款資料副本
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_deposit")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> depositFiles;
    /**
     * 申請項目報價單
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_quotation_sheet")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> quotationSheet;
    /**
     * 商業登記證明
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_register")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> registerFiles;
    /**
     * 財務侷發出之無欠稅登記證明書
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_no_owing")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> noOwing;
    /**
     * 營業稅-開業/更改申報表
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_change_form")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> identityForm;
    /**
     * 營業稅-徵稅憑單
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_taxation_bills")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> taxationBills;

    /**
     * 參展產品/服務資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_product_serve_files")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> productServeFiles;


    /**
     * 展後報告
     */
    @OneToOne(mappedBy = "encourageEnterprise", fetch = FetchType.LAZY)
    private EncourageEnterpriseReport report;
    /**
     * 豁免提交
     */
    private Boolean noSubmit;
    /** 納稅人信息 */
    /**
     * 納稅人姓名（已弃用，可删除）
     */
    private String taxpayer;
    /**
     * 纳税人編號（已弃用，可删除）
     */
    private String taxpayerCode;
    /**
     * 纳税人類型
     */
    @Enumerated(EnumType.STRING)
    private TaxpayerType taxpayerType;

    /**
     * 補齊資料時間
     */
    private Date supplementinfoTime;


    @Column(columnDefinition = "tinyint default 0")
    private Boolean phoneSame;

    private String liaisonOtherPhone;

    /**
     * 預期展會收入
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_revenue")
    @Fetch(FetchMode.SELECT)
    private List<SourceRevenue> revenues;
    /**
     * 預期展會支出
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_expenses")
    @Fetch(FetchMode.SELECT)
    private List<SourceRevenue> expenses;
    /**
     * 參展類別 （單一選擇）
     */
    private String ecategory;

    /**
     * 參展的預期成效（多選）
     */
    @ElementCollection
    private List<String> eresults;
    /**
     * 其它選項
     */
    private String othereresults;

    /**
     * 請選擇其它情況
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean situational;

    /**
     * 參展產品/服務資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_operating_site_files")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> operatingSiteFiles;
    /**
     * 參展產品/服務資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_transactionde_claration_files")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> transactiondeclarationFiles;
    /**
     * 參展產品/服務資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_commercial_contract_files")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> commercialcontractFiles;
    /**
     * 參展產品/服務資料
     */
    private Boolean debestatus;
    /**
     * 如屬沒有聘用員工的情況
     */
    private Boolean whetherHire;
    /**
     * 其他供招商局審批的資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_enterprise_other_approve_information")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCertAttachment> otherApproveInformation;
    // 在保存前触发
    @PrePersist
    @PreUpdate
    void prePersistSaveOrUpdate() {
        if (ObjectUtil.isEmpty(catalogueCost)) {
            catalogueCost = 0.00;
        }
        if (ObjectUtil.isEmpty(catalogueCostRate)) {
            catalogueCostRate = 0.00;
        }
        if (ObjectUtil.isEmpty(catalogueCostQuote)) {
            catalogueCostQuote = 0.00;
        }
        if (ObjectUtil.isEmpty(websiteCost)) {
            websiteCost = 0.00;
        }
        if (ObjectUtil.isEmpty(productFreight)) {
            productFreight = 0.00;
        }
        if (ObjectUtil.isEmpty(productFreightRate)) {
            productFreightRate = 0.00;
        }
        if (ObjectUtil.isEmpty(productFreightQuote)) {
            productFreightQuote = 0.00;
        }
        if (ObjectUtil.isEmpty(exhibitRent)) {
            exhibitRent = 0.00;
        }
        if (ObjectUtil.isEmpty(exhibitRate)) {
            exhibitRate = 0.00;
        }
        if (ObjectUtil.isEmpty(exhibitRentQuote)) {
            exhibitRentQuote = 0.00;
        }
        if (ObjectUtil.isEmpty(exhibitArea)) {
            exhibitArea = 0.00;
        }
        if (ObjectUtil.isEmpty(exhibitBoothNum)) {
            exhibitBoothNum = 0.00;
        }
        if (ObjectUtil.isEmpty(makeCost)) {
            makeCost = 0.00;
        }
        if (ObjectUtil.isEmpty(makeCostRate)) {
            makeCostRate = 0.00;
        }
        if (ObjectUtil.isEmpty(makeCostQuote)) {
            makeCostQuote = 0.00;
        }
        if (ObjectUtil.isEmpty(trafficCost)) {
            trafficCost = 0.00;
        }
        if (ObjectUtil.isEmpty(trafficCostRate)) {
            trafficCostRate = 0.00;
        }
        if (ObjectUtil.isEmpty(trafficCostQuote)) {
            trafficCostQuote = 0.00;
        }
        if (ObjectUtil.isEmpty(leafletCost)) {
            leafletCost = 0.00;
        }
        if (ObjectUtil.isEmpty(leafletCostRate)) {
            leafletCostRate = 0.00;
        }
        if (ObjectUtil.isEmpty(leafletCostQuote)) {
            leafletCostQuote = 0.00;
        }if (ObjectUtil.isEmpty(advertCost)) {
            advertCost = 0.00;
        }
        if (ObjectUtil.isEmpty(advertCostRate)) {
            advertCostRate = 0.00;
        }
        if (ObjectUtil.isEmpty(advertCostQuote)) {
            advertCostQuote = 0.00;
        }
        if (ObjectUtil.isEmpty(exhibitionprojectcost)) {
            exhibitionprojectcost = 0.00;
        }
        if (ObjectUtil.isEmpty(approvedcatalogueCost)) {
            approvedcatalogueCost = 0.00;
        }
        if (ObjectUtil.isEmpty(approvedwebsiteCost)) {
            approvedwebsiteCost = 0.00;
        }
        if (ObjectUtil.isEmpty(approvedproductFreight)) {
            approvedproductFreight = 0.00;
        }
        if (ObjectUtil.isEmpty(approvedexhibitRent)) {
            approvedexhibitRent = 0.00;
        }
        if (ObjectUtil.isEmpty(approvedmakeCost)) {
            approvedmakeCost = 0.00;
        }
        if (ObjectUtil.isEmpty(approvedtrafficCost)) {
            approvedtrafficCost = 0.00;
        }
        if (ObjectUtil.isEmpty(approvedleafletCost)) {
            approvedleafletCost = 0.00;
        }
        if (ObjectUtil.isEmpty(approvedadvertCost)) {
            approvedadvertCost = 0.00;
        }
        if (ObjectUtil.isEmpty(approvedexhibitionprojectcost)) {
            approvedexhibitionprojectcost = 0.00;
        }
        if (ObjectUtil.isEmpty(actualcatalogueCost)) {
            actualcatalogueCost = 0.00;
        }
        if (ObjectUtil.isEmpty(actualwebsiteCost)) {
            actualwebsiteCost = 0.00;
        }
        if (ObjectUtil.isEmpty(approvedexhibitionprojectcost)) {
            approvedexhibitionprojectcost = 0.00;
        }
        if (ObjectUtil.isEmpty(actualproductFreight)) {
            actualproductFreight = 0.00;
        }
        if (ObjectUtil.isEmpty(actualexhibitRent)) {
            actualexhibitRent = 0.00;
        }
        if (ObjectUtil.isEmpty(actualmakeCost)) {
            actualmakeCost = 0.00;
        }
        if (ObjectUtil.isEmpty(actualtrafficCost)) {
            actualtrafficCost = 0.00;
        }
        if (ObjectUtil.isEmpty(actualleafletCost)) {
            actualleafletCost = 0.00;
        }if (ObjectUtil.isEmpty(actualadvertCost)) {
            actualadvertCost = 0.00;
        }
        if (ObjectUtil.isEmpty(actualexhibitionprojectcost)) {
            actualexhibitionprojectcost = 0.00;
        }
        if (ObjectUtil.isEmpty(reporttotalAmount)) {
            reporttotalAmount = 0.00;
        }
        if (ObjectUtil.isEmpty(reportcatalogueCost)) {
            reportcatalogueCost = 0.00;
        }
        if (ObjectUtil.isEmpty(reportwebsiteCost)) {
            reportwebsiteCost = 0.00;
        }
        if (ObjectUtil.isEmpty(reportproductFreight)) {
            reportproductFreight = 0.00;
        }
        if (ObjectUtil.isEmpty(reportexhibitRent)) {
            reportexhibitRent = 0.00;
        }
        if (ObjectUtil.isEmpty(reportmakeCost)) {
            reportmakeCost = 0.00;
        }
        if (ObjectUtil.isEmpty(reporttrafficCost)) {
            reporttrafficCost = 0.00;
        }
        if (ObjectUtil.isEmpty(reportleafletCost)) {
            reportleafletCost = 0.00;
        }
        if (ObjectUtil.isEmpty(reportadvertCost)) {
            reportadvertCost = 0.00;
        }
        if (ObjectUtil.isEmpty(reportexhibitionprojectcost)) {
            reportexhibitionprojectcost = 0.00;
        }
        if (ObjectUtil.isEmpty(totalAmount)) {
            totalAmount = 0.00;
        }
        if (ObjectUtil.isEmpty(totalAmountQuote)) {
            totalAmountQuote = 0.00;
        }
    }
    /**
     * 展位编号
     */
    private String boothNo;

    public EncourageEnterprise(EncourageEnterpriseVO vo) {
        copyProperties(vo, this);
    }

    public EncourageEnterprise(Encourage e) {
        BeanUtils.copyProperties(e, this);
    }

    @Override
    public EncourageEnterpriseVO toVO() {
        return toVO(false);
    }


    public EncourageEnterpriseVO toVO(boolean includeLazy) {
        EncourageEnterpriseVO vo = new EncourageEnterpriseVO();
        copyProperties(this, vo);
        // 展後報告
//        EncourageEnterpriseReport report = this.getReport();
//        if (null != report) {
//            vo.setReport(report.toVO());
//        }
// 展会
        return vo;
    }
}
