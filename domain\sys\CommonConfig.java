/**
 * Copyright (C), 2017-2019, 珠海联创工场技术有限公司
 * FileName: CommonConfig
 * Author:   license
 * Date:     19-6-14 下午3:29
 * Description: 配置项
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.exhibition.domain.sys;

import cn.hutool.core.util.ObjectUtil;
import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.enums.Confirm;
import com.exhibition.vo.sys.CommonConfigVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 〈一句话功能简述〉<br>
 * 〈配置项〉
 *
 * <AUTHOR>
 * @create 19-6-14
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "common_config")
public class CommonConfig extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 3364397745986616797L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long         id;
    /**
     * 父分类
     */
    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    private CommonConfig parent;
    /**
     * 组
     */
    private String       groupName;
    /**
     * 选项代码
     */
    private String       code;
    /**
     * 选项值(中文)
     */
    private String       name;
    /**
     * 选项值(英)
     */
    private String       nameEn;
    /**
     * 选项值(葡)
     */
    private String       namePt;
    /**
     * 选项级别
     */
    private Integer      level;
    /**
     * 排序
     */
    private Integer      sequence;
    /**
     * 描述
     */
    @Column(name = "desc_")
    private String       desc;
    /**
     * 數據來源
     */
    private String       source;
    /**
     * 父级代码
     */
    private String       pid;
    /**
     * 是否只读
     */
    @Enumerated(EnumType.STRING)
    private Confirm      readonly;
    /**
     * 是否禁用
     */
    @Enumerated(EnumType.STRING)
    private Confirm      disabled;
    /**
     * 是否删除
     */
    @Enumerated(EnumType.STRING)
    private Confirm      deleted;


    public CommonConfig(CommonConfigVO commonConfig) {
        BeanUtils.copyProperties(commonConfig, this, "parent");
//        if (null != commonConfig.getParentId()) {
//            CommonConfig parent = new CommonConfig();
//            parent.setId(commonConfig.getParentId());
//            this.setParent(parent);
//        }
    }

    public CommonConfigVO toVO() {
        CommonConfigVO commonConfig = new CommonConfigVO();
        BeanUtils.copyProperties(this, commonConfig, "parent");
      /*  if (ObjectUtil.isEmpty(commonConfig.getDisabled())){
            commonConfig.setDisabled(Confirm.no);
        }*/
//        if (this.getParent() != null) {
//            commonConfig.setParentId(this.getParent().getId());
//            commonConfig.setParentName(this.getParent().getName());
//        }
        return commonConfig;
    }
}
