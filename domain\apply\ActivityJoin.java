package com.exhibition.domain.apply;

import com.exhibition.vo.apply.ActivityJoinVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import java.io.Serializable;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: ActivityJoin
 * @description: 預計參展人數
 * @author: ShiXin
 * @create: 2020-03-13 10:33
 **/
@Data
@NoArgsConstructor
@Embeddable
public class ActivityJoin implements Serializable {

    private static final long serialVersionUID = -549653606840693571L;

    /**
     *澳門、海外、總數
     */
    private ActivityJoinType type;
    /**
     * 參展攤位
     */
    private Integer exhibitorsBooth;
    /**
     * 參展商
     */
    private Integer exhibitors;
    /**
     * 專業買家
     */
    private Integer professionalBuyer;
    /**
     * 觀展公眾
     */
    private Integer watchingThePublic;

    public ActivityJoin(ActivityJoinVO vo) {
        BeanUtils.copyProperties(vo,this);
    }

    public ActivityJoinVO toVO() {
        ActivityJoinVO v = new ActivityJoinVO();
        BeanUtils.copyProperties(this,v);
        return v;
    }

}
