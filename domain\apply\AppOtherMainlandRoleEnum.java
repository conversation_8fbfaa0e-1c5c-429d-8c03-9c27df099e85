package com.exhibition.domain.apply;

import java.util.Arrays;

public enum AppOtherMainlandRoleEnum {
    /**
     * 參與會展活動
     */
    GUEST("嘉宾"),
    ATTENDEES("与会人士"),
    EXHIBITOR("参展商"),
    PROFESSIONAL_AUDIENCE("专业观众"),
    ORGANIZER_OF_MICE_EVENTS("会展活动筹办方"),
    MICE_SERVICE_PROVIDER("会展活动服务供应商"),
    OTHER("其他");


    private final String name;

    AppOtherMainlandRoleEnum(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static AppOtherMainlandRoleEnum getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }

}
