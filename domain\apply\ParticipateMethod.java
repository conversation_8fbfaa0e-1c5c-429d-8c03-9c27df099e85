package com.exhibition.domain.apply;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @desc 参展方式
 * @date 2020-04-13
 * @since 1.0.0
 */
@ApiModel("参展方式")
public enum ParticipateMethod {
    /**
     * 一般參展
     */
    @ApiModelProperty("一般參展")
    GENERAL_EXHIBITION("一般參展"),
    /**
     * 組團參展
     */
    @ApiModelProperty("組團參展")
    GROUP_EXHIBITION("組團參展"),
    /**
     * 中小企業參展
     */
    @ApiModelProperty("美食街")
    SME_PARTICIPATION("美食街"),
    /**
     * 廣東省企業參展
     */
    @ApiModelProperty("廣東省企業參展")
    GUANGDONG_EXHIBITION("廣東省企業參展"),
    /**
     * 一帶一路國家參展
     */
    @ApiModelProperty("一帶一路國家參展")
    BELT_AND_ROAD("一帶一路國家參展"),
    /**
     * 尊貴展商
     */
    @ApiModelProperty("尊貴參展商")
    DISTINGUISHED_EXHIBITOR("尊貴參展商"),
    /**
     * 緣創新區
     */
    @ApiModelProperty("緣創新區")
    AREA("緣創新區"),
    /**
     * 中小企業-澳門創新創業企業參展
     */
    @ApiModelProperty("中小企業-澳門創新創業企業參展")
    SME_PARTICIPATION_MACAO("中小企業-澳門創新創業企業參展"),
    /**
     *參展
     */
    PARTICIPATE("參展"),
    /**
     *IP及文創
     */
    IP("IP及文創"),
    /**
     * 參加代表團
     */
    MISSION("參加代表團"),
    Coorganizers("共同主辦單位"),
    /**
     * 其他
     */
    @ApiModelProperty("其他")
    OTHER("其他");


    private final String name;

    ParticipateMethod(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ParticipateMethod getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
