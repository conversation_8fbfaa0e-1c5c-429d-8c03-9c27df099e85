package com.exhibition.domain.activity;

import com.exhibition.vo.activity.ActivityAppendixVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 2020-03-03
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Embeddable
public class ActivityAppendix implements Serializable {
    private static final long serialVersionUID = 7209939721804288945L;
    /**
     * uid
     */
    private String          uid;

    /**
     *文件名
     */
    private String          oriname;
    /**
     * url
     */
    private String          url;

    public ActivityAppendix(ActivityAppendixVO vo) {
        BeanUtils.copyProperties(vo, this);
    }

    public ActivityAppendixVO toVO() {
        return toVO(false);
    }

    public ActivityAppendixVO toVO(boolean includeLazy) {
    	ActivityAppendixVO vo = new ActivityAppendixVO();
        BeanUtils.copyProperties(this, vo);
        return vo;
    }
}
