package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.sys.Status;
import com.exhibition.vo.apply.EncourageAttendReportVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;

import javax.persistence.CollectionTable;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 031Attend参展展后报告
 * @date 2020-06-07
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_attend_report")
public class EncourageAttendReport extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    private static final String[] IGNORE_PROPERTIES =
            new String[]{"tasks"};
    /**
     * 接收資助方式
     */
    private EncourageReceive            receive;
    /**
     * 展位租金
     */
    private Double                      exhibitRent;
    /**
     * 潛力客戶數量
     */
    private Integer                     totalCustomers;
    /**
     * 展位租金收據
     */
    @ElementCollection
    @CollectionTable(name = "encourage_attend_exhibit_rent")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert>         exhibitRentFiles;
    /**
     * 展會製作費
     */
    private Double                      makeCost;
    /**
     * 展會製作費收據
     */
    @ElementCollection
    @CollectionTable(name = "encourage_attend_make_cost")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert>         makeCostFiles;
    /**
     * 代表名片
     */
    @ElementCollection
    @CollectionTable(name = "encourage_attend_card")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert>         cardFiles;
    /**
     * 展會及展位相片
     */
    @ElementCollection
    @CollectionTable(name = "encourage_attend_photo")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert>         photoFiles;
    /**
     * 活動成效總結
     */
    @ElementCollection
    @CollectionTable(name = "encourage_attend_summary")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert>         summaryFiles;
    /**
     * 收支賬目總結
     */
    @ElementCollection
    @CollectionTable(name = "encourage_attend_account")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert>         accountFiles;
    /**
     * 潛在客戶
     */
    @ElementCollection
    @CollectionTable(name = "encourage_attend_customer")
    @Fetch(FetchMode.SELECT)
    private List<PotentialCustomer>     customers;
    /**
     * 意見
     */
    private String                      opinion;
    /**
     * 再次參加意向
     */
    private String                      againAttend;
    /**
     * 其他資助
     */
    private String                      otherSupport;
    /**
     * 代表收費
     */
    private String                      chargeExplain;
    
    /**
     * 声明
     */
    private Boolean                     stateAgree;
    /**
     * 審核狀態
     */
    @Enumerated(EnumType.STRING)
    private Status                      status;
    /**
     * 申请日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date                        applyTime;
    /**
     * 審批操作記錄
     */
    @OneToMany(mappedBy = "encourageAttendReport", fetch = FetchType.LAZY)
    private List<AdmTaskEncourageAttendReport> tasks;
    /**
     * 展會实体
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "encourage_attend_id", referencedColumnName = "id")
    private EncourageAttend              encourageAttend;

    public EncourageAttendReport(EncourageAttendReportVO v) {
        BeanUtils.copyProperties(v, this, IGNORE_PROPERTIES);
        if (v.getEncourageAttendId() != null) {
            EncourageAttend ea = new EncourageAttend();
            ea.setId(v.getEncourageAttendId());
            this.setEncourageAttend(ea);
        }
    }

    public EncourageAttendReportVO toVO() {
        EncourageAttendReportVO vo = new EncourageAttendReportVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);
        if (vo.getTasks() == null && this.getTasks() != null) {
            vo.setTasks(this.getTasks().stream().map(AdmTask::toSimpleVO).collect(Collectors.toList()));
        }
        return vo;
    }
}