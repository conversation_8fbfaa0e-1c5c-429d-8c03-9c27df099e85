package com.exhibition.domain.apply;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Embeddable;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 地區劃分的參展機構詳情
 * @date 2020-06-07
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Embeddable
public class MeetingGroup implements Serializable {
    
    private static final long serialVersionUID = -6257490264489674605L;

    /**
     * 地區
     */
    private MeetingGroupRegion    region;
    /**
     * 總共人數
     */
    private Integer               total;
    /**
     * 具入住證明
     */
    private Integer               totalRoomings;
    /**
     * 具交通證明
     */
    private Integer               totalTransportations;
}