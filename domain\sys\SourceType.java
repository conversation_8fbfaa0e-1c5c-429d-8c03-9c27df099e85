package com.exhibition.domain.sys;

import java.util.Arrays;

/**
 * 来源类型
 */
public enum SourceType {
    /**
     * 會展通 -MICEPASS
     */
    MICEPASS("會展通"),

    /**
     * 中葡信息網 -INFORMATION
     */
    INFORMATION("中葡信息網"),

    /**
     * BM商業配對 -BM
     */
    BM("BM商業配對"),

    /**
     * IPIM官網 -IPIM
     */
    IPIM("IPIM官網"),

    /**
     * 一戶通 -ACCOUNT
     */
    ACCOUNT("一戶通"),
    /**
     * 商社通 -ENTITY
     */
    ENTITY("商社通"),
    /**
     * 默認值 -MACAOMICE
     */
    MACAOMICE("活動端"),
    /**
     * 31meeting
     */
    MEETING("31會議");


    private final String name;

    SourceType( String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static SourceType getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
