package com.exhibition.domain.apply;

import com.exhibition.vo.apply.EncourageAttendVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;

import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinTable;
import javax.persistence.Lob;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: EncourageAttend
 * @description: 031Attend参展
 * @author: ShiXin
 * @create: 2020-03-11 10:21
 **/
@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_attend")
public class EncourageAttend extends Encourage<EncourageAttend, EncourageAttendVO> implements Serializable {

    private static final long serialVersionUID = -4603573144253467777L;

    /**
     * 備註
     */
    private String              remarks;
    /**基本資料 */
    /**
     * 刊登社團設立之《政府公報》副本
     */
    @ElementCollection
    @JoinTable(name = "encourage_attend_newspapers")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> newspapers;
    /**
     * 身份證明局發出之社團登記證明書副本，含領導架構之組成
     */
    @ElementCollection
    @JoinTable(name = "encourage_attend_registration")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> registration;
    /**
     * 申請表格簽署人之身份證明文件副本
     */
    @ElementCollection
    @JoinTable(name = "encourage_attend_identification")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> identification;
    /**展会资料*/
    /**
     * 展覽會簡介(尚須另行提交招展書/展覽會簡介)
     */
    @Lob
    private String              activityIntroduction;
    /**
     * 招展書/展覽會簡介附件
     */
    @ElementCollection
    @JoinTable(name = "encourage_attend_activity")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> activityFiles;
    /**
     * 參展目的
     */
    @Lob
    private String              objective;
    /**
     * 預期成效
     */
    @Lob
    private String              expectedResults;
    /**申請資助項目*/
    /**
     * 展位面积
     */
    private Double              area;
    /**
     * 展位租金
     */
    private Double              money;
    /**
     * 展位製作費
     */
    private Double              boothProductionFee;
    /**
     * 活動收支預算表【請見範本，並以附件形式提交】
     */
    @ElementCollection
    @JoinTable(name = "encourage_attend_budget")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> budgets;
    /**
     * 展位租金資料（報價 / 發票 / 收據），尚需列明展位面積及位置平面
     */
    @ElementCollection
    @JoinTable(name = "encourage_attend_rent")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> rents;
    /**
     * 由展位組織機構發出之招商代理授權書（如適用）
     */
    @ElementCollection
    @JoinTable(name = "encourage_attend_agent")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> agents;
    /**
     * 展位搭建資料（報價 / 明細 / 搭建設計效果圖）
     */
    @ElementCollection
    @JoinTable(name = "encourage_attend_booth")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> booths;
    /**
     * 本人僅代表申請者聲明　有／沒有
     */
    private Boolean             haveReceive;
    /**
     * 就是次資助項目向澳門特別行政區政府其他部門或機構申請及獲批給資助或以其他方式受惠
     */
    private String              receiveStatement;
    /**
     * 本人僅代表申請者聲明　有／沒有
     */
    private Boolean             haveCharge;
    /**
     * 本人僅代表申請者聲明　有／沒有
     */
    private String              chargeStatement;
    /**
     * 展後報告
     */
    @OneToOne(mappedBy = "encourageAttend", fetch = FetchType.LAZY)
    private EncourageAttendReport report;



    public EncourageAttend(EncourageAttendVO vo) {
        copyProperties(vo, this);
    }

    public EncourageAttend(Encourage e) {
        BeanUtils.copyProperties(e, this);
    }


    @Override
    public EncourageAttendVO toVO() {
        return toVO(false);
    }

    public EncourageAttendVO toVO(boolean includeLazy) {
        EncourageAttendVO vo = new EncourageAttendVO();
        copyProperties(this, vo, "report");
        // 展後報告
        EncourageAttendReport report = this.getReport();
        if (null != report) {
            vo.setReport(report.toVO());;
        }
        return vo;
    }
}
