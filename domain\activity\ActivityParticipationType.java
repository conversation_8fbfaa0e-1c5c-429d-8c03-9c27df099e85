package com.exhibition.domain.activity;

import java.util.Arrays;

public enum ActivityParticipationType {
    /**
     * 普通参展
     */
    BTCZ("普通参展"),
    /**
     * 参會
     */
    CH("参會"),
    /**
     * 展團
     */
    CT("展團"),
    /**
     * S&P計劃
     */
    SP("S&P計劃"),
    /**
     * 中小企
     */
    ZXQ("中小企"),
    /**
     * 一带一路
     */
    BRI("一带一路"),
    YDYL("一带一路"),
    /**
     * 境外参展
     */
    JWCZ("境外参展"),
    /**
     * 境外代表團
     */
    JWDBT("境外代表團"),
    /**
     * '參展財務鼓勵計劃'
     */
    ENTERPRISE("'參展財務鼓勵計劃'"),
    /**
     * 境外代表團
     */
    CONVENTION("'會議及展覽激勵計劃'");

    private final String name;

    ActivityParticipationType(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ActivityParticipationType getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }
        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
