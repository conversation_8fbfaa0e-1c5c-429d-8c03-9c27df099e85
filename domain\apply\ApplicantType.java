package com.exhibition.domain.apply;

import java.util.Arrays;

public enum ApplicantType {
    /**
     * 活動所有人
     */
    ALL("活動所有人"),
    /**
     * 活動籌組人
     */
    ORGANIZER("活動籌組人"),
    /**
     * 本地分會
     */
    LOCAL_BRANCH("本地分會"),
    /**
     * 其他
     */
    OTHER("其他");

    private final String name;

    ApplicantType(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ApplicantType getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }

}
