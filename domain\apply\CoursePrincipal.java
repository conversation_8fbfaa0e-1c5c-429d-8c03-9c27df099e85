package com.exhibition.domain.apply;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Embeddable;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Embeddable
public class CoursePrincipal implements Serializable {

    private static final long serialVersionUID = -7420878389796868664L;

    /**
     * 負責人名稱
     */
    private String                name;
    /**
     * 在活動負責的工作內容
     */
    private String                duty;
}