package com.exhibition.domain.apply;

import com.exhibition.vo.apply.EncourageCertVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 2020-03-03
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Embeddable
public class EncourageCert implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = -4127720903254430391L;

    /**
     * uid
     */
    private String          uid;

    /**
     *文件名
     */
    private String          oriname;
    /**
     * url
     */
    private String          url;

    public EncourageCert(EncourageCertVO vo) {
        BeanUtils.copyProperties(vo, this);
    }

    public EncourageCertVO toVO() {
        EncourageCertVO vo = new EncourageCertVO();
        BeanUtils.copyProperties(this, vo);
        return vo;
    }
}
