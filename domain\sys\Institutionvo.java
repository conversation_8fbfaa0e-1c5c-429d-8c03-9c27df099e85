package com.exhibition.domain.sys;

import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
public class Institutionvo {
    /**
     * 机构名称(中文)
     */
    private String                       nameZh;
    /**
     * 机构id
     */
    private Long                       id;

    public Institutionvo tofind(){
        Institutionvo institutionvo=new Institutionvo();
        institutionvo.setNameZh(this.getNameZh());
        institutionvo.setId(this.getId());
        return institutionvo;
    }
}
