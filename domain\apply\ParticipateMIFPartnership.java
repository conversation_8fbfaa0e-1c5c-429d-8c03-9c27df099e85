package com.exhibition.domain.apply;

import java.util.Arrays;

public enum ParticipateMIFPartnership {

    /**
     * 產品
     */
    PARODUCT("產品"),
    /**
     * 服務
     */
    SERVICE("服務"),
    /**
     * 合夥/合資
     */
    PARTNERSHIP("合夥/合資"),
    /**
     * 投資項目
     */
    INVESTENEBT_PROJECT("投資項目"),
    /**
     * 其他
     */
    OTHER("其他");


    private final String name;

    ParticipateMIFPartnership(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ParticipateMIFPartnership getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
