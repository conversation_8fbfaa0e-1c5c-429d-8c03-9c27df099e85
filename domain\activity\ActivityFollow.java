package com.exhibition.domain.activity;

import com.exhibition.domain.apply.Encourage;
import com.exhibition.vo.activity.ActivityFollowVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 展會跟進
 * @date 2023年03月30日 14:57
 */
@Data
@NoArgsConstructor
@Embeddable
@JsonView(Encourage.EncourageSimpleView.class)
public class ActivityFollow implements Serializable {
    /**
     * 跟進情況
     */
    private String situation;
    /**
     * 跟進同事
     */
    private String personnel;

    public ActivityFollow(ActivityFollowVO vo) {

        BeanUtils.copyProperties(vo, this,"activity");
    }

    public ActivityFollowVO toVO() {
        return toVO(false);
    }
    public ActivityFollowVO toVO(boolean includeLazy) {
        ActivityFollowVO vo = new ActivityFollowVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }
}
