package com.exhibition.domain.apply;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Embeddable;
import java.io.Serializable;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: MeetingHotel
 * @description: 預計住房數目分級別
 * @author: ShiXin
 * @create: 2020-03-13 16:03
 **/
@Data
@NoArgsConstructor
@Embeddable
public class MeetingHotel implements Serializable {

    private static final long serialVersionUID = 8392272260355264789L;

    /**
     * 賓館登記
     */
    private MeetingRoomLevel level;
    /**
     * 房間數
     */
    private  Integer rooms;
    /**
     * 說明
     */
    private String specify;

}
