package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.vo.apply.EncourageConventionApplyItemsVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_convention_apply_items")
public class EncourageConventionApplyItems extends BaseEntity implements Serializable {

    private static final String[] IGNORE_PROPERTIES = new String[]{"encourageConvention"};

    //序
    @Column(length = 20)
    private String serial;
    //申请項目
    @Column(length = 100)
    private String   applyItem;
    //詳細說明/計算基礎及方式
    @Column(columnDefinition = "text")
    private String  description;
    //服務供應實體
    @Column(length = 100)
    private String   serviceProvider;
    //報價單編號
    @Column(length = 100)
    private String   quotationNo;
    //申請金額(澳門元)
    @Column(length = 20,nullable = false,columnDefinition = "varchar(20) default '0.00'")
    private String  applySum;
    //获批金額(澳門元)
    @Column(length = 20,nullable = false,columnDefinition = "varchar(20) default '0.00'")
    private String  approveSum;
    //实际支出金額(澳門元)
    @Column(length = 20,nullable = false,columnDefinition = "varchar(20) default '0.00'")
    private String  actpaySum;
    //總金額：
    @Column(length = 20,nullable = false,columnDefinition = "varchar(20) default '0.00'")
    private String  totalSum;

    @ManyToOne(cascade=CascadeType.ALL)
    @JoinColumn(name = "encourage_convention_id", referencedColumnName = "id")
    private EncourageConvention           encourageConvention;

    @ElementCollection
    @CollectionTable(name = "encourage_convention_applyItems_details")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> details;

    /**
     * 資助比例
     */
    @Column
    private Double fundingRate;


    /**
     * 符合申請資格的金額
     */
    @Column(nullable = false,columnDefinition = "double NOT NULL DEFAULT 0.00")
    private Double fitApplySum;
    /**
     * 符合結算資格金額
     */
    @Column(nullable = false,columnDefinition = "double NOT NULL DEFAULT 0.00")
    private Double fitSetmentSum;

    @PrePersist
    @PreUpdate
    protected void prePersist() {
        super.prePersist();
        if (applySum == null || applySum.trim().isEmpty()) {
            applySum = "0.00";
        }
        if (approveSum == null || approveSum.trim().isEmpty()) {
            approveSum = "0.00";
        }
        if (actpaySum == null || actpaySum.trim().isEmpty()) {
            actpaySum = "0.00";
        }
        if (totalSum == null || totalSum.trim().isEmpty()) {
            totalSum = "0.00";
        }
        if (fitApplySum == null) {
            fitApplySum = 0.00;
        }
        if (fitSetmentSum == null) {
            fitSetmentSum = 0.00;
        }
    }

    public EncourageConventionApplyItems(EncourageConventionApplyItemsVO vo) {
        BeanUtils.copyProperties(vo,this,IGNORE_PROPERTIES);
        if (vo.getEncourageConventionVO()!=null) {
            this.setEncourageConvention(new EncourageConvention(vo.getEncourageConventionVO()));
        }
    }

    public EncourageConventionApplyItemsVO toVO() {
        return toVO(false);
    }

    public EncourageConventionApplyItemsVO toVO(boolean includeLazy) {
        EncourageConventionApplyItemsVO vo = new EncourageConventionApplyItemsVO();
        BeanUtils.copyProperties(this, vo,IGNORE_PROPERTIES);

        return vo;
    }
}
