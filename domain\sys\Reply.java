package com.exhibition.domain.sys;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.vo.sys.ReplyVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.controller.sys
 * @ClassName: Reply
 * @description: 回函模板
 * @author: ShiXin
 * @create: 2020-04-15 16:38
 **/

@Data
@NoArgsConstructor
@Entity
@Table(name = "reply")
public class Reply extends BaseEntity implements Serializable {


    private static final long   serialVersionUID = 2176144932435326028L;
    /**
     * 回函模板标题
     */
    private              String title;
    /**
     * 编辑者
     */
    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    private              User   edit;
    /**
     * 回函模板更新
     */
    @Lob
    @Basic(fetch = FetchType.LAZY)
    private              String content;
    /**
     * URL
     */
    @Column(columnDefinition = "text")
    private              String url;


    public Reply(ReplyVO v) {
        BeanUtils.copyProperties(v, this);

        Long editId = v.getEditId();
        if (null != editId){
            User user = new User();
            user.setId(editId);
            this.setEdit(user);
        }

    }

    public ReplyVO toVO() {
        return toVO(false);
    }

    public ReplyVO toVO(boolean includeLazy) {
        ReplyVO replyVO = new ReplyVO();
        BeanUtils.copyProperties(this, replyVO, "edit");

        if (null != this.edit) {
            replyVO.setEditId(edit.getId());
            replyVO.setEditName(edit.getName());
        }
        return replyVO;
    }

}
