package com.exhibition.domain.apply;

import com.exhibition.vo.apply.ParticipateGMBPFVO;
import com.exhibition.vo.apply.ParticipateVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import javax.persistence.CollectionTable;
import javax.persistence.Column;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: ParticipateThisBureau
 * @description: 參加本局展會
 * @author: ShiXin
 * @create: 2020-03-17 10:21
 **/
@Data
@NoArgsConstructor
@Entity
@Table(name = "participate_gmbpf")
public class ParticipateGMBPF extends Participate<ParticipateGMBPF, ParticipateVO> implements Serializable {

    private static final long serialVersionUID = 3745294526132493650L;


    /**
     * 其他參展方式
     */
    private String otherExhibitMethod;

    /**
     * 展位喜好
     */
    @Enumerated(value = EnumType.STRING)
    private ParticipateExhibitBoothPreference preference;

    /**
     * 希望參展面積
     */
    private double area;

    /**
     * 過往是否參展
     */
    private Boolean attendHistoryExhibition;

    /**
     * 过往參展年份
     */
    private String attendHistoryYear;


    /**
     * 參展產品、服務
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_exhibition_product")
    @Enumerated(EnumType.STRING)
    private List<ParticipateGMBPFProduct> exhibitionProducts;

    /**
     * 其他產品說明
     */
    private String otherProductSpecify;


    /**
     * 商業配對意向
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_business_matching")
    @Enumerated(EnumType.STRING)
    private List<ParticipateBusinessMatching> businessMatchings;


    /**
     * 商業配對意向說明
     */
    private String otherMatchingSpecify;


    /**
     * 主要目標市場
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_tarket_market")
    @Enumerated(EnumType.STRING)
    private List<ParticipateTargetMarket> targetMarkets;

    /**
     * 其他亞洲地區說明
     */
    private String otherAsiaAreaSpecify;

    /**
     * 其他國家地區說明
     */
    private String otherCountriesSpecify;


    /**
     * 繳費記錄
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_payment_record")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> paymentRecordFiles;


    /**
     * 組團企業
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_group")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateEnterpriseGroup> groups;


    /**
     * 上傳信函文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_letter")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> letterFiles;


    /**
     * 申請單位文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_unit")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> applicantUnitFiles;


    /**
     * 備註
     */
    private String remarks;

    /**\
     * 公司简介
     */
    @Column(columnDefinition = "text")
    private String companyProfile;

    /**
     * 参展的照片
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_image")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> images;

    /**
     *  参展的宣传片
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_video")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> video;

    /**
     *  参展商名单
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_exhibitor")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> exhibitor;
    /**
     * 社會保障基金供款單副本（最近一季）-->附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_shebao")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> shebaoFiles;
    /**
     * 澳門製造”之產品，須提供由澳門特別行政區政府經濟及科技發展局發出之產地來源證明書或工業准照副本 —>附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_macaozhizao")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> macaozhizaoFiles;
    /**
     * 澳門品牌”之產品，須提供由澳門特別行政區政府經濟及科技發展局發出之商標註冊證副本—>附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_macaobrand")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> macaobrandFiles;
    /**
     * 澳門代理海外品牌”之產品，須提供產品代理授權書副本 —>附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_macaoagent")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> macaoagentFiles;
    /**
     * “橫琴粵澳深度合作區製造”之產品 —>附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_macaohq")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> macaohqFiles;
    /**
     * 由商業及動產登記局於三個月內發出之本澳之商業登記證明/書面報告副本—>附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_shangye")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> shangyeFiles;
    /**
     * 四大產業及專業服務，證明文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_professional")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> professionalServiceFiles;
    @ElementCollection
    private List<String> isProfessionalServiceFiles;
    /**
     *  企業主的有效身份證明文件副本及倘有的授權書
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_powerattorney")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> powerAttorneyFiles;
    @ElementCollection
    private List<String> isPowerAttorne;
    /**
     * 營業稅 - 開業/更改申報表(M/1表格) 或財政局發出之開業聲明書副本—>附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_macaom1")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> macaom1Files;
    /**
     * 最近一年內發出的營業稅–徵稅憑單(M/8)副本—>附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_macaom8")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> macaom8Files;
    /**
     * 展品
     */
    private String exhibits;
    /**
     * 展位楣板（中文）
     */
    private String boothlintel;
    /**
     * 展位楣板（英文）
     */
    private String boothlintelen;
    /**
     * 公司簡介英文
     */
    @Column(columnDefinition = "text")
    private String companyProfileen;
    /**
     * 主要業務範圍
     */
    @ElementCollection
    private List<String> mainBusinessscope;
    /**
     * 其它
     */
    private String otherMainBusinessscope;
    /**
     * 次要業務範圍
     */
    @ElementCollection
    private List<String> lessBusinessscope;
    /**
     * 其它
     */
    private String otherlessBusinessscope;
    /**
     * 是否電商平台
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean ePlatform;
    /**
     * 電商平台名稱
     */
    private String platformName;
    /**
     * 商業配對意向
     */
    private String  busniessIntention;
    /**
     * 有意尋找的採購商/合作夥伴類型（如超市、餐廳或貿易公司等）
     */
    private String Partner;
    /**
     * 展品類別
     */
    @ElementCollection
    private List<String>  ExhibitCategory;
    /**
     * 其它展品類別
     */
    private String otherexhibitCategory;
    //是否同意
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isagree;
    //是否聲明
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isstate;
    //現聲明本申請單位知悉並同意主辦及承辦單位對上述資料及內容不負任何法律責任，並同意主辦及承辦單位保留使用、發放及宣傳推廣的權利
    @Column(columnDefinition = "tinyint default 0")
    private Boolean ispublic;

    //是否商業配對
    @Column(columnDefinition = "tinyint default 0")
    private Boolean  isbm;
    /**
     * 选项
     */
    private String commercial;
    /**
     * 是否股东身份证明文件
     */
    @ElementCollection
    private List<String> isLetter;
    /**
     * 是否M1
     */
    @ElementCollection
    private List<String> isMacaom1;
    /**
     * 是否商业登记证三个月内
     */
    @ElementCollection
    private List<String> isShangye;

    /**
     * 是否M1
     */
    @ElementCollection
    private List<String> isMacaom8;
    /**
     * 社會保障基金
     */
    @ElementCollection
    private List<String> isFss;
    /**
     * 是否包含图片
     */
    @ElementCollection
    private List<String> isImages;

    /**
     * 財政局A
     */
    @ElementCollection
    private List<String> isDsfa;
    /**
     * 財政局B
     */
    @ElementCollection
    private List<String> isDsfb;
    /**
     * 最近一年的合同
     */
    @ElementCollection
    private List<String> isContract;

    /**
     * 社會保障基金文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_fss_files")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> fssFiles;
    /**
     * 財政局A
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_dsfa_files")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> dsfaFiles;
    /**
     * 財政局B
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_dsfb_files")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> dsfbFiles;
    /**
     * 最近一年的合同
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_contract_files")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> contractFiles;
    /**
     * 最近一年的照片
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_phone_files")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> phoneFiles;
    /**
     * 照片
     */
    @ElementCollection
    private List<String> isPhone;
    /**
     * 其他文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_others")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> othersFiles;
    @ElementCollection
    private List<String> isOthersFiles;
    /**
     * 經營業務範疇
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_businessCope_files")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> businessCopeFiles;
    @ElementCollection
    private List<String> isBusinessCopeFiles;
    /**
     * 选项
     */
    private String natureOption;
    /**
     * 場所登記
     */
    private String siteRegistrationCode;
    /**
     * 是否知悉
     */
    private Boolean know;
    /**
     * 商业及动产登记局登记编号
     */
    private String registrationNumber;
    /**
     * 社會保障基金
     */
    @ElementCollection
    private List<String> isManufacturingFiles;
    @ElementCollection
    private List<String> isBrandFiles;
    @ElementCollection
    private List<String> isOverseasFiles;
    @ElementCollection
    private List<String> isGbaFiles;
    /**
     * 符合「澳門製造」資格：商品必須為澳門生產，並提供由澳門經濟及科技發展局發出的產地來源證明書副本及工業准照(即廠牌)副本；
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_manufacturing")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> manufacturingFiles;
    /**
     * 符合「澳門品牌」資格：須提供由澳門經濟及科技發展局發出的商標註冊證明副本；
     * (+加入上載附件框)
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_brand")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> brandFiles;
    /**
     * 符合「澳門代理海外品牌商品」資格：提供有關商品之代理授權書。
     * (+加入上載附件框)
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_overseas")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> overseasFiles;
    @ElementCollection
    private List<String> isBusinessOrPracticeFiles;
    @ElementCollection
    private List<String> isPowerAttorney;
    @ElementCollection
    private List<String> isDeclaration;
    /**
     * 50%股東證明
     */
    @ElementCollection
    private List<String> isIdentification;
    /**
     * 是否商業及動產登記局
     */
    @ElementCollection
    private List<String> isBusinessRegistration;
    /**
     * 是否其它文件
     */
    @ElementCollection
    private List<String> isOtherRegistration;

    /**
     *1.持有註冊資本百分之五十或以上的有效澳門特别行政區居民息份證明文件或法人的設立文件副本及倘有的授摧查
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_identityproof_or_legalperson")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> identityproofOrLegalperson;


    /**
     *4.若上述第3項之文件未能租示公司注冊诚本/股權分配情况或具其他特殊情况而未能提交本項所指之文件，經本局批准，可以聲明書或其他相閱道明文件作補充
     */
    @ElementCollection
    @CollectionTable(name = "participate_gmbpf_other_registration")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> otherRegistrationFiles;



    public ParticipateGMBPF(ParticipateGMBPFVO vo) {
        copyProperties(vo, this);
    }

    @Override
    public ParticipateGMBPFVO toVO() {
        return toVO(false);
    }

    public ParticipateGMBPFVO toVO(boolean includeLazy) {
        ParticipateGMBPFVO vo = new ParticipateGMBPFVO();
        copyProperties(this, vo);
        return vo;
    }

}
