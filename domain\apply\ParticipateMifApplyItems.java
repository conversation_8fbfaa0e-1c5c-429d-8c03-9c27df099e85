package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.vo.apply.ParticipateMifApplyItemsVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;

import javax.persistence.CascadeType;
import javax.persistence.CollectionTable;
import javax.persistence.Column;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@Entity
@Table(name = "participate_mif_apply_items")
public class ParticipateMifApplyItems extends BaseEntity implements Serializable {

    private static final String[] IGNORE_PROPERTIES = new String[]{"participateMif"};


    //項目
    private boolean   item;
    //產品
    private boolean   exhibit;
    //展品名稱
    private String  exhibitName;
    //展品類別
    private String  exhibitsType;
    //生產地
    private String  yieldly;
    //產品性質
    private String  productNature;

    //參展項目
    private String   applyItem;

    //項目説明
    @Column(columnDefinition = "text")
    private String  description;

    @ManyToOne(cascade=CascadeType.ALL)
    @JoinColumn(name = "participate_mif_id", referencedColumnName = "id")
    private ParticipateMIF           participateMif;

    //產品照片
    @ElementCollection
    @CollectionTable(name = "participate_mif_photograph")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> photograph;



    public ParticipateMifApplyItems(ParticipateMifApplyItemsVO vo) {
        BeanUtils.copyProperties(vo,this,IGNORE_PROPERTIES);
        if (vo.getParticipateMifVo()!=null) {
            this.setParticipateMif(new ParticipateMIF(vo.getParticipateMifVo()));
        }
    }

    public ParticipateMifApplyItemsVO toVO() {
        return toVO(false);
    }

    public ParticipateMifApplyItemsVO toVO(boolean includeLazy) {
        ParticipateMifApplyItemsVO vo = new ParticipateMifApplyItemsVO();
        BeanUtils.copyProperties(this, vo,IGNORE_PROPERTIES);

        return vo;
    }
}
