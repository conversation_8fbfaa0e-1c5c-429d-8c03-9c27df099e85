package com.exhibition.domain.thirdparty;

import com.exhibition.domain.apply.ActivityHistoryType;

import java.util.Arrays;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2024/4/30 9:21
 * @describe
 */
public enum DockingType {
    FINANCIAL("財政局"),

    FUNDO_DE_SEGURANCA_SOCIAL("社會保障基金"),

    IDENTITY("身份證明局");

    private final String name;

    DockingType(String name) {
        this.name = name;
    }
    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static DockingType getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }

    public static void main(String[] args) {
        String temp="identity";
        System.out.println(temp.toUpperCase(Locale.ROOT));
    }
}
