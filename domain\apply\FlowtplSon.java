package com.exhibition.domain.apply;

import com.exhibition.domain.sys.User;
import com.exhibition.vo.apply.FlowtplSonVO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.MapKeyColumn;
import javax.persistence.Table;
import java.util.Map;

@Data
@NoArgsConstructor
@Entity
@Table(name = "flowtpl_son")
public class FlowtplSon {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /*@MapKeyColumn(name = "step", length = 20)
    private int step;*/

    @ManyToMany
    @JoinTable(name = "flowtpl_approver_list",
            inverseJoinColumns = @JoinColumn(name = "approver"))
    @MapKeyColumn(name = "step", length = 20)
    private Map<Integer, User> approvers;

    /*@Id
    private Long fid;*/


    @Column(columnDefinition = "tinyint default 0")
    private Boolean choose;


    public FlowtplSon(FlowtplSonVO vo){}

    public FlowtplSonVO toVO() {
        FlowtplSonVO flowtplSonVO = new FlowtplSonVO();
        flowtplSonVO.setId(this.id);
        return flowtplSonVO;
    }
}
