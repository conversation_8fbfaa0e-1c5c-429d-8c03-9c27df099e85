package com.exhibition.domain.apply;

import com.exhibition.vo.apply.ParticipateRecordPersonVO;
import com.exhibition.vo.apply.ParticipateSpOrganiserVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Embeddable
public class ParticipateRecordPerson implements Serializable {

    //名稱
    private String name;
    //Name
    private String postion;
    //Nome
    private String phone;
    //類型
    private String email;

    public ParticipateRecordPerson(ParticipateRecordPersonVO vo) {
        BeanUtils.copyProperties(vo,this);

    }

    public ParticipateRecordPersonVO toVO() {
        return toVO(false);
    }

    public ParticipateRecordPersonVO toVO(boolean includeLazy) {
        ParticipateRecordPersonVO vo = new ParticipateRecordPersonVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }
}
