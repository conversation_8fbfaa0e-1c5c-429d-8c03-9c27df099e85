package com.exhibition.domain.apply;

import com.exhibition.vo.apply.MeetingRoomVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: ActivityGuestroom
 * @description: 預計住房數目
 * @author: ShiXin
 * @create: 2020-03-13 15:43
 **/
@Data
@NoArgsConstructor
@Embeddable
public class MeetingRoom implements Serializable {

    private static final long    serialVersionUID = -6294823073702812942L;
    /**
     * 預計住房日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private              Date    date;
    /**
     * 預計住房數
     */
    private              Integer rooms;

    public MeetingRoom(MeetingRoomVO vo) {
        BeanUtils.copyProperties(vo,this);
    }

    public MeetingRoomVO toVO() {
        MeetingRoomVO v = new MeetingRoomVO();
        BeanUtils.copyProperties(this,v);
        return v;
    }
}
