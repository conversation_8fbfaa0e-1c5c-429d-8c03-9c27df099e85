/**
 * Copyright (C), 2020-2020, 珠海联创有限公司
 * FileName: ParticipateAlone
 * Author:   liang
 * Date:     20-3-4 下午3:04
 * Description: 独立参展
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.exhibition.domain.apply;

import com.exhibition.domain.sys.Gender;
import com.exhibition.vo.apply.ParticipateCertVOs;
import com.exhibition.vo.apply.ParticipateDelegationVO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 代表团
 * @date 20-3-4
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "participate_delegation")
public class ParticipateDelegation extends Participate<ParticipateDelegation, ParticipateDelegationVO> implements Serializable {

    private static final long serialVersionUID = 4965312432545512189L;

    /**
     * 姓名（中文）
     */
    private String  nameZh;
    /**
     * 姓名（英文）
     */
    private String  nameEn;
    /**
     * 性别，M-男，F-女
     */
    @Enumerated(value = EnumType.STRING)
    private Gender  gender;
    /**
     * 出生日期
     */
    private Date    birthDate;
    /**
     * 港澳居民身份证号
     */
    private String  idCard;
    /**
     * 身份证号有效期
     */
    private Date    idCardValidityDate;
    /**
     * 港澳居民来往内地通行证号码
     */
    private String  exitNumber;
    /**
     * 港澳居民来往内地通行证有效日期
     */
    private Date    exitValidityDate;
    /**
     * 护照号码
     */
    private String  passportNumber;
    /**
     * 护照有效期
     */
    private Date    passportValidityDate;
    /**
     * 其他证件名称
     */
    private String  otherCertificateName;
    /**
     * 其他证件号码
     */
    private String  otherIDNumber;
    /**
     * 其他证件有效期
     */
    private Date    otherValidityDate;
    /**
     * 是否商协会，0-否，1-是
     */
    private Boolean isAssociation;
    /**
     * 商协会名称
     */
    private String  associationName;
    /**
     * 会内职务
     */
    private String  associationPosition;
    /**
     * 商會行业类别
     */
    private String  associateIndustry;
    /**
     * 是否公司，0-否，1-是
     */
    private Boolean isCompany;
    /**
     * 公司名称
     */
    private String  companyName;
    /**
     * 公司地址
     */
    private String  companyAddress;
    /**
     * 公司职务
     */
    private String  companyPosition;
    /**
     * 公司商業類別
     */
    private String  companyIndustry;
    /**
     * 其他名义参与，0-否，1-是
     */
    private Boolean isOtherWay;
    /**
     * 其他名义名称
     */
    private String  otherWayName;
    /**
     * 随团去程，0-否，1-是
     */
    private Boolean goWithGroup;
    /**
     * 隨团回程，0-否，1-是
     */
    private Boolean backWithGroup;
    /**
     * 由本局安排，0-否，1-是
     */
    private Boolean arrangement;
    /**
     * 其他交通和住宿情况
     */
    private String  otherArrangement;
    /**
     * 平行分論壇出席選項
     */
    private String  parallelsubForum;
    /**
     * 現聲明本申請表所填寫及提交之資料真確無誤，且知悉並同意相關條款。
     */
    private Boolean isAgree;
    /**
     * 邀请码
     */
    private String  inviteCode;
    /**
     * 联络人备注
     */
    private String  liaisonMemo;



//    /**
//     * 上傳支付憑證*/
//    @ElementCollection
//    @CollectionTable(name = "participate_delegation_pay_order")
//    @Fetch(FetchMode.SELECT)
//    private List<ParticipateCert>  payOrderFiles;

    /**
     * 用戶資料
     */
    @OneToMany(mappedBy = "participateDelegation", cascade = { CascadeType.ALL }, fetch = FetchType.LAZY)
    private List<ParticipateDelegationUser> participateDelegationUsers;



    public ParticipateDelegation(ParticipateDelegationVO vo) {
        copyProperties(vo, this);
    }

    @Override
    public ParticipateDelegationVO toVO() {
        return toVO(false);
    }

    public ParticipateDelegationVO toVO(boolean includeLazy) {
        ParticipateDelegationVO vo = new ParticipateDelegationVO();
        copyProperties(this, vo);
        List<ParticipateCert> accessoryFiles = this.getAccessoryFiles();
        if (accessoryFiles != null) {
            List<ParticipateCertVOs> participateCertVOsList=new ArrayList<>();
            for (ParticipateCert accessoryFile : accessoryFiles) {
                ParticipateCertVOs participateCertVOs=new ParticipateCertVOs();
                participateCertVOs.setName(accessoryFile.getOriname());
                participateCertVOs.setUrl(accessoryFile.getUrl());
                participateCertVOs.setUid(accessoryFile.getUid());
                participateCertVOsList.add(participateCertVOs);
            }
            vo.setParticipateCertVOsList(participateCertVOsList);
        }
        return vo;
    }
}
