package com.exhibition.domain.apply;

import com.exhibition.vo.apply.ParticipatePLPEXVO;
import com.exhibition.vo.apply.ParticipateVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import javax.persistence.CollectionTable;
import javax.persistence.Column;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: ParticipateThisBureau
 * @description: 參加本局展會
 * @author: ShiXin
 * @create: 2020-03-17 10:21
 **/
@Data
@NoArgsConstructor
@Entity
@Table(name = "participate_plpex")
public class ParticipatePLPEX extends Participate<ParticipatePLPEX, ParticipateVO> implements Serializable {


    private static final long serialVersionUID = 3126718529793019743L;


    /**
     * 其他參展方式
     */
    private String otherExhibitMethod;

    /**
     * 展位喜好
     */
    @Enumerated(value = EnumType.STRING)
    private ParticipateExhibitBoothPreference preference;

    /**
     * 希望參展面積
     */
    private double area;

    /**
     * 過往是否參展
     */
    private Boolean attendHistoryExhibition;

    /**
     * 过往參展年份
     */
    private String attendHistoryYear;


    /**
     * 參展產品、服務
     */
    @ElementCollection
    @CollectionTable(name = "participate_plpex_exhibition_product")
    @Enumerated(EnumType.STRING)
    private List<ParticipatePLPEXProduct> exhibitionProducts;

    /**
     * 其他產品說明
     */
    private String otherProductSpecify;

    /**
     * 期望合作形式
     */
    @ElementCollection
    @CollectionTable(name = "participate_plpex_business_matching")
    @Enumerated(EnumType.STRING)
    private List<ParticipateBusinessMatching> businessMatchings;

    /**
     * 商業配對意向說明
     */
    private String otherMatchingSpecify;

    /**
     * 主要目標市場
     */
    @ElementCollection
    @CollectionTable(name = "participate_plpex_tarket_market")
    @Enumerated(EnumType.STRING)
    private List<ParticipateTargetMarket> targetMarkets;

    /**
     * 其他亞洲地區說明
     */
    private String otherAsiaAreaSpecify;

    /**
     * 其他國家地區說明
     */
    private String otherCountriesSpecify;


    /**
     * 繳費記錄
     */
    @ElementCollection
    @CollectionTable(name = "participate_plpex_payment_record")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> paymentRecordFiles;


    /**
     * 組團企業
     */
    @ElementCollection
    @CollectionTable(name = "participate_plpex_group")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateEnterpriseGroup> groups;


    /**
     * 上傳信函文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_plpex_letter")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> letterFiles;


    /**
     * 申請單位文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_plpex_unit")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> applicantUnitFiles;
    /**
     * 由商業及動產登記局於三個月內發出之本澳之商業登記證明/書面報告副本—>附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_plpex_shangye")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> shangyeFiles;
    /**
     * 營業稅 - 開業/更改申報表(M/1表格) 或財政局發出之開業聲明書副本—>附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_plpex_macaom1")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> macaom1Files;
    /**
     * 最近一年內發出的營業稅–徵稅憑單(M/8)副本—>附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_plpex_macaom8")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> macaom8Files;


    /**
     * 備註
     */
    private String remarks;

    /**\
     * 公司简介
     */
    @Column(columnDefinition = "text")
    private String companyProfile;
    /**
     * 展品
     */
    @Column(columnDefinition = "text")
    private String exhibits;/**
     * 公司簡介英文
     */
    @Column(columnDefinition = "text")
    private String companyProfileen;

    /**
     * 展位楣板（中文）
     */
    @Column(columnDefinition = "text")
    private String boothlintel;
    /**
     * 展位楣板（英文）
     */
    @Column(columnDefinition = "text")
    private String boothlintelen;
    /**
     * 主要業務範圍
     */
    @ElementCollection
    private List<String> mainBusinessscope;
    /**
     * 其它
     */
    private String otherMainBusinessscope;
    /**
     * 次要業務範圍
     */
    @ElementCollection
    private List<String> lessBusinessscope;
    /**
     * 其它
     */
    private String otherlessBusinessscope;

    /**
     * 寻求
     */
    @ElementCollection
    private List<String> seeking;
    /**
     * 寻求其它
     */
    private String otherSeeking;
    /**
     * 提供
     */
    @ElementCollection
    private List<String> offering;
    /**
     * 提供其它
     */
    private String otherOffering;
    /**
     * 是否電商平台
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean ePlatform;
    /**
     * 電商平台名稱
     */
    private String platformName;
    /**
     * 商業配對意向
     */
    private String  busniessIntention;
    /**
     * 有意尋找的採購商/合作夥伴類型（如超市、餐廳或貿易公司等）
     */
    private String Partner;
    /**
     * 展品類別
     */
    @ElementCollection
    private List<String>  ExhibitCategory;
    /**
     * 其它展品類別
     */
    private String otherexhibitCategory;
    //是否同意
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isagree;
    //是否聲明
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isstate;

    //是否商業配對
    @Column(columnDefinition = "tinyint default 0")
    private Boolean  isbm;

    /**
     * 参展的照片
     */
    @ElementCollection
    @CollectionTable(name = "participate_plpex_image")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> images;

    /**
     *  参展的宣传片
     */
    @ElementCollection
    @CollectionTable(name = "participate_plpex_video")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> video;

    /**
     *  参展商名单
     */
    @ElementCollection
    @CollectionTable(name = "participate_plpex_exhibitor")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> exhibitor;

    public ParticipatePLPEX(ParticipatePLPEXVO vo) {
        copyProperties(vo, this);
    }

    @Override
    public ParticipatePLPEXVO toVO() {
        return toVO(false);
    }

    public ParticipatePLPEXVO toVO(boolean includeLazy) {
        ParticipatePLPEXVO vo = new ParticipatePLPEXVO();
        copyProperties(this, vo);
        return vo;
    }


}
