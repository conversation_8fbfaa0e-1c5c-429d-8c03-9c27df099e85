package com.exhibition.domain.apply;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 * <AUTHOR>
 * @desc
 * @date 2020-03-03
 * @since 1.0.0
 */

@Data
@NoArgsConstructor
@Entity
@DiscriminatorValue(Flowtpl.ACTIVITY)
public class AdmTaskParticipate extends AdmTask {


    private static final long serialVersionUID = 5676795014339192890L;
    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    private Participate         participate;


}
