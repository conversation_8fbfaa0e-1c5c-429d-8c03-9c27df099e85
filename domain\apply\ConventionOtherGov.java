package com.exhibition.domain.apply;

import com.exhibition.vo.apply.ConventionOtherGovVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: ConventionOtherGov
 * @description: 其他政府機構支持
 * @author: ShiXin
 * @create: 2020-03-13 17:02
 **/
@Data
@NoArgsConstructor
public class ConventionOtherGov implements Serializable {

    private static final long serialVersionUID = -549653606840693571L;

    /**
     * 政府機關名稱
     */
    private String name;
    /**
     * 申請支持內容(包括協助、財務支持等)
     */
    private String applicationContent;

    public ConventionOtherGov(ConventionOtherGovVO vo) {
        BeanUtils.copyProperties(vo,this);
    }

    public ConventionOtherGovVO toVO() {
        ConventionOtherGovVO v = new ConventionOtherGovVO();
        BeanUtils.copyProperties(this,v);
        return v;
    }

}
