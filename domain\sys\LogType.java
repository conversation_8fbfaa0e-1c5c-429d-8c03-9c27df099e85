package com.exhibition.domain.sys;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @desc
 * @date 2020-04-23
 * @since 1.0.0
 */
public enum LogType {
    /**
     * 瀏覽日志
     */
    read("瀏覽日志"),
    /**
     * 操作日志
     */
    operate("操作日志"),
    /**
     * 操作日志
     */
    api("API日志"),
    /**
     * 展會
     */
    activity("展會"),
    /**
     *banner
     */
    banner("banner"),
    /**
     * 審批任務
     */
    task("審批任務"),
    /**
     * 圖片申請
     */
    picture("圖片申請"),
    /**
     * 審批人設置
     */
    flowtpl("审批人設置"),
    /**
     * 本局主辦之展會
     */
    local("本局主辦之展會"),
    /**
     * 本局組織之境外展會
     */
    outlands("本局組織之境外展會"),
    /**
     * 022境外活動-參展
     */
    attend("本局之境外展會參展審批"),
    /**
     * 021參加代表團
     */
    delegation("本局之境外展會參加代表團審批"),
    /**
     * 粵澳名優商品展 -GMBPF
     */
    GMBPF("粵澳名優商品展"),
    /**
     * 國際基礎設施投資與建設高峰論墰 -IIICF
     */
    IIICF("國際基礎設施投資與建設高峰論墰"),
    /**
     * 澳門國際品牌連鎖加盟展 -MFE
     */
    MFE("澳門國際品牌連鎖加盟展"),
    /**
     * 澳門國際環保合作發展論壇及展覽 -MIECF
     */
    MIECF("澳門國際環保合作發展論壇及展覽"),
    /**
     * 澳門國際貿易展覽會 -MIF
     */
    MIF("澳門國際貿易展覽會"),
    /**
     * 葡語國家產品及服務展 -PLPEX
     */
    PLPEX("葡語國家產品及服務展"),
    /**
     * 展會問卷結果
     */
    questionnaireAnswer("展會問卷結果"),
    /**
     * 展會問卷
     */
    questionnaire("展會問卷"),
    /**
     * 公共配置
     */
    config("公共配置"),
    /**
     * 檔案管理
     */
    document("檔案管理"),
    /**
     * 機構
     */
    institution("機構"),
    /**
     * 機構聯絡人
     */
    liaison("機構聯絡人"),
    /**
     * 權限
     */
    purview("權限"),
    /**
     * 回函
     */
    reply("回函"),
    /**
     * 角色
     */
    role("角色"),
    /**
     * 用戶
     */
    user("用戶"),
    /**
     * 子賬號
     */
    account("子賬號"),
    /**
     * 文件上傳
     */
    file("文件上傳"),
    /**
     * 申請記錄、過往記錄
     */
    record("申請記錄、過往記錄"),
    /**
     * 服務跟進記錄
     */
    serviceFollowUp("服務跟進記錄"),
    /**
     * 申請記錄附件
     */
    attachment("申請記錄附件"),
    /**
     * 031非牟利社團赴境外展會設置展位
     */
    encourage_attend("非牟利社團赴境外展會設置展位"),
    /**
     * 032非牟利社團組織代表團參與境外展會
     */
    encourage_mission("非牟利社團組織代表團參與境外展會"),
    /**
     * 033企業參與本地或境外展會
     */
    encourage_enterprise("企業參與本地或境外展會"),
    /**
     * 034會議及展覽資質計劃
     */
    encourage_convention("會議及展覽資質計劃");

    private final String name;

    LogType( String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static LogType getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
