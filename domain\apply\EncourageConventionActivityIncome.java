package com.exhibition.domain.apply;

import com.exhibition.vo.apply.EncourageConventionActivityIncomeVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Embeddable
public class EncourageConventionActivityIncome implements Serializable {


            //序
    @Column(length = 500)
    private String serial;
            //收入項目
    @Column(length = 500)
    private String incomeItem;
           //詳細說明
    @Column(columnDefinition = "text")
    private String description;
    //金額(澳門元)
    @Column(length = 20)
    private String sum="0.00";
    //總金額：
    @Column(length = 20)
    private String totalSum="0.00";
    @PrePersist
    @PreUpdate
    void prePersistSaveOrUpdate() {
//        super.prePersist();
        if (sum == null || sum.trim().isEmpty()) {
            sum = "0.00";
        }
        if (totalSum == null || totalSum.trim().isEmpty()) {
            totalSum = "0.00";
        }
    }

    public EncourageConventionActivityIncome(EncourageConventionActivityIncomeVO vo) {
        BeanUtils.copyProperties(vo,this);

    }

    public EncourageConventionActivityIncomeVO toVO() {
        return toVO(false);
    }

    public EncourageConventionActivityIncomeVO toVO(boolean includeLazy) {
        EncourageConventionActivityIncomeVO vo = new EncourageConventionActivityIncomeVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }
}
