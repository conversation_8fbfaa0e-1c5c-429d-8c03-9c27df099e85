package com.exhibition.domain.apply;

import com.exhibition.vo.apply.ParticipateSpOrganiserVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Embeddable
public class ParticipateSpOrganiser implements Serializable {

    //名稱
    private String heading;
    //Name
    private String name;
    //Nome
    private String nome;
    //類型
    private String nationality;

    public ParticipateSpOrganiser(ParticipateSpOrganiserVO vo) {
        BeanUtils.copyProperties(vo,this);

    }

    public ParticipateSpOrganiserVO toVO() {
        return toVO(false);
    }

    public ParticipateSpOrganiserVO toVO(boolean includeLazy) {
        ParticipateSpOrganiserVO vo = new ParticipateSpOrganiserVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }
}
