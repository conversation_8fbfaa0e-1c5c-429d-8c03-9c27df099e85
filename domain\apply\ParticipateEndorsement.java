package com.exhibition.domain.apply;

import com.exhibition.domain.sys.Liaison;
import com.exhibition.vo.apply.ParticipateEndorsementVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 簽注函
 **/
@Data
@NoArgsConstructor
@Entity
@Table(name = "participate_endorsement")
public class ParticipateEndorsement extends Participate<Participate,ParticipateEndorsementVO>
        implements Serializable {

    private static final long serialVersionUID = -2863057685173892213L;


    /**
     * 其他要求
     */
    private String otherRequirements; /**
     * 是否同意報名須知
     */
    private Boolean  isAgreeFirstStep;

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long no;
    /**
     * 是否需要正本
     */
    private Boolean isVerity;
    /**
     * 通讯地址
     */
    private String correspondenceAddress;
    /**
     * 核心编号
     */
    private String crucialCode;

    /**
     * 聯絡人
     */
    @ElementCollection
    @CollectionTable(name = "participate_endorsement_con")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<ParticipateEndorsementCon> cons;

    public ParticipateEndorsement(ParticipateEndorsementVO vo) {
        copyProperties(vo, this);
        if (!CollectionUtils.isEmpty(vo.getCons())) {
            List<ParticipateEndorsementCon> collect =
                    vo.getCons().stream().map(ParticipateEndorsementCon::new).collect(Collectors.toList());
            this.setCons(collect);
        }
    }

    @Override
    public ParticipateEndorsementVO toVO() {
        return toVO(false);
    }

    public ParticipateEndorsementVO toVO(boolean includeLazy) {
        ParticipateEndorsementVO vo = new ParticipateEndorsementVO();

        if (!CollectionUtils.isEmpty(cons)) {
            vo.setCons(cons.stream().map(ParticipateEndorsementCon::toVO).collect(Collectors.toList()));
        }else{
            vo.setCons(Collections.emptyList());
        }
        copyProperties(this, vo);
        return vo;
    }

}
