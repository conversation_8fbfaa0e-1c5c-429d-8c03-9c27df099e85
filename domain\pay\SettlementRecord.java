package com.exhibition.domain.pay;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.apply.Encourage;
import com.exhibition.vo.pay.SettlementRecordVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;

import javax.persistence.CollectionTable;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.List;

//結算
@Data
@NoArgsConstructor
@Entity
@Table(name = "settlement")
@JsonView(Encourage.EncourageSimpleView.class)
public class SettlementRecord extends BaseEntity {


        //入帳筆數（MOP）
        private String mopEntryCount;
        //總結算金額(MOP)
        private String mopTotalSettlementAmount;
        //入帳筆數（HKD）
        private String hkdEntryCount;
        //總結算金額(HKD)
        private String hkdTotalSettlementAmount;

        @ElementCollection
        @CollectionTable(name = "settlement_detail")
        @Fetch(FetchMode.SELECT)
        private List<SettlementRecordDetail> details;

        public SettlementRecord(SettlementRecordVO vo) {
                BeanUtils.copyProperties(vo, this);
        }

        public SettlementRecordVO toVO() {
                SettlementRecordVO vo = new SettlementRecordVO();
                BeanUtils.copyProperties(this, vo);
                return vo;
        }
}
