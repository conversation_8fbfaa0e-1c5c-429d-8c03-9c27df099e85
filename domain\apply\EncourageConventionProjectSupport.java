package com.exhibition.domain.apply;

import com.exhibition.vo.apply.EncourageConventionProjectSupportVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Embeddable
public class EncourageConventionProjectSupport implements Serializable {


    //申請單位/機構
    @Column(length = 500)
    private String applicantUnit;
    //申請項目
    @Column(length = 500)
    private String applicantProject;
    //申請結果(待回覆 批准 不批准)
    /**
     * 申請結果待回覆/批准/不批准
     */
    @Column(length = 500)
    private String supportRes;
//    //待回覆
//    @Column(columnDefinition = "tinyint default 0")
//    private Boolean toBeReplied;
//    //批准
//    @Column(columnDefinition = "tinyint default 0")
//    private Boolean approve;
//    //不批准
//    @Column(columnDefinition = "tinyint default 0")
//    private Boolean disapprove;
    //金額(澳門元)(倘有)
    @Column(length = 20)
    private String sum;

    public EncourageConventionProjectSupport(EncourageConventionProjectSupportVO vo) {
        BeanUtils.copyProperties(vo,this);

    }

    public EncourageConventionProjectSupportVO toVO() {
        return toVO(false);
    }

    public EncourageConventionProjectSupportVO toVO(boolean includeLazy) {
        EncourageConventionProjectSupportVO vo = new EncourageConventionProjectSupportVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }
}
