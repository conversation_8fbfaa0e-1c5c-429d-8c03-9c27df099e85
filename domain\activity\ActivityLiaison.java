package com.exhibition.domain.activity;

import com.exhibition.domain.sys.Gender;
import com.exhibition.vo.activity.ActivityLiaisonVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 展會活動聯絡人
 * @date 2020-02-24
 * @since 1.0.0
 */

@Data
@NoArgsConstructor
@Embeddable
public class ActivityLiaison implements Serializable {
    /**
     * 姓名
     */
    private String name;
    /**
     * 性別
     */
    @Enumerated(value = EnumType.STRING)
    private Gender gender;
    /**
     * 區號及電話
     */
    private String areaCode;

    /**
     * 區號及傳真
     */
    private String areaCodeOrFax;
    /**
     * 電話
     */
    private String tel;
    /**
     * 電子郵箱
     */
    private String email;

    public ActivityLiaison(ActivityLiaisonVO vo) {
        BeanUtils.copyProperties(vo,this);

    }

    public ActivityLiaisonVO toVO() {
        return toVO(false);
    }

    public ActivityLiaisonVO toVO(boolean includeLazy) {
        ActivityLiaisonVO vo = new ActivityLiaisonVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }

}
