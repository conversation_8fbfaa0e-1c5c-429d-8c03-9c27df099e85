package com.exhibition.domain.apply;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Embeddable;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 培訓信息
 * @date 2020-06-09
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Embeddable
public class TrainingDesc implements Serializable {
    
    private static final long serialVersionUID = -4212863346166658656L;
    
    /**
     * 課程名稱
     */
    private String          name;
    /**
     * 授課地點
     */
    private String          address;
    /**
     * 授課方式
     */
    private String          pattern;
    /**
     * 課時數
     */
    private Integer         length;
    /**
     * 課程占比
     */
    private Double          percent; 
}