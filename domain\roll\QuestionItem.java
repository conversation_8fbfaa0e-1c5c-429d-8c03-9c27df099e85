package com.exhibition.domain.roll;

import com.exhibition.vo.roll.QuestionItemVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 选择题选项
 * @date 2020-03-30
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "question_item")
public class QuestionItem implements Serializable {

    private static final String[] IGNORE_PROPERTIES = new String[]{"question"};

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 所属题目
     */
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private Question question;

    /**
     * 选项内容
     */
    private String item;
    /**
     *
     */
    private String text;
    /**
     * 顺序
     */
    private Integer sequence;

    public QuestionItem(QuestionItemVO vo) {
        BeanUtils.copyProperties(vo, this, IGNORE_PROPERTIES);
    }

    public QuestionItemVO toVO() {
        QuestionItemVO vo = new QuestionItemVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);
        return vo;
    }

}
