package com.exhibition.domain.apply;

import com.exhibition.vo.apply.EncourageConventionActivityIncomeVO;
import com.exhibition.vo.apply.EncourageConventionReportActivityIncomeVO;
import com.exhibition.vo.apply.EncourageConventionReportExpenditureVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Embeddable
public class EncourageConventionReportActivityIncome implements Serializable {
    //序
    @Column(length = 20)
    private String serial;
    //收入項目
    @Column(length = 50)
    private String incomeItem;
    //詳細說明
    @Column(columnDefinition = "text")
    private String description;
    //金額(澳門元)
    @Column(length = 20)
    private String sum;

    public EncourageConventionReportActivityIncome(EncourageConventionReportActivityIncomeVO vo) {
        BeanUtils.copyProperties(vo, this);

    }

    public EncourageConventionReportActivityIncome(EncourageConventionReportExpenditureVO vo) {
        BeanUtils.copyProperties(vo, this);
    }

    public EncourageConventionReportActivityIncomeVO toVO() {
        return toVO(false);
    }

    public EncourageConventionReportActivityIncomeVO toVO(boolean includeLazy) {
        EncourageConventionReportActivityIncomeVO vo = new EncourageConventionReportActivityIncomeVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }
}
