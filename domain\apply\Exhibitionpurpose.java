package com.exhibition.domain.apply;

import java.util.Arrays;

/**
 * <AUTHOR> @desc 參加目的
 */
public enum Exhibitionpurpose {
    /**
     * 推廣品牌及形象
     */
    PBAI("推廣品牌及形象"),
    /**
     * 鞏固現有客戶網絡
     */
    CECN("鞏固現有客戶網絡"),
    /**
     * 了解行業趨勢及資訊
     */
    KOAITAI("了解行業趨勢及資訊"),
    /**
     * 發佈新產品
     */
    PDINP("發佈新產品"),

    /**
     * 開拓新商業聯繫
     */
    ENBC("開拓新商業聯繫"),
    /**
     * 拓展銷售網絡
     */
    ESN("拓展銷售網絡"),
    /**
     * 市場測試
     */
    MARKET("市場測試");


    private final String name;

    Exhibitionpurpose(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static Exhibitionpurpose getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }

}
