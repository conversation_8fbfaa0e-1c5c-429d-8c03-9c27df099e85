package com.exhibition.domain.apply;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @desc 接收資助方式
 * @date 2020-05-28
 * @since 1.0.0
 */
public enum EncourageReceive {
    /**
     * 支票
     */
    CHEQUE("支票"),
    /**
     * 銀行轉載
     */
    TRANSFER("銀行轉賬");

    private final String name;

    EncourageReceive(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static EncourageReceive getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
