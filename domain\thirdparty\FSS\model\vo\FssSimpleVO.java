package com.exhibition.domain.thirdparty.FSS.model.vo;

import com.exhibition.base.vo.BaseVO;
import com.exhibition.domain.thirdparty.DockingType;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/25 11:24
 * @describe
 */
@Data
@NoArgsConstructor
@ApiOperation("Fss條件查詢")
public class FssSimpleVO extends BaseVO implements Serializable {
    private static final long serialVersionUID = 8107281279561725154L;
    @ApiModelProperty("機構名稱")
    private String institutionNameZh;
    @ApiModelProperty("場所登記編號")
    private String financialPremisesCode;
    @ApiModelProperty("納稅人編號")
    private String dsfNo;
    @ApiModelProperty("社保雇主注冊編號")
    private String fssEmpNo;
    private DockingType type;
}
