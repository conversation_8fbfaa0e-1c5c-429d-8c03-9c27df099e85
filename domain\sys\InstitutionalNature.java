package com.exhibition.domain.sys;

import java.util.Arrays;

public enum InstitutionalNature {
    /**
     * 政府机构
     */
    GOVERNMENT_ORGANS("政府機構"),
    /**
     * 大學
     */
    UNIVERSITY("大學"),
    /**
     * 商/协会
     */
    BUSINESS_OR_ASSOCIATION("商/协会"),
    /**
     * 企业
     */
    ENTERPRISE("企業"),
    /**
     * 自然人
     */
    NATURAL_PERSON("自然人"),
    /**
     * 其他
     */
    OTHER("其他");


    private final String name;

    InstitutionalNature(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static InstitutionalNature getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }

}
