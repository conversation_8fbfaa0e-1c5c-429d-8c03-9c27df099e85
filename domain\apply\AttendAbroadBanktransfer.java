package com.exhibition.domain.apply;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @desc 接收資助方式
 * @date 2020-05-28
 * @since 1.0.0
 */
public enum AttendAbroadBanktransfer {
    /**
     * 首次
     */
    FIRST("首次"),
    /**
     * 過往
     */
    SECOND("過往");

    private final String name;

    AttendAbroadBanktransfer(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static AttendAbroadBanktransfer getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
