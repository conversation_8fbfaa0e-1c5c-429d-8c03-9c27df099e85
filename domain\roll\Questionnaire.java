package com.exhibition.domain.roll;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.activity.Activity;
import com.exhibition.domain.apply.EncourageType;
import com.exhibition.domain.apply.ParticipateMethod;
import com.exhibition.vo.roll.QuestionVO;
import com.exhibition.vo.roll.QuestionnaireVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.BatchSize;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 展会问卷
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "questionnaire")
public class Questionnaire extends BaseEntity implements Serializable {

    private static final String[] IGNORE_PROPERTIES = new String[]{"activity", "questions"};

    /**
     * 展会问卷模板名称
     */
    private String                  name;
    /**
     * 展会问卷模板名称
     */
    private String                  nameEn;
    private String                  namePt;
    @Column(columnDefinition = "text")
    private String                  Content;
    @Column(columnDefinition = "text")
    private String                  ContentPt;
    @Column(columnDefinition = "text")
    private String                  ContentEn;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isLogin=false;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isdownfile=false;
    /**------------------01、02展會申請關聯問卷的條件-----------------*/
    /**
     * 展會
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private Activity                activity;
    /**
     * 选择参展方式
     */
    @ElementCollection
    @CollectionTable(name = "questionnaire_method")
    @Enumerated(EnumType.STRING)
    private List<ParticipateMethod> methods;
    /**------------------03展會申請關聯問卷的條件-----------------*/
    /**
     * 03展會的申請類型
     */
    @Enumerated(EnumType.STRING)
    private EncourageType           encourageType;
    /**
     * 逻辑删除状态,0:无效,1:有效
     */
    @Column(columnDefinition = "tinyint default 1")
    private Boolean                 status;
    /**
     * 题目
     */
    @OneToMany(mappedBy = "questionnaire", fetch = FetchType.LAZY, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @BatchSize(size = 20)
    private List<Question>          questions;
    /**
     * 邮箱
     */
    @Column(columnDefinition = "varchar(255)")
    private String email;
    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date deadLine;

    public Questionnaire(QuestionnaireVO vo, boolean b) {
        BeanUtils.copyProperties(vo, this, IGNORE_PROPERTIES);

        Long activityId = vo.getActivityId();
        if (null != activityId) {
            Activity activity = new Activity();
            activity.setId(activityId);
            this.setActivity(activity);
        }
        if (b) {
            List<QuestionVO> questions = vo.getQuestions();
            if (!CollectionUtils.isEmpty(questions)) {
                List<Question> collect = questions.stream()
                        .map(Question::new)
                        .peek(q -> q.setQuestionnaire(this))
                        .collect(Collectors.toList());
                this.setQuestions(collect);
            } else {
                setQuestions(Collections.emptyList());
            }
        }


    }

    public QuestionnaireVO toVO() {
        return toVO(false);
    }

    public QuestionnaireVO toVO(boolean includeLazy) {
        QuestionnaireVO vo = new QuestionnaireVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);

        if (!CollectionUtils.isEmpty(questions)) {
            vo.setQuestionSize(questions.size());
        }
        if (null != activity) {
            vo.setActivityId(activity.getId());
            vo.setActivityNameZh(activity.getNameZh());
            vo.setActivityNameEn(activity.getNameEn());
            vo.setActivityNamePt(activity.getNamePt());
        }

        if (includeLazy) {

            if (!CollectionUtils.isEmpty(questions)) {
                vo.setQuestions(questions.stream().map(q -> q.toVO(true)).collect(Collectors.toList()));
            }
        }
        return vo;
    }

    public QuestionnaireVO toSimpleVO() {
        QuestionnaireVO vo = new QuestionnaireVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);

        if (!CollectionUtils.isEmpty(questions)) {
            vo.setQuestionSize(questions.size());
        }

        if (!CollectionUtils.isEmpty(questions)) {
            vo.setQuestions(questions.stream().map(q -> q.toVO(true)).collect(Collectors.toList()));
        }

        return vo;
    }
}
