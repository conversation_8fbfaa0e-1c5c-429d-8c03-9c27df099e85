package com.exhibition.domain.apply;

import java.util.Arrays;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: ExhibitMethod
 * @description: 參展產品
 * @author: ShiXin
 * @create: 2020-03-19 16:22
 **/

public enum ParticipatePLPEXProduct {


    /**
     * 潮流服飾
     */
    FASHIONABLE_COSTUME("潮流服飾"),
    /**
     * 餐飲業
     */
    CATERING("餐飲業"),
    /**
     * 品牌代理中介
     */
    BRAND_AGENCY("品牌代理中介"),
    /**
     * 服務業
     */
    SERVICE_INDUSTRY("服務業"),

    /**
     * 零售業
     */
    RETAIL("零售業"),
    INVESTMENT_ENVIRONMENT("環境"),
    MICE_BUSINESS_TRAVEL("會展通商務旅行"),
    CURTURAL_INNOVATION("文化創新"),
    ACCESSORIES("配件"),
    CHINESE_MEDICINE("中醫藥"),
    CRAFTS_AND_ACCESSORIES("工藝品和配件"),
    DIGITAL_IMAGING("數字成像"),
    ELECTRONIC_PRODUCT("電子產品"),
    ENVIRONMENTAL_PROTECTION("環境保護"),
    E_COMMERCE("電子商務"),
    FOOD_AND_DRINK("食物和飲料"),
    FRANCHISE("特許經銷權"),
    FURNITURE("家居"),
    GOVERNMENT_SERVICES("政府服務"),
    HOTEL_INDUSTRY("酒店行業"),
    TOY("玩具"),
    /**
     * 休閒娛樂業
     */
    LEISURE_INDUSTRY("休閒娛樂業"),
    /**
     * 品牌顧問及設計
     */
    BRAND_CONSULTING_AND_DESIGN("品牌顧問及設計"),
    /**
     * 餐飲/開業設備及技術
     */
    EQUIPMENT_AND_TECHNOLOGY("餐飲/開業設備及技術"),
    /**
     *餐飲業
     */
    ENERGY_EFFICIENCY("餐飲業"),
    /**
     *品牌代理中介
     */
    WASTE_MANAGEMENT_SOLUTIONS("品牌代理中介"),
    /**
     *休閒娛樂業
     */
    WATER("休閒娛樂業"),
    /**
     *零售貿易
     */
    AIR_QUALITY("零售貿易"),
    /**
     *知識產權產業
     */
    ENVIRONMENTAL_PRODUCTS_AND_SERVICES("知識產權產業"),
    /**
     *品牌顧問及設計
     */
    SOIL_REMEDIATION("品牌顧問及設計"),
    /**
     *新零售
     */
    ENVIRONMENTAL_MONITORING("新零售"),
    /**
     *新零售
     */
    GREEN_BUILDING("新零售"),
    /**
     *新零售
     */
    RENEWABLE_ENERGY("新零售"),
    /**
     *新零售
     */
    GREEN_TRANSPORTATION("新零售"),
    /**
     * 其他
     */
    OTHER("其他");


    private final String name;

    ParticipatePLPEXProduct(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ParticipatePLPEXProduct getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
