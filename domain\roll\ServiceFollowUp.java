package com.exhibition.domain.roll;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.activity.Activity;
import com.exhibition.vo.roll.ServiceFollowUpVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;

import javax.persistence.CollectionTable;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "service_follow_up")
public class ServiceFollowUp extends BaseEntity implements Serializable {
    /**
     * 個案來源
     */
    private String                  recordOrigin;
    /**
     * 立案同事
     */
    private String                    registerColleague;
    /**
     * 跟進同事
     */
    private String                    followUpColleague;
    /**
     * 開展跟進日期
     */
    private Date                    followUpTime;

    /**
     * 展會
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private Activity activity;
    /**
     * 落實情況
     */
    private String                  implementStatus;

    /**
     * 引進在澳舉辦形式 招攬(SOL) 競投( BID)
     */
    private String                  holdForm;

    /**
     * 預計參會人數規模(人)
     */
    private Integer                 scaleAttend;

    /**
     * 預計入場人數規模(人)
     */
    private Integer                 scaleEnter;

    /**
     * 預計社區活動規模(人)
     */
    private Integer                 scaleComActivity;

    /**
     * 預計塲地規模(平方米)
     */
    private Double                 scalePlace;

    /**
     * 預計展位規模(3x3,個)
     */
    private Integer                 numStand;

    /**
     * 預計展商規模(個)
     */
    private Integer                 numExhibitor;

    /**
     * 實際買家規模(人)
     */
    private Integer                 numBuyer;

    /**
     * 實際參會人數規模(人)
     */
    private Integer                 actualAttend;

    /**
     * 實際入場人數規模(人)
     */
    private Integer                 actualEnter;

    /**
     * 實際社區活動規模(人)
     */
    private Integer                 actualComActivity;

    /**
     * 實際塲地規模(平方米)
     */
    private Double                 actualPlace;

    /**
     * 實際展位規模(3x3,個)
     */
    private Integer                 numStandActual;

    /**
     * 實際展商規模(個)
     */
    private Integer                 numExhibitorActual;

    /**
     * 預實際買家規模(人)
     */
    private Integer                 numBuyerActual;

    /**
     * 本局提供之協助
     * A1尋找本地供應商
     * A2聯繫本地商協會
     * A3協調政府部門
     * A4活動宣傳
     * A5活動組織協調
     * A6社區活動組織協調
     * A7作為支持單位
     * A8內地參展參會人員赴澳便利措施
     * A9輔助「會展扶助專項申請」
     */
    @ElementCollection
    @CollectionTable(name = "service_follow_up_assistance")
    @Fetch(FetchMode.SELECT)
    @Enumerated(EnumType.STRING)
    private List<ServiceFollowUpAssistance> assistance;

    /**
     * 跟進情況更新
     */
    private String                  followUpStatus;

    public ServiceFollowUp(ServiceFollowUpVO vo) {
        BeanUtils.copyProperties(vo, this);
        if(null != vo.getActivity() && null != vo.getActivity().getId())
            activity = new Activity(vo.getActivity());
    }

    public ServiceFollowUpVO toVO() {
        return toVO(false);
    }


    public ServiceFollowUpVO toVO(boolean includeLazy) {
        ServiceFollowUpVO vo = new ServiceFollowUpVO();
        BeanUtils.copyProperties(this, vo);
        if(null != activity)
            vo.setActivity(activity.toVO());

        return vo;
    }

}
