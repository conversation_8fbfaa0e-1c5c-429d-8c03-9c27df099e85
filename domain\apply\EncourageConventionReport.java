package com.exhibition.domain.apply;

import cn.hutool.core.util.ObjectUtil;
import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.sys.Status;
import com.exhibition.vo.apply.EncourageConventionReportVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 034會議及展覽資助計劃展後報告
 * @date 2020-06-07
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_convention_report")
public class EncourageConventionReport extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -6952482751920495344L;
    private static final String[] IGNORE_PROPERTIES = new String[]{"encourageConvention","applyItems", "tasks"};

    /**------活動資料 - 展覽------------------------------------------------------------------------------------------*/
    /**
     * 總人數
     */
    private Integer totalExhibitors;
    /**
     * 澳門參加者總數
     */
    private Integer totalMacaoParticipants;
    /**
     * 海外參加者總數
     */
    private Integer totalOverseasParticipants;

    /**
     * 參展人數
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_join")
    @Fetch(FetchMode.SELECT)
    private List<ActivityJoin> activityJoins;

    /**------財務支持 - 會議------------------------------------------------------------------------------------------*/
    /**
     * 住房數目
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_meeting_room")
    @Fetch(FetchMode.SELECT)
    private List<MeetingRoom> meetingRooms;
    /**
     * 餐飲或會議套餐費用
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_dining")
    @Fetch(FetchMode.SELECT)
    private List<ConventionDining> conventionDinings;
    /**
     * 主題演講嘉賓總人數
     */
    private Integer totalSpeakers;
    /**
     * 主題演講嘉賓以地區劃分
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_speaker")
    @Fetch(FetchMode.SELECT)
    private List<MeetingGroup> speakers;
    /**
     * 團長總人數：
     */
    private Integer totalHeadsOfDelegation;
    /**
     * 團長以地區劃分
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_delegations")
    @Fetch(FetchMode.SELECT)
    private List<MeetingGroup> headsOfDelegations;
    /**
     * 代表團數目：
     */
    private Integer totalDelegations;
    /**
     * 代表團總人數：
     */
    private Integer totalNoDelegations;
    /**
     * 實際支付之所有宣傳及推廣費用
     */
    private Double promotionMarketingCost;
    /**
     * 實際支付之所有同聲翻譯及文件翻譯費用
     */
    private Double translationCost;
    /**
     * 實際支付之所有進入社區之本地交通費用補助費用
     */
    private Double transportationCost;
    /**
     * 實際支付之PCO費用
     */
    private Double PCOCost;
    /**
     * 實際支付之開幕典禮費用
     */
    private Double openingCeremonyCost;
    /**
     * 實際支付之展覽場地租金費用
     */
    private Double venueRental;
    /**
     * 實際支付之所有會展綠色通道費用
     */
    private Double greenChannelCost;
    /**
     * 實際支付之所有特色迎賓表演費用
     */
    private Double welcomeActivitiesCost;

    /**------財務支持 - 展覽------------------------------------------------------------------------------------------*/
    /**
     * 展会-總人數
     */
    private Integer exTotalExhibitors;
    /**
     * 展会-澳門參加者總數
     */
    private Integer exTotalMacaoParticipants;
    /**
     * 展会-海外參加者總數
     */
    private Integer exTotalOverseasParticipants;
    /**
     * 展覽場地租金
     */
    private Double exVenueRental;
    /**
     * 展覽場地面積
     */
    private Double exSpaceRented;
    /**
     * 展会-住房數目
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_ex_meeting_room")
    @Fetch(FetchMode.SELECT)
    private List<MeetingRoom> exMeetingRooms;
    /**
     * 展会-住房數目分等級
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_ex_meeting_hotel")
    @Fetch(FetchMode.SELECT)
    private List<MeetingHotel> exMeetingHotels;
    /**
     * 展会-總房數 Total Room Nights：
     */
    private Integer exTotalRooms;
    /**
     * 硬件設施費用
     */
    private Double hardwareCost;
    /**
     * 展会-實際支付之開幕典禮費用
     */
    private Double exOpeningCeremonyCost;
    /**
     * 展会-實際支付之所有宣傳及推廣費用
     */
    private Double exPromotionMarketingCost;
    /**
     * 展会-實際支付之所有同聲翻譯及文件翻譯費用
     */
    private Double exTranslationCost;
    /**
     * 實際合資格買家數量
     */
    private Integer totalQualifiedBuyers;
    /**
     * 合資格買家以地區劃分
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_qualified_buyer")
    @Fetch(FetchMode.SELECT)
    private List<MeetingGroup> qualifiedBuyers;
    /**
     * 實際支付之所有展品及貨運物流費用
     */
    private Double logisticsCost;
    /**
     * 展会-實際支付之所有會展綠色通道費用
     */
    private Double exGreenChannelCost;
    /**
     * 展会-實際支付之所有特色迎賓表演費用
     */
    private Double exWelcomeActivitiesCost;
    /**
     * 展会-實際支付之所有進入社區之本地交通費用補助費用
     */
    private Double exTransportationCost;

    /**
     * ------服務供應商資料------------------------------------------------------------------------------------------
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_service_provider")
    @Fetch(FetchMode.SELECT)
    private List<ServiceProvider> serviceProviders;
    /**
     * ------其他政府機構支持------------------------------------------------------------------------------------------
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_gov_support")
    @Fetch(FetchMode.SELECT)
    private List<GovSupport> govSupports;
    /**
     * 附件
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_attachment")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCertAttachment> attachments;
    /**
     * 声明
     */
    private Boolean stateAgree;
    /**
     * 展會申請实体
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "encourage_convention_id", referencedColumnName = "id")
    private EncourageConvention encourageConvention;
    /**
     * 審核狀態
     */
    @Enumerated(EnumType.STRING)
    private Status status;
    /**
     * 申请日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date applyTime;
    /**
     * 審批操作記錄
     */
    @OneToMany(mappedBy = "encourageConventionReport", fetch = FetchType.LAZY)
    private List<AdmTaskEncourageConventionReport> tasks;

    //034 new field
    /**
     * 3.5活動場地
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_venue")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<EncourageConventionReportVenue> reportVenue;

    /**
     * 3.6 活動組織架構
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_organizations")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<EncourageConventionReportOrganization> reportOrganizations;
    /**
     * 3.7 活動實際成效
     */
    @Column(columnDefinition = "text")
    private String actualActualEffect;

    /**
     * 3.8 活動對推動會展業提質發展的程度
     */
    //國際化步伐
    @Column(columnDefinition = "text")
    private String actualPace;
    // 市場化運作
    @Column(columnDefinition = "text")
    private String actualOperation;
    //專業化導向
    @Column(columnDefinition = "text")
    private String actualOriented;
    //數字化賦能
    @Column(columnDefinition = "text")
    private String actualEnergize;
    //綠色化發展
    @Column(columnDefinition = "text")
    private String actualDevelop;

    /**
     * 3.9 其他活動資料
     */
    //屬重點產業相關的專業會展活動(如：中醫藥大健康、現代金融、高新技術、會展商貿和文化體育)，請於附件提供詳細資料。
    @Column(columnDefinition = "tinyint default 0")
    private Boolean actualDatum1;
    /**
     * 屬重點產業相關的專業會展活動(如：中醫藥大健康、現代金融、高新技術、會展商貿和文化體育)，請於附件提供詳細資料。
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_details")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> details;
    //屬“一會展兩地”模式舉辦，請於附件提供詳細資料。
    @Column(columnDefinition = "tinyint default 0")
    private Boolean actualDatum2;

    /**
     * 屬“一會展兩地”模式舉辦，請於附件提供詳細資料。
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_places_details")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> placesDetails;

    /**
     * 3.10 實際入住酒店資料(如適用)
     */
    /**
     * 酒店名稱
     */
    @ElementCollection
    private List<String> actualhotelName;

    /**
     * 城市/地區
     */
    @Column(length = 20)
    private String actualUrbanArea;
    // 實際入住房數目
    //入住日期
    @Column(length = 10)
    private String actualCheckInDate1;
    @Column(length = 10)
    private String actualCheckInDate2;
    @Column(length = 10)
    private String actualCheckInDate3;
    @Column(length = 10)
    private String actualCheckInDate4;
    @Column(length = 10)
    private String actualCheckInDate5;
    @Column(length = 10)
    private String actualCheckInDate6;
    /**
     * 房數
     */
    private Integer actualRoomsNumber1;
    private Integer actualRoomsNumber2;
    private Integer actualRoomsNumber3;
    private Integer actualRoomsNumber4;
    private Integer actualRoomsNumber5;
    private Integer actualRoomsNumber6;
    /**
     * 總房數
     */
    private Integer actualRoomsNumberTotal;

    /**
     * 3.11 實際活動日程*請以附件形式提供詳細資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_schedule")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<EncourageConventionReportSchedule> reportSchedule;
    /**
     * 預計活動日程*請以附件形式提供詳細資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_expected_schedule")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> actualSchedule;

    /**
     * 3.12 實際參與者資料*請以附件形式提供詳細資料
     */
    //1.會議
    //總數:
    //與會者總數
    private Integer actualTotalParticipants;
    //演講嘉賓總數
    private Integer actualTotalsSpeakers;
    //其他
    @Column(length = 20)
    private String actualOther;
    //其他總數
    private Integer actualTotalOther;
    //實際總數
    private Integer actualTotal;
    //澳門:
    //澳門與會者
    private Integer actualMacaoParticipants;
    //澳門演講嘉賓
    private Integer actualMacaoSpeakers;
    //澳門其他
    private Integer actualMacaoOther;
    //澳門總數
    private Integer actualMacaoTotal;
    //中國(包括內地、香港及台灣地區):
    //中國與會者
    private Integer actualChinaParticipants;
    //中國演講嘉賓
    private Integer actualChinaSpeakers;
    //中國其他
    private Integer actualChinaOther;
    //中國總數
    private Integer actualChinaTotal;
    //海外
    //海外與會者
    private Integer actualOverseaParticipants;
    //海外演講嘉賓
    private Integer actualOverseaSpeakers;
    //海外其他
    private Integer actualOverseaOther;
    //海外總數
    private Integer actualOverseaTotal;

    //2.展覽
    //總數:
    //參展商總數
    private Integer actualTotalExhibitorsNum;
    // 當中設 9 平方米或以上標準展位的參展商總數
    private Integer actualTotalBooth;
    //買家總數
    private Integer actualTotalBuyer;
    //專業觀眾總數
    private Integer actualTotalAudience;
    //觀展公眾總數
    private Integer actualTotalPublic;
    //其他
    @Column(length = 20)
    private String actualExhibitionOther;
    //其他總數
    private Integer actualExhibitionTotalOther;
    //澳門:
    //澳門參展商
    private Integer actualMacaoExhibitorsNum;
    //澳門當中設 9 平方米或以上標準展位的參展商
    private Integer actualMacaoBooth;
    // 澳門買家
    private Integer actualMacaoBuyer;
    //澳門專業觀眾
    private Integer actualMacaoAudience;
    //澳門觀展公眾
    private Integer actualMacaoPublic;
    //澳門其他
    private Integer actualExhibitionMacaoOther;
    //中國(包括內地、香港及台灣地區):
    //中國參展商
    private Integer actualChinaExhibitors;
    //中國當中設 9 平方米或以上標準展位的參展商
    private Integer actualChinaBooth;
    //中國買家
    private Integer actualChinaBuyer;
    //中國專業觀眾
    private Integer actualChinaAudience;
    //中國觀展公眾
    private Integer actualChinaPublic;
    //中國其他
    private Integer actualExhibitionChinaOther;
    //海外
    //海外參展商
    private Integer actualOverseaExhibitors;
    //海外當中設 9 平方米或以上標準展位的參展商
    private Integer actualOverseaBooth;
    //海外買家
    private Integer actualOverseaBuyer;
    // 海外專業觀眾
    private Integer actualOverseaAudience;
    //海外觀展公眾
    private Integer actualOverseaPublic;
    //海外其他
    private Integer actualExhibitionOverseaOther;
    /**
     * 預計參與者資料*請以附件形式提供詳細資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_expected_participant")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> actualParticipant;

    //有(請於下表提供資料) 沒有(請跳至第4.2項)
    @Column(columnDefinition = "tinyint default 0")
    private Boolean whetherSupport;

    //4.1 有否向其他機構申請財務資助或其他支持項目？
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_support")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<EncourageConventionReportSupport> reportSupport;

    //有(請於下表提供資料) 沒有(請跳至第4.3項)
    @Column(columnDefinition = "tinyint default 0")
    private Boolean whetherActivityIncome;

    //4.2 整體活動實際收入明細 (除本局資助金額外，列明有關活動的所有收入，如參會/參展費用、廣告收入、贊助等)
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_activity_income")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<EncourageConventionReportActivityIncome> reportActivityIncome;

    //4.2 總金額：收入
    @Column(length = 20)
    private String activityIncomeTotalSum;

    //4.3 整體活動實際支出明細 (請列明有關活動的所有支出，包括向本局申請資助項目)
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_expenditure")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<EncourageConventionReportExpenditure> reportExpenditure;

    //4.3 總金額：支出
    @Column(length = 20)
    private String expenditureTotalSum;

    //4.4 獲批准資助項目實際支出明細
    /*@ElementCollection
    @CollectionTable(name = "encourage_convention_report_approved")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore*/
    @OneToMany(mappedBy = "encourageConventionReport", cascade = CascadeType.ALL)
    private List<EncourageConventionReportApproved> reportApproved;

    //4.4 總金額：批准
    @Column(length = 20)
    private String approvedTotalSum;

    //5. 受資助者之聲明及、法定代表簽署及蓋章 有(請於下表提供資料) 沒有(請跳至第4.3項)
    @Column(columnDefinition = "tinyint default 0")
    private Boolean actualAgree;

    /**
     * 個人企業主/具有50%控股權之澳門居民身份證明文件副本
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_identity")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> identityFiles;

    /**
     * 申請者如屬個人: 有效身份證明文件副本及澳門特別行政區政府財政局發出之開業申報文件
     * (M1)副本
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_individual")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> individualFiles;

    /**
     * 申請者如屬企業 商業登記文件(如：當地政府機關簽發之商業登記文件副本、本澳之商業登
     * 記證明/報告書、營業稅 M1 及 M8 副本等)
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_legal_entity")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> legalEntityFiles;

    /**
     * 展會資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_non_profit_organisation")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> nonProfitOrganisationFiles;

    /**
     * 所得補充稅M/1 A組或B組收益申報書副本
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_contract")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> contractFiles;

    /**
     * 最近一季社會保障基金供款資料副本
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_receipt")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> receiptFiles;

    /**
     * 參展產品/服務資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_dmc")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> dmcFiles;

    /**
     * 申請項目報價單
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_nature_and_history")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> natureAndHistoryFiles;

    /**
     * 其他參考文件
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_report_other_documents")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> otherDocumentsFiles;

    public EncourageConventionReport(EncourageConventionReportVO v) {
        BeanUtils.copyProperties(v, this, IGNORE_PROPERTIES);

        if (!CollectionUtils.isEmpty(v.getReportApproved())) {
            List<EncourageConventionReportApproved> collect =
                    v.getReportApproved().stream().map(EncourageConventionReportApproved::new).peek(a -> a.setEncourageConventionReport(this)).collect(Collectors.toList());
            this.setReportApproved(collect);
        }

        if (!CollectionUtils.isEmpty(v.getReportExpenditure())) {
            List<EncourageConventionReportExpenditure> collect =
                    v.getReportExpenditure().stream().map(EncourageConventionReportExpenditure::new).collect(Collectors.toList());
            this.setReportExpenditure(collect);
        }

        if (!CollectionUtils.isEmpty(v.getReportActivityIncome())) {
            List<EncourageConventionReportActivityIncome> collect =
                    v.getReportActivityIncome().stream().map(EncourageConventionReportActivityIncome::new).collect(Collectors.toList());
            this.setReportActivityIncome(collect);
        }

        if (!CollectionUtils.isEmpty(v.getReportSupport())) {
            List<EncourageConventionReportSupport> collect =
                    v.getReportSupport().stream().map(EncourageConventionReportSupport::new).collect(Collectors.toList());
            this.setReportSupport(collect);
        }

        if (!CollectionUtils.isEmpty(v.getReportActivityIncome())) {
            List<EncourageConventionReportActivityIncome> collect =
                    v.getReportActivityIncome().stream().map(EncourageConventionReportActivityIncome::new).collect(Collectors.toList());
            this.setReportActivityIncome(collect);
        }

        if (!CollectionUtils.isEmpty(v.getReportSupport())) {
            List<EncourageConventionReportSupport> collect =
                    v.getReportSupport().stream().map(EncourageConventionReportSupport::new).collect(Collectors.toList());
            this.setReportSupport(collect);
        }

        if (!CollectionUtils.isEmpty(v.getReportSchedule())) {
            List<EncourageConventionReportSchedule> collect =
                    v.getReportSchedule().stream().map(EncourageConventionReportSchedule::new).collect(Collectors.toList());
            this.setReportSchedule(collect);
        }

        if (!CollectionUtils.isEmpty(v.getReportOrganizations())) {
            List<EncourageConventionReportOrganization> collect =
                    v.getReportOrganizations().stream().map(EncourageConventionReportOrganization::new).collect(Collectors.toList());
            this.setReportOrganizations(collect);
        }

        if (!CollectionUtils.isEmpty(v.getReportVenue())) {
            List<EncourageConventionReportVenue> collect =
                    v.getReportVenue().stream().map(EncourageConventionReportVenue::new).collect(Collectors.toList());
            this.setReportVenue(collect);
        }

        if (v.getEncourageConventionId() != null) {
            EncourageConvention ec = new EncourageConvention();
            ec.setId(v.getEncourageConventionId());
            this.setEncourageConvention(ec);
        }
        //會議及展覽匯總參展人數
        int actualTotalParticipants = ObjectUtil.isNotEmpty(v.getActualTotalParticipants())?v.getActualTotalParticipants():0;
        int actualTotalSpeakers = ObjectUtil.isNotEmpty(v.getActualTotalsSpeakers())?v.getActualTotalsSpeakers():0;
        int actualTotalOther = ObjectUtil.isNotEmpty(v.getActualTotalOther())?v.getActualTotalOther():0;
        this.actualTotal=actualTotalParticipants+actualTotalSpeakers+actualTotalOther;
        //澳門總數
        int actualMacaoParticipants = ObjectUtil.isNotEmpty(v.getActualMacaoParticipants())?v.getActualMacaoParticipants():0;
        int actualMacaoSpeakers = ObjectUtil.isNotEmpty(v.getActualMacaoSpeakers())?v.getActualMacaoSpeakers():0;
        int actualMacaoOther = ObjectUtil.isNotEmpty(v.getActualMacaoOther())?v.getActualMacaoOther():0;
        this.actualMacaoTotal=actualMacaoParticipants+actualMacaoSpeakers+actualMacaoOther;
        //中國總數
        int actualChinaParticipants = ObjectUtil.isNotEmpty(v.getActualChinaParticipants())?v.getActualChinaParticipants():0;
        int actualChinaSpeakers = ObjectUtil.isNotEmpty(v.getActualChinaSpeakers())?v.getActualChinaSpeakers():0;
        int actualChinaOther = ObjectUtil.isNotEmpty(v.getActualChinaOther())?v.getActualChinaOther():0;
        this.actualChinaTotal=actualChinaParticipants+actualChinaSpeakers+actualChinaOther;
        //海外總數
        int actualOverseaParticipants = ObjectUtil.isNotEmpty(v.getActualOverseaParticipants())?v.getActualOverseaParticipants():0;
        int actualOverseaSpeakers = ObjectUtil.isNotEmpty(v.getActualOverseaSpeakers())?v.getActualOverseaSpeakers():0;
        int actualOverseaOther = ObjectUtil.isNotEmpty(v.getActualOverseaOther())?v.getActualOverseaOther():0;
        this.actualOverseaTotal = actualOverseaParticipants+actualOverseaSpeakers+actualOverseaOther;
        //其他總數
        int actualTotalExhibitorNum = ObjectUtil.isNotEmpty(v.getActualTotalExhibitorsNum())?v.getActualTotalExhibitorsNum():0;
        int actualTotalBooth = ObjectUtil.isNotEmpty(v.getActualTotalBooth())?v.getActualTotalBooth():0;
        int actualTotalAudience = ObjectUtil.isNotEmpty(v.getActualTotalAudience())?v.getActualTotalAudience():0;
        int actualTotalPublic = ObjectUtil.isNotEmpty(v.getActualTotalPublic())?v.getActualTotalPublic():0;
        int actualTotalBuyer = ObjectUtil.isNotEmpty(v.getActualTotalBuyer())?v.getActualTotalBuyer():0;
        this.actualExhibitionTotalOther=ObjectUtil.isNotEmpty(v.getActualExhibitionTotalOther())?v.getActualExhibitionTotalOther():0;
        //澳門其他
        int actualMacaoExhibitorsNum = ObjectUtil.isNotEmpty(v.getActualMacaoExhibitorsNum())?v.getActualMacaoExhibitorsNum():0;
        int actualMacaoBooth = ObjectUtil.isNotEmpty(v.getActualMacaoBooth())?v.getActualMacaoBooth():0;
        int actualMacaoBuyer = ObjectUtil.isNotEmpty(v.getActualMacaoBuyer())?v.getActualMacaoBuyer():0;
        int actualMacaoAudience = ObjectUtil.isNotEmpty(v.getActualMacaoAudience())?v.getActualMacaoAudience():0;
        int actualMacaoPublic = ObjectUtil.isNotEmpty(v.getActualMacaoPublic())?v.getActualMacaoPublic():0;
        this.actualExhibitionMacaoOther=ObjectUtil.isNotEmpty(v.getActualExhibitionMacaoOther())?v.getActualExhibitionMacaoOther():0;
        //中國其他
        int actualChinaExhibitor = ObjectUtil.isNotEmpty(v.getActualChinaExhibitors())?v.getActualChinaExhibitors():0;
        int actualChinaBooth = ObjectUtil.isNotEmpty(v.getActualChinaBooth())?v.getActualChinaBooth():0;
        int actualChinaBuyer = ObjectUtil.isNotEmpty(v.getActualChinaBuyer())?v.getActualChinaBuyer():0;
        int actualChinaAudience = ObjectUtil.isNotEmpty(v.getActualChinaAudience())?v.getActualChinaAudience():0;
        int actualChinaPublic = ObjectUtil.isNotEmpty(v.getActualChinaPublic())?v.getActualChinaPublic():0;
        this.actualExhibitionChinaOther=ObjectUtil.isNotEmpty(v.getActualExhibitionChinaOther())?v.getActualExhibitionChinaOther():0;
        //海外其他
        int actualOverseaExhibitors = ObjectUtil.isNotEmpty(v.getActualOverseaExhibitors())?v.getActualOverseaExhibitors():0;
        int actualOverseaBooth = ObjectUtil.isNotEmpty(v.getActualOverseaBooth())?v.getActualOverseaBooth():0;
        int actualOverseaBuyer = ObjectUtil.isNotEmpty(v.getActualOverseaBuyer())?v.getActualOverseaBuyer():0;
        int actualOverseaAudience = ObjectUtil.isNotEmpty(v.getActualOverseaAudience())?v.getActualOverseaAudience():0;
        int actualOverseaPublic = ObjectUtil.isNotEmpty(v.getActualOverseaPublic())?v.getActualOverseaPublic():0;
        this.actualExhibitionOverseaOther=ObjectUtil.isNotEmpty(v.getActualExhibitionOverseaOther())?v.getActualExhibitionOverseaOther():0;
        //支出明细总金额
        if (!CollectionUtils.isEmpty(v.getReportExpenditure())) {
            List<EncourageConventionReportExpenditure> collect =
                    v.getReportExpenditure().stream().map(EncourageConventionReportExpenditure::new).collect(Collectors.toList());
            String totalSum = collect.stream()
                    .map(EncourageConventionReportExpenditure::getSum)
                    .filter(StringUtils::isNotEmpty)
                    .map(BigDecimal::new)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .toString();
            this.setExpenditureTotalSum(totalSum);
        }
        //实际支出明细
        if (!CollectionUtils.isEmpty(v.getReportApproved())) {
            List<EncourageConventionReportApproved> collect =
                    v.getReportApproved().stream().map(EncourageConventionReportApproved::new).collect(Collectors.toList());
            String totalSum = collect.stream()
                    .map(EncourageConventionReportApproved::getApprovedSum)
                    .filter(StringUtils::isNotEmpty)
                    .map(BigDecimal::new)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .toString();
            this.setApprovedTotalSum(totalSum);
        }
        //实际收入明细總金額
        if (!CollectionUtils.isEmpty(v.getReportActivityIncome())) {
            List<EncourageConventionReportActivityIncome> collect =
                    v.getReportActivityIncome().stream().map(EncourageConventionReportActivityIncome::new).collect(Collectors.toList());
            String totalSum = collect.stream()
                    .map(EncourageConventionReportActivityIncome::getSum)
                    .filter(StringUtils::isNotEmpty)
                    .map(BigDecimal::new)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .toString();
            this.setActivityIncomeTotalSum(totalSum);
        }



    }

    public EncourageConventionReportVO toVO() {
        EncourageConventionReportVO v = new EncourageConventionReportVO();
        BeanUtils.copyProperties(this, v, IGNORE_PROPERTIES);

        EncourageConvention encourageConvention = this.getEncourageConvention();
        if (null != encourageConvention) {
            v.setEncourageConventionVO(encourageConvention.toVO());
            v.setEncourageConventionId(encourageConvention.getId());
        }

        if (!CollectionUtils.isEmpty(reportApproved)) {
            v.setReportApproved(reportApproved.stream().map(EncourageConventionReportApproved::toVO).collect(Collectors.toList()));
        } else {
            v.setReportApproved(Collections.emptyList());
        }

        if (!CollectionUtils.isEmpty(reportExpenditure)) {
            v.setReportExpenditure(reportExpenditure.stream().map(EncourageConventionReportExpenditure::toVO).collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(reportActivityIncome)) {
            v.setReportActivityIncome(reportActivityIncome.stream().map(EncourageConventionReportActivityIncome::toVO).collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(reportSupport)) {
            v.setReportSupport(reportSupport.stream().map(EncourageConventionReportSupport::toVO).collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(reportSchedule)) {
            v.setReportSchedule(reportSchedule.stream().map(EncourageConventionReportSchedule::toVO).collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(reportOrganizations)) {
            v.setReportOrganizations(reportOrganizations.stream().map(EncourageConventionReportOrganization::toVO).collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(reportVenue)) {
            v.setReportVenue(reportVenue.stream().map(EncourageConventionReportVenue::toVO).collect(Collectors.toList()));
        }

        if (v.getTasks() == null && this.getTasks() != null) {
            v.setTasks(this.getTasks().stream().map(AdmTask::toSimpleVO).collect(Collectors.toList()));
        }
        return v;
    }
}
