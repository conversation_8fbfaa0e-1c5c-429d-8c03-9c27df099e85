package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.activity.Activity;
import com.exhibition.domain.sys.Institution;
import com.exhibition.domain.sys.Liaison;
import com.exhibition.domain.sys.Status;
import com.exhibition.domain.sys.User;
import com.exhibition.util.CopyUtils;
import com.exhibition.util.StringUtils;
import com.exhibition.vo.apply.EncourageInspectionVO;
import com.exhibition.vo.apply.EncourageLetterVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;
import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: EncourageInspection
 * @description: 巡展表
 * @author: Jay
 * @create: 2025-02-25 16:50
 **/
@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_inspection")
public class EncourageInspection<T extends EncourageInspection, V extends EncourageInspectionVO> extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -4603573144253468767L;
    private static final String[] IGNORE_PROPERTIES =
            new String[]{"activity", "institution", "liaison", "applicant", "tasks", "liaison"};

    // Assuming Encourage is another entity class
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "encourage_id", referencedColumnName = "id")
    private Encourage encourage;



    /**
     * 實質設展及派具看管展位
     */
    @Column(columnDefinition = "BOOLEAN DEFAULT false")
    private Boolean              substantiveExhibition;
    /**
     * 展位眉板名稱與申請資料相符
     */
    @Column(columnDefinition = "BOOLEAN DEFAULT false")
    private Boolean              boothMatch;
    /**
     * 展示/售賣產品與申請資料相符
     */
    @Column(columnDefinition = "BOOLEAN DEFAULT false")
    private Boolean              productsMatch;
    /**
     * 備注
     */
    @Column(columnDefinition = "text")
    private String remark;
    /**
     * 複核情況
     */
    @Column(columnDefinition = "text")
    private String reviewMemo;
    /**
     * 複檢標記
     */
    @Column(columnDefinition = "BOOLEAN DEFAULT false")
    private Boolean              reinspectionMark;
    /**
     * 活動情況
     */
    @Column(columnDefinition = "text")
    private String  activitySituation;
    /**
     * 參會情況
     */
    @Column(columnDefinition = "text")
    private String attendance;
    /**
     * 參展情況
     */
    @Column(columnDefinition = "text")
    private String exhibitionSituation;

    /**
     * 巡展圖片
     */
    @ElementCollection
    @JoinTable(name = "encourage_inspection_pic")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> inspectionPic;
    /**
     * 巡展異常記錄
     */
    @ElementCollection
    @JoinTable(name = "encourage_inspection_record_exception")
    @Fetch(FetchMode.SELECT)
    private List<RecordException> inspectionRecordException;
    /**
     * 類型
     */
    @Enumerated(EnumType.STRING)
    private EncourageType inspectionType;
    /**
     * 狀態
     */
    @ElementCollection
    private List<String> inspectionStates;
    /**
     * 巡展時間
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date inspectionTimes;
    /**
     * 展位编号
     */
    @Column(columnDefinition = "varchar(125)")
    private String boothNo;
    /**
     * 檢查結果
     */
    @Column(columnDefinition = "BOOLEAN DEFAULT false")
    private Boolean inspectionResult;

    /**1
     *硬件設施
     */
    @Column(columnDefinition = "varchar(255)")
    private String hardwareFacilities;

    /**2
     *展覽場地租金
     */
    @Column(columnDefinition = "varchar(255)")
    private String exhibitionVenueRental;

    /**3
     *電子科技應用
     */
    @Column(columnDefinition = "varchar(255)")
    private String electronicTechnology;
    /**4
     *綠色會展應用
     */
    @Column(columnDefinition = "varchar(255)")
    private String greenConvention;
    /**5
     *社區特色活動的籌辦及交通費用
     */
    @Column(columnDefinition = "varchar(255)")
    private String organizationAndExpenses;
    /**6
     *澳門專業展會及組織者之活動策劃及管理費用
     */
    @Column(columnDefinition = "varchar(255)")
    private String plannAndExpenses;
    /**7
     *酒店住宿
     */
    @Column(columnDefinition = "varchar(255)")
    private String hotelAccommodation;
    /**8
     *宣傳及推廣
     */
    @Column(columnDefinition = "varchar(255)")
    private String promotionAndMarket;
    /**9
     *餐飲或會議套餐
     */
    @Column(columnDefinition = "varchar(255)")
    private String cateringOrMeeting;
    /**10
     *場地考察
     */
    @Column(columnDefinition = "varchar(255)")
    private String siteInspection;
    /**11
     *競投輔助
     */
    @Column(columnDefinition = "varchar(255)")
    private String biddingSupport;

    /**12
     *加入行業組織之費用
     */
    @Column(columnDefinition = "varchar(255)")
    private String industryAssociationAdmissionFee;
    /**13
     *其它
     */
    @Column(columnDefinition = "varchar(255)")
    private String Other;
    /**14
     *展品及貨運物流
     */
    @Column(columnDefinition = "varchar(255)")
    private String logistics;





    public EncourageInspection(EncourageInspectionVO vo) {
        BeanUtils.copyProperties(vo, this, IGNORE_PROPERTIES);
        if (null != vo.getEncourageId()) {
            Encourage e = new Encourage();
            e.setId(vo.getEncourageId());
            this.setEncourage(e);
        }
    }

    public EncourageInspection(Encourage e) {
        BeanUtils.copyProperties(e, this);


    }



    public EncourageInspectionVO toVO() {
        return toVO(false);

    }

    public EncourageInspectionVO toVO(boolean includeLazy) {
        EncourageInspectionVO vo = new EncourageInspectionVO();
        BeanUtils.copyProperties(this, vo);
        copyProperties( vo,  this);

        return vo;
    }
    protected void copyProperties(EncourageInspectionVO v, EncourageInspection t) {
//        BeanUtils.copyProperties(t.encourage, v, IGNORE_PROPERTIES);
        Encourage encourage = this.encourage;
        if (encourage != null) {

            v.setCode(encourage.getCode());
            v.setStatus(encourage.getStatus());
            v.setType(encourage.getType());
            v.setEncourageId(encourage.getId());
            v.setEncourage(encourage.toVO());
            // 展会
            Activity activity = this.encourage.getActivity();
            if (null != activity) {
                v.setActivityId(activity.getId());
                v.setActivityName(StringUtils.isNotEmpty(activity.getNameZh())?activity.getNameZh():"");
                v.setActivityScope(activity.getActivityScope());
                v.setActivity(activity.toSimpleVO());
            }
            // 机构
            Institution institution = this.encourage.getInstitution();
            if (null != institution) {
                v.setInstitutionId(institution.getId());
                v.setInstitutionName(institution.getNameZh());
                v.setInstitution(institution.toVO());
            }
            // 组团机构
            Institution institutionCluster = this.encourage.getInstitutionCluster();
            if (null != institutionCluster) {
                v.setInstitutionClusterId(institutionCluster.getId());
                v.setInstitutionCluster(institutionCluster.toSimpleVO());
            }

            // 发起人
            User applicant = this.encourage.getApplicant();
            if (null != applicant) {
                v.setApplicantId(applicant.getId());
                v.setApplicantName(applicant.getName()+applicant.getLastName()+"("+applicant.getAccount()+")");
                v.setApplicantAccount(applicant.getAccount());
                v.setApplicantPhone(applicant.getPhone());
            }

            // 联络人
            Liaison liaison = this.encourage.getLiaison();
            if (null != liaison) {
                v.setLiaisonId(liaison.getId());
                v.setLiaisonName(liaison.getNameZh());
                v.setLiaison(liaison.toVO());
            }
        }



    }


}
