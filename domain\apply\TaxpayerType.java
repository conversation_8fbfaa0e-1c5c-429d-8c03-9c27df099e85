package com.exhibition.domain.apply;

import java.util.Arrays;

public enum TaxpayerType {

    /**
     * 個人企業
     */
    INDIVIDUAL_BUSINESS("個人企業"),
    /**
     * 有限公司
     */
    LIMITED_COMPANY("有限公司"),

    /**
     * 團體
     */
    ORGANIZATION("團體");




    private final String name;

    TaxpayerType(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static TaxpayerType getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
