package com.exhibition.domain.apply;

import cn.hutool.core.util.ObjectUtil;
import com.exhibition.domain.sys.Liaison;
import com.exhibition.domain.sys.User;
import com.exhibition.req.GPSAPADDReq;
import com.exhibition.req.GPSAPUPDATEReq;
import com.exhibition.vo.apply.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 034會議及展覽資助計劃
 * @date 2020-05-27
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_convention")
@BatchSize(size = 20)
@Slf4j
public class EncourageConvention extends Encourage<EncourageConvention, EncourageConventionVO> implements Serializable {

    private static final long serialVersionUID = 5222788763289965175L;

    private static final String[] IGNORE_PROPERTIES = new String[]{
            "liaisonSub", "meetingDates", "activityDates", "conventionHistory", "openTimes", "activityJoins",
            "meetingRooms", "conventionDinings", "conventionVenueRentals", "exMeetingRooms", "conventionOtherGovs", "applyItems","organizations"};

    /**
     * 034活動類型
     */
    @Enumerated(EnumType.STRING)
    private ConventionType conventionType;
    /**
     * 聯絡人二資料
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private Liaison liaisonSub;
    /*@ManyToMany()
    @JoinTable(name = "encourage_convention_2_liaison", joinColumns = {@JoinColumn(name = "convention_id")},
            inverseJoinColumns = {@JoinColumn(name = "liaison_id")})
    @org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    private List<Liaison>               liaisons;*/
    //------申請人資料--------------------------------------------------------------------------------------------------
    /**
     * 官方名稱和銀行戶口名稱是否相同
     */
    private Boolean sameName;
    /**
     * 官方名稱和銀行戶口名稱不同的說明
     */
    private String descriptionDifferentNames;
    /**
     * 1.2 機構類型/企業類型
     */
    @Enumerated(EnumType.STRING)
    private InstitutionType institutionType;
    /**
     * 企業類型說明
     */
    private String enterpriseTypeDescription;
    /**
     * 1.5申請者身份分類
     */
    @Enumerated(EnumType.STRING)
    private ApplicantType applicantType;
    /**
     * 申請者身份分類說明
     */
    private String applicantTypeDescription;

    /**
     * 申請者及相關詳細活動資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_related_file")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> relatedFiles;
    /**------4、活動資料 - 會議-------------------------------------------------------------------------------------------*/
    /**
     * 活動開始日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date meetingFrom;
    /**
     * 活動結束日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date meetingTo;
    /**
     * 活動截止日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date meetingExpiryTime;
    /**
     * 活動補交期限日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date meetingUxedoEndTime;
    /**
     * 會議場地
     */
    private String meetingPlace;
    /**
     * 會議的舉辦週期
     */
    @Enumerated(EnumType.STRING)
    private ActivityCycle meetingCycle;
    /**
     * 請說明
     */
    private String meetingCycleDescription;
    /**
     * 預計會議舉行時數
     */
    private Double hours;
    /**
     * 預計全天會議舉行時數
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_meeting_date")
    @Fetch(FetchMode.SELECT)
    private List<MeetingDate> meetingDates;
    /**
     * 預計活動舉行時數
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_activity_date")
    @Fetch(FetchMode.SELECT)
    private List<MeetingDate> activityDates;
    /**
     * 以往曾舉辦會議的國家
     * 或地區
     */
    @Enumerated(EnumType.STRING)
    private ActivityHistoryType meetingHistory;
    /**
     * 以往曾舉辦會議的國家
     * 或地區
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_history")
    @Fetch(FetchMode.SELECT)
    private List<ConventionHistory> conventionHistory;
    /**
     * 第二屆舉辦,第一届會議日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date secondDate;
    /**
     * 第二屆舉辦,第一届會議地點
     */
    private String secondVenue;
    /**
     * 過往活動資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_history_file")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> historyFiles;
    /**
     * 總人數
     */
    private Integer totalAttendees;
    /**
     * 澳門人數
     */
    private Integer attendeesFromMacao;
    /**
     * 海外人數
     */
    private Integer overseasAttendees;
    /**
     * 預計參會者資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_attendee_file")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> attendeeFiles;
    /**----------------------5.活動資料-展覽-----------------------------------------------------------------------------*/
    /**
     * 活動開始日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date conventionFrom;
    /**
     * 活動結束日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date conventionTo;
    /**
     * 預計展覽會開放時間:
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_open_time")
    @Fetch(FetchMode.SELECT)
    private List<ConventionOpenTime> openTimes;
    /**
     * 展覽預留場地
     */
    private String exhibitionReservedVenue;
    /**
     * 落實展覽場地面積(每日平方米)
     */
    private Double confirmedExhibitionSpace;
    /**
     * 展位编号
     */
    @Column(columnDefinition = "text")
    private String boothNo;
    /**
     * 展覽的舉辦週期
     */
    @Enumerated(EnumType.STRING)
    private ActivityCycle activityCycle;
    /**
     * 展覽其他的請說明
     */
    private String activityCycleDescription;
    /**
     * 預計參展人數
     */
   /* @ElementCollection
    @CollectionTable(name = "encourage_convention_join")
    @Fetch(FetchMode.SELECT)
    private List<ActivityJoin>          activityJoins;*/
    /**
     * 澳門
     */
    /**
     * 澳門參展攤位
     */
    private Integer macaoExhibitorsBooth;
    /**
     * 澳門參展商
     */
    private Integer macaoExhibitors;
    /**
     * 澳門專業買家
     */
    private Integer macaoProfessionalBuyer;
    /**
     * 澳門觀展公眾
     */
    private Integer macaoWatchingThePublic;
    /**
     * 海外
     */
    /**
     * 海外參展攤位
     */
    private Integer overseasExhibitorsBooth;
    /**
     * 海外參展商
     */
    private Integer overseasExhibitors;
    /**
     * 海外專業買家
     */
    private Integer overseasProfessionalBuyer;
    /**
     * 海外觀展公眾
     */
    private Integer overseasWatchingThePublic;
    /**
     * 縂數
     */
    /**
     * 縂數參展攤位
     */
    private Integer totalExhibitorsBooth;
    /**
     * 縂數參展商
     */
    private Integer totalExhibitors;
    /**
     * 縂數專業買家
     */
    private Integer totalProfessionalBuyer;
    /**
     * 縂數觀展公眾
     */
    private Integer totalWatchingThePublic;
    /**
     * 預計合資格境外買家
     */
    private Integer overSeasBuyers;
    /**
     * 預計合資格境外買家附件資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_overseas_buyer")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> overSeasBuyerFiles;
    /**----------------------於其他政府機構就同一活動之支持申請------------------------------------------------------------------------------*/
    /**
     * 基本協助
     */
    /**
     * 旅遊資料及歡迎禮物
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_gift")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> gifts;
    /**
     * 澳門宣傳短片
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_video")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> videos;
    /**
     * 使用投促局網上商業配對服務平台
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_platform")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> platforms;
    /**
     * 活動資料發放到官方網頁會展活動日誌內
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_government")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> governments;
    /**
     * 於投促局接待處發放活動資訊
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_ipim")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> ipims;
    /**
     * 協調與各政府部門的聯繋
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_facilitation")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> facilitations;
    /**
     * 一般參會者住宿
     */
    /**
     * 申請住宿費用支持
     */
    private Boolean accommodationSupport;
    /**
     * 預計住房數目
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_meeting_room")
    @Fetch(FetchMode.SELECT)
    private List<MeetingRoom> meetingRooms;
    /**
     * 會議-五星級住房數
     */
    private Integer meetingStar5Rooms;
    /**
     * 會議-五星級住房名稱
     */
    private String meetingStar5Name;
    /**
     * 會議-四星級住房數
     */
    private Integer meetingStar4Rooms;
    /**
     * 會議-四星級住房名稱
     */
    private String meetingStar4Name;
    /**
     * 會議-三星級住房數
     */
    private Integer meetingStar3Rooms;
    /**
     * 會議-三星級住房名稱
     */
    private String meetingStar3Name;
    /**
     * 會議-其他星級住房數
     */
    private Integer meetingOtherRooms;
    /**
     * 會議-其住星級房名稱
     */
    private String meetingOtherName;
    /**
     * 會議-縂房數
     */
    private Integer meetingTotalRooms;
    /**
     * 申請餐飲或會議套餐費用支持
     */
    private Boolean packageSupport;
    /**
     * 申請餐飲或會議套餐費用支持
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_dining")
    @Fetch(FetchMode.SELECT)
    private List<ConventionDining> conventionDinings;
    /**
     * 申請主題演講嘉賓及團長費用支持
     */
    private Boolean supportForKeynote;
    /**
     * 主題演講嘉賓總人數
     */
    private Integer totalSpeakers;
    /**
     * 團長總人數：
     */
    private Integer totalHeadsOfDelegation;
    /**
     * 代表團數目：
     */
    private Integer totalDelegations;
    /**
     * 代表團總人數：
     */
    private Integer totalNoDelegations;
    /**
     * 申請宣傳及推廣費用支持
     */
    private Boolean promotionMarketingSupport;
    /**
     * 申請同聲傳譯及文件翻譯費用支持
     */
    private Boolean interpretingTranslation;
    /**
     * 申請進入社區之本地交通費用補助支持
     */
    private Boolean localTransportationSupport;
    /**
     * 申請PCO補助支持
     */
    private Boolean PCOSupport;
    /**
     * 申請開幕典禮的支持
     */
    private Boolean ceremonySupport;
    /**
     * 申請展覽場地租金支持
     */
    private Boolean venueRentalSupport;
    /**
     * 會展綠色通道
     */
    private Boolean greenChannelSupport;
    /**
     * 申請特色迎賓表演支持
     */
    private Boolean performanceSupport;
    //----------------------6.3財務支持 – 展覽 -------------------------------------------------------------
    /**
     * 申請展覽場地租金支持
     */
    private Boolean exVenueRentalSupport;
    /**
     * 展覽場地租金
     */
   /* @ElementCollection
    @CollectionTable(name = "encourage_convention_venue_rental")
    @Fetch(FetchMode.SELECT)
    private List<ConventionVenueRental> conventionVenueRentals;*/
    /**
     * 搭建-開始
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date buildVenueFrom;
    /**
     * 搭建-結束
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date buildVenueTo;
    /**
     * 搭建-落實展覽場地面積
     */
    @Column(columnDefinition = "double default 0")
    private Double buildGroupArea;
    /**
     * 搭建-場地租金單價(每日每平方米澳門元)
     */
    @Column(columnDefinition = "double default 0.00")
    private Double buildPrice;
    /**
     * 搭建-場地租金總價(澳門元)
     */
    @Column(columnDefinition = "double default 0.00")
    private Double buildTotalPrice;
    /**
     * 展覽-開始
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date exhibitionVenueFrom;
    /**
     * 展覽-結束
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date exhibitionVenueTo;
    /**
     * 展覽-落實展覽場地面積
     */
    @Column(columnDefinition = "double default 0")
    private Double exhibitionGroupArea;
    /**
     * 展覽-場地租金單價(每日每平方米澳門元)
     */
    @Column(columnDefinition = "double default 0.00")
    private Double exhibitionPrice;
    /**
     * 展覽-場地租金總價(澳門元)
     */
    @Column(columnDefinition = "double default 0.00")
    private Double exhibitionTotalPrice;
    /**
     * 拆展-開始
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date apartVenueFrom;
    /**
     * 拆展-結束
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date apartVenueTo;
    /**
     * 拆展-落實展覽場地面積
     */
    @Column(columnDefinition = "double default 0.00")
    private Double apartGroupArea;
    /**
     * 拆展-場地租金單價(每日每平方米澳門元)
     */
    @Column(columnDefinition = "double default 0.00")
    private Double apartPrice;
    /**
     * 拆展-場地租金總價(澳門元)
     */
    @Column(columnDefinition = "double default 0.00")
    private Double apartTotalPrice;
    /**
     * 展会-申請住宿費用支持
     */
    private Boolean exAccommodationSupport;
    /**
     * 展会-預計住房數目
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_ex_meeting_room")
    @Fetch(FetchMode.SELECT)
    private List<MeetingRoom> exMeetingRooms;
    /**
     * 展覽-五星級住房數
     */
    private Integer exMeetingStar5Rooms;
    /**
     * 展覽-五星級住房名稱
     */
    private String exMeetingStar5Name;
    /**
     * 展覽-四星級住房數
     */
    private Integer exMeetingStar4Rooms;
    /**
     * 展覽-四星級住房名稱
     */
    private String exMeetingStar4Name;
    /**
     * 展覽-三星級住房數
     */
    private Integer exMeetingStar3Rooms;
    /**
     * 展覽-三星級住房名稱
     */
    private String exMeetingStar3Name;
    /**
     * 展覽-其他星級住房數
     */
    private Integer exMeetingOtherRooms;
    /**
     * 展覽-其住星級房名稱
     */
    private String exMeetingOtherName;
    /**
     * 展覽-縂房數
     */
    private Integer exMeetingTotalRooms;
    /**
     * 展会-硬件设施支持
     */
    private Boolean exhardwareSupport;
    /**
     * 展会-申請開幕典禮的支持
     */
    private Boolean exCeremonySupport;
    /**
     * 展会-申請合資格買家費用支持
     */
    private Boolean exQualifiedBuyersSupport;
    /**
     * 展会-申請展品及貨運物流費用支持
     */
    private Boolean exLogisticsSupport;
    /**
     * 展会-申請宣傳及推廣費用支持
     */
    private Boolean exPromotionMarketingSupport;
    /**
     * 展会-申請進入社區之本地交通費用補助支持
     */
    private Boolean exLocalTransportationSupport;
    /**
     * 展会-申請會展綠色通道支持
     */
    private Boolean exGreenChannelSupport;
    /**
     * 展会-申請特色迎賓表演支持
     */
    private Boolean exPerformanceSupport;
    //----------------------7、於其他政府機關就同一活動之支持申請-------------------------------------------------------------
    /**
     * 於其他政府機關就同一活動之支持申請
     */
    private Boolean otherGovernmentSupport;
    /**
     * 於其他政府機關就同一活動之支持申請
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_other_gov")
    @Fetch(FetchMode.SELECT)
    private List<ConventionOtherGov> conventionOtherGovs;
    //----8、與本申請表同時提交之文件-----------------------------------------------------------------------------------------
    /**
     * 申請者如屬個人: 有效身份證明文件副本及澳門特別行政區政府財政局發出之開業申報文件
     * (M1)副本
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_individual")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> individualFiles;
    /**
     * 申請者如屬企業 商業登記文件(如：當地政府機關簽發之商業登記文件副本、本澳之商業登
     * 記證明/報告書、營業稅 M1 及 M8 副本等)
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_legal_entity")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> legalEntityFiles;
    /**
     * 申請者如屬非牟利團體: 團體設立文件(如：當地政府機關簽發之登記文件副本、本澳之澳門特
     * 別行政區公報副本、本澳之身份證明局發出之登記證明書副本等)
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_non_profit")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> nonProfitOrganisationFiles;
    /**------------------------------------------------證明已確定活動文件-------------*/
    /**
     * 場地提供者之書面協議/合同副本
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_contract")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> contractFiles;
    /**
     * 場地/服務提供者之訂金收據
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_receipt")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> receiptFiles;
    /**
     * 申請者及其聘用之本地專業會議組織者∕目的地管理公司的簡介
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_dmc")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> dmcFiles;
    /**
     * 活動屬性及背景資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_nature_history")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> natureAndHistoryFiles;
    /**
     * 活動計劃大綱
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_detailed_programme")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> detailedProgrammeFiles;
    /**
     * 預計的參展商名單及其他資料(包括名稱、展位編號及大小、平面圖等資料)
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_related_documents")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> relatedDocumentsFiles;
    /**
     * 主題演講嘉賓名單及其他文件(如：個人簡介及演講題目)
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_keynote_speakers")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> keynoteSpeakersFiles;
    /**
     * 團長的名單及相關資料，相關資料包括代表團的劃分基礎及其成員
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_delegates")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> delegatesFiles;
    /**
     * 合資格買家名片及其公司業務證明(如：來源地商業登記)
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_business_card")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> businessCardFiles;
    /**
     * 其他參考文件
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_other_documents")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> otherDocumentsFiles;
    /**
     * 其他參考說明
     */
    private String otherDocumentsSpecify;
    //預審公函編號
    private String serialno;
    //FSP編號
    //(申請年份-FSP編號)
    private String fspno;
    //使用面積
    @Column(columnDefinition = "double default 0.00")
    private Double meetingArea;
    //淨展出面積
    @Column(columnDefinition = "varchar(100) default '0.00'")
    private String spaceSpecify;
    //展出比例
    private Double extPercent;
    //參與人數
    private Integer extPerson;
    //預算預留年份
    private String budgetYear;
    //預審審批狀況
    private String preApprovalStatus;
    //預審建議書批准日期
    private Date preApprovalDate;
    //結算審批狀況
    private String setApprovalStatus;
    //結算建議書批准日期
    private Date setApprovalDate;
    //活動預計支出金額
    @Column(columnDefinition = "double default 0.00")
    private Double expensesMoney;
    //預審建議書編號
    private String preProposalNo;
    //預審分數
    private Double preScore;
    //預審金額
    @Column(columnDefinition = "double default 0.00")
    private Double preMoney;
    //預審人員
    private String prePerson;
    //所屬經濟分類
    //（企業/社團/高等院校)
    private String classification;
    //活動核查日期
    private String verificationDate;
    //使用面積
    private String usableArea;
    //參與人數
    private String usablePerson;
    //結算建議書編號
    private String setProposalNo;
    //結算金額
    @Column(columnDefinition = "double default 0.00")
    private String setMoney;
    //結算人員
    private String setPerson;
    //(預審)本地交通
    //費用支持
    private String preTrafficMoney;
    //(實際)本地交通
    //費用支持
    private String actTrafficMoney;

    /**
     * 預審人員
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private User recordPreUser;
    /**
     * 結算人員
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private User recordSetUser;


    //034 new  field
    /**
     * 3.活動資料
     */
    //3.1活動名稱
    private String activityName;

    private String activityNameEn;

    private String activityWebsite;

//    @Column(columnDefinition = "tinyint default 0")
//    private Boolean actBoolean1;
//    @Column(columnDefinition = "tinyint default 0")
//    private Boolean actBoolean2;
//    @Column(columnDefinition = "tinyint default 0")
//    private Boolean actBoolean3;
//    @Column(columnDefinition = "tinyint default 0")
//    private Boolean actBoolean4;
    /**
     * 3.3 活動類型
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_meeting_enum")
    @Enumerated(EnumType.STRING)
    private List<EncourageConventionMeetingEnum> meetingEnum;

    /**
     * 3.5活動場地
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_event_venue")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<EncourageConventionEventVenue> eventVenue;

    //3.6 活動組織架構之組成
    @Column(columnDefinition = "text")
    private String compositionStructure;
    @OneToMany(mappedBy = "encourageConvention", cascade = CascadeType.ALL)
    private List<EncourageConventionOrganization> organizations;

    @ElementCollection
    @CollectionTable(name = "encourage_convention_organizations_files")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> organizationsFiles;

    //3.7 活動簡介
    @Column(columnDefinition = "text")
    private String activityIntroduction;
    @ElementCollection
    @CollectionTable(name = "encourage_convention_introduction_files")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> activityIntroductionFiles;
    //3.8 預期達致效益
    @Column(columnDefinition = "text")
    private String expectedBenefits;
    @ElementCollection
    @CollectionTable(name = "encourage_convention_benefits_files")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> expectedBenefitsFiles;

    //3.9 活動對推動會展業提質發展的程度
    //國際化步伐
    @Column(columnDefinition = "text")
    private String internationalizationPace;
    @ElementCollection
    @CollectionTable(name = "encourage_convention_pace_files")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> internationalizationPaceFiles;
    // 市場化運作
    @Column(columnDefinition = "text")
    private String marketOperation;
    @ElementCollection
    @CollectionTable(name = "encourage_convention_operation_files")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> marketOperationFiles;
    //專業化導向
    @Column(columnDefinition = "text")
    private String professionalOrientation;
    @ElementCollection
    @CollectionTable(name = "encourage_convention_orientation_files")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> professionalOrientationFiles;
    //數字化賦能
    @Column(columnDefinition = "text")
    private String digitalEmpowerment;
    @ElementCollection
    @CollectionTable(name = "encourage_convention_empowerment_files")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> digitalEmpowermentFiles;
    //綠色化發展
    @Column(columnDefinition = "text")
    private String greenDevelopment;
    @ElementCollection
    @CollectionTable(name = "encourage_convention_development_files")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> greenDevelopmentFiles;

    //是否首屆舉辦
    @Column(columnDefinition = "tinyint default 0")
    private Boolean whetherFirstTime;

    //3.10 其他活動資料
    //活動首屆舉辦日期
    @Temporal(TemporalType.TIMESTAMP)
    private Date firstEventStart;
    @Temporal(TemporalType.TIMESTAMP)
    private Date firstEventEnd;
    // 屬國際性會議/專業論壇/專業或品牌展覽，請提供資料或證明文件
    @Column(columnDefinition = "tinyint default 0")
    private Boolean custBoolean1;
    /**
     * 屬國際性會議/專業論壇/專業或品牌展覽，請提供資料或證明文件
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_supporting_document")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> supportingDocument;

    //屬重點產業相關的專業會展活動(如：中醫藥大健康、現代金融、高新技術、會展商貿和文化體育)，請於附件提供詳細資料。
    @Column(columnDefinition = "tinyint default 0")
    private Boolean custBoolean2;
    //是否存在關聯交易
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isTradeRelatedRelation;
    //是否存在關聯參展商
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isRelatedExhibitor;
    //關聯交易申報表字段
    @ElementCollection
    @CollectionTable(name = "encourage_convention_related_trade")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<EncourageConventionRelatedTrade> relatedGrade;
    //存在關聯參展商所帶附件
    @ElementCollection
    @CollectionTable(name = "encourage_convention_related_exhibitor_adjunct")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> relatedExhibitorAdjunct;

    /**
     * 屬重點產業相關的專業會展活動(如：中醫藥大健康、現代金融、高新技術、會展商貿和文化體育)，請於附件提供詳細資料。
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_details")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> details;


    //屬“一會展兩地”模式舉辦，請於附件提供詳細資料。
    @Column(columnDefinition = "tinyint default 0")
    private Boolean custBoolean3;
    /**
     * 屬“一會展兩地”模式舉辦，請於附件提供詳細資料。
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_places_details")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> placesDetails;


    //3.11 入住酒店資料(如適用)
    //預計入住澳門酒店
    @Column(columnDefinition = "tinyint default 0")
    private Boolean estimatedOccupancy;
    //酒店名稱  多选
    @ElementCollection
    private List<String> hotelName;
    // 城市/地區
    @Column(length = 125)
    private String urbanArea;
    // 預計住房數目
    //入住日期
    @Column(length = 10)
    private String checkInDate1;
    @Column(length = 10)
    private String checkInDate2;
    @Column(length = 10)
    private String checkInDate3;
    @Column(length = 10)
    private String checkInDate4;
    @Column(length = 10)
    private String checkInDate5;
    @Column(length = 10)
    private String checkInDate6;
    //房數
    private Integer roomsNumber1;
    private Integer roomsNumber2;
    private Integer roomsNumber3;
    private Integer roomsNumber4;
    private Integer roomsNumber5;
    private Integer roomsNumber6;
    //總房數
    private Integer roomsNumberTotal;

    //3.12 預計活動日程*請以附件形式提供詳細資料
    @ElementCollection
    @CollectionTable(name = "encourage_convention_estimated_schedule")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<EncourageConventionEstimatedSchedule> estimatedSchedule;

    /**
     * 預計活動日程*請以附件形式提供詳細資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_expected_schedule")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> expectedSchedule;

    //3.13 預計參與者資料*請以附件形式提供詳細資料
    /**
     * 預計參與者資料*請以附件形式提供詳細資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_expected_participant")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> expectedParticipant;
    //1.會議
    //總數:
    //與會者總數
    private Integer totalParticipants;
    //演講嘉賓總數
    private Integer totalsSpeakers;
    //其他
    @Column(columnDefinition = "text")
    private String other;
    @Column(columnDefinition = "text" )
    private String otherRemark;
    //其他總數
    private Integer totalOther;
    //總數
    private Integer total;
    //澳門:
    //澳門與會者
    private Integer macaoParticipants;
    //澳門演講嘉賓
    private Integer macaoSpeakers;
    //澳門其他
    private Integer macaoOther;
    //澳門總數
    private Integer macaoTotal;
    //中國(包括內地、香港及台灣地區):
    //中國與會者
    private Integer chinaParticipants;
    //中國演講嘉賓
    private Integer chinaSpeakers;
    //中國其他
    private Integer chinaOther;
    //中國總數
    private Integer chinaTotal;
    //海外
    //海外與會者
    private Integer overseaParticipants;
    //海外演講嘉賓
    private Integer overseaSpeakers;
    //海外其他
    private Integer overseaOther;
    //海外總數
    private Integer overseaTotal;

    //2.展覽
    //總數:
    //參展商總數
    private Integer totalExhibitorsNum;
    // 當中設 9 平方米或以上標準展位的參展商總數
    private Integer totalBooth;
    //買家總數
    private Integer totalBuyer;
    //專業觀眾總數
    private Integer totalAudience;
    //觀展公眾總數
    private Integer totalPublic;
    //其他
    @Column(columnDefinition = "text")
    private String exhibitionOther;
    @Column(columnDefinition = "text")
    private String exhibitionOtherRemark;
    //其他總數
    private Integer exhibitionTotalOther;
    //澳門:
    //澳門參展商
    private Integer macaoExhibitorsNum;
    //澳門當中設 9 平方米或以上標準展位的參展商
    private Integer macaoBooth;
    // 澳門買家
    private Integer macaoBuyer;
    //澳門專業觀眾
    private Integer macaoAudience;
    //澳門觀展公眾
    private Integer macaoPublic;
    //澳門其他
    private Integer exhibitionMacaoOther;
    //中國(包括內地、香港及台灣地區):
    //中國參展商
    private Integer chinaExhibitors;
    //中國當中設 9 平方米或以上標準展位的參展商
    private Integer chinaBooth;
    //中國買家
    private Integer chinaBuyer;
    //中國專業觀眾
    private Integer chinaAudience;
    //中國觀展公眾
    private Integer chinaPublic;
    //中國其他
    private Integer exhibitionChinaOther;
    //海外
    //海外參展商
    private Integer overseaExhibitors;
    //海外當中設 9 平方米或以上標準展位的參展商
    private Integer overseaBooth;
    //海外買家
    private Integer overseaBuyer;
    // 海外專業觀眾
    private Integer overseaAudience;
    //海外觀展公眾
    private Integer overseaPublic;
    //海外其他
    private Integer exhibitionOverseaOther;


    //有(請於下表提供資料) 沒有(請跳至第4.2項)
    @Column(columnDefinition = "tinyint default 0")
    private Boolean whetherSupport;
    //4.1 有否向其他機構申請財務資助或其他支持項目？
    @ElementCollection
    @CollectionTable(name = "encourage_convention_project_support")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<EncourageConventionProjectSupport> projectSupport;

    //有(請於下表提供資料) 沒有(請跳至第4.3項)
    @Column(columnDefinition = "tinyint default 0")
    private Boolean whetherActivityIncome;
    //4.2 整體活動收入預算明細 (除本局資助金額外，列明有關活動的所有收入，如參會/參展費用、廣告收入、贊助等)
    @ElementCollection
    @CollectionTable(name = "encourage_convention_activity_income")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<EncourageConventionActivityIncome> activityIncome;

    //4.3 整體活動支出預算明細 (請列明有關活動的所有支出，包括向本局申請資助項目)
    @ElementCollection
    @CollectionTable(name = "encourage_convention_activity_expenditure")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<EncourageConventionActivityExpenditure> activityExpenditure;

    //4.4 申請項目明細 加到里面附件
    @OneToMany(mappedBy = "encourageConvention", cascade = CascadeType.ALL)
    private List<EncourageConventionApplyItems> applyItems;

    //有(請於下表提供資料) 沒有(請跳至第4.3項)
    @Column(columnDefinition = "tinyint default 0")
    private Boolean agree;
    /**
     * 個人企業主/具有50%控股權之澳門居民身份證明文件副本
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_identity")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> identityFiles;

    //機構
    /**
     * 机构名称(英文)
     */
    @Column(length = 125)
    private String nameEn;
    /**
     * 机构名称(葡文)
     */
    @Column(length = 125)
    private String namePt;
    /**
     * M8場地登記編號（營業稅編號）
     */
    @Column(length = 125)
    private String siteRegistrationCode;
    /**
     * 納稅人編號 - 按財政局 M/1 所得補充稅收益申報書所載之資料填寫
     */
    @Column(length = 125)
    private String taxpayerNo;
    //->場所地址
    @Column(length = 255)
    private String streetZh;
    //->城市
    @Column(length = 125)
    private String cityZh;
    //>省份
    @Column(length = 125)
    private String provinceZh;
    //->國家/地區
    @Column(length = 125)
    private String countryZh;
    //->電話
    @Column(length = 50)
    private String tel;
    //->傳真
    @Column(length = 50)
    private String fax;
    //->電郵
    @Column(length = 50)
    private String email;
    //->網址
    @Column(length = 75)
    private String website;

    //最終申请金額
    @Column(length = 50)
    private String finalSum="0.00";

    //最終支付金額
    @Column(length = 50)
    private String finalPaySum="0.00";

    /**
     * 補齊資料時間
     */
    private Date supplementinfoTime;
    //资助比例
    private Integer fundingRate;

    //預審公函編號
    @Column(length = 50)
    private String officiaLetterNo;

    /**
     * 034待補充資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_supplementinfo_file")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> supplementinfoFiles;

    /*澳門特別行政區政府公共資產監督管理局的更新返回ID*/
    private Integer gpsapUpdateId;
    /*澳門特別行政區政府公共資產監督管理局的添加返回ID*/
    private Integer gpsapAddId;
    /**
     * 是否同步
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isSyncing;

    @Enumerated(EnumType.STRING)
    private FundingMethods fundingMethods;
    //the gap between anticipation and actually of reasons for income
    //等待
   /* private String reasonIncomeGap;
    //
    private String reasonSpendingGap;*/
    //
    private Boolean relatedPartyApplicable;

    //預算預留年份
    @Column(length = 50)
    private String remainYear;
    //取消申請預審
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isCancelPrejudication;
    //取消申請結算q
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isCancelSettlement;

    // 在保存前触发
    @PrePersist
    @PreUpdate
    void prePersistSaveOrUpdate() {
        if (ObjectUtil.isEmpty(buildGroupArea)) {
            buildGroupArea = 0.00;
        }
        if (ObjectUtil.isEmpty(buildPrice)) {
            buildPrice = 0.00;
        }
        if (ObjectUtil.isEmpty(buildTotalPrice)) {
            buildTotalPrice = 0.00;
        }
        if (ObjectUtil.isEmpty(exhibitionGroupArea)) {
            exhibitionGroupArea = 0.00;
        }
        if (ObjectUtil.isEmpty(exhibitionPrice)) {
            exhibitionPrice = 0.00;
        }
        if (ObjectUtil.isEmpty(exhibitionTotalPrice)) {
            exhibitionTotalPrice = 0.00;
        }
        if (ObjectUtil.isEmpty(apartGroupArea)) {
            apartGroupArea = 0.00;
        }
        if (ObjectUtil.isEmpty(apartPrice)) {
            apartPrice = 0.00;
        }
        if (ObjectUtil.isEmpty(apartTotalPrice)) {
            apartTotalPrice = 0.00;
        }
        if (ObjectUtil.isEmpty(meetingArea)) {
            meetingArea = 0.00;
        }
        if (ObjectUtil.isEmpty(spaceSpecify)) {
            spaceSpecify = "0.00";
        }
        if (ObjectUtil.isEmpty(expensesMoney)) {
            expensesMoney = 0.00;
        }
        if (ObjectUtil.isEmpty(preMoney)) {
            preMoney = 0.00;
        }
        if (ObjectUtil.isEmpty(setMoney)) {
            setMoney = "0.00";
        }
        if (ObjectUtil.isEmpty(expensesMoney)) {
            expensesMoney = 0.00;
        }
        if (finalSum == null || finalSum.trim().isEmpty()) {
            finalSum = "0.00";
        }

        if (finalPaySum == null || finalPaySum.trim().isEmpty()) {
            finalPaySum = "0.00";
        }
    }


    public EncourageConvention(Encourage e) {
        BeanUtils.copyProperties(e, this);
    }

    public EncourageConvention(EncourageConventionVO vo) {
        copyProperties(vo, this, IGNORE_PROPERTIES);

        if (!CollectionUtils.isEmpty(vo.getApplyItems())) {
            List<EncourageConventionApplyItems> collect =
                    vo.getApplyItems().stream().map(EncourageConventionApplyItems::new).peek(a -> a.setEncourageConvention(this)).collect(Collectors.toList());
            String totalSum = collect.stream()
                    .map(EncourageConventionApplyItems::getApplySum)
                    .filter(StringUtils::isNotEmpty)
                    .map(BigDecimal::new)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .toString();
            String finalSum = collect.stream()
                    .map(EncourageConventionApplyItems::getApproveSum)
                    .filter(StringUtils::isNotEmpty)
                    .map(BigDecimal::new)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .toString();
            String finalPaySum = collect.stream()
                    .map(EncourageConventionApplyItems::getActpaySum)
                    .filter(StringUtils::isNotEmpty)
                    .map(BigDecimal::new)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .toString();
            collect.forEach(item -> item.setTotalSum(totalSum));
            this.setApplyAmount(Double.parseDouble(totalSum));
            this.setFinalSum(finalSum);
            this.setFinalPaySum(finalPaySum);
            this.setApplyItems(collect);
        }

        if (!CollectionUtils.isEmpty(vo.getActivityExpenditure())) {
            List<EncourageConventionActivityExpenditure> collect =
                    vo.getActivityExpenditure().stream().map(EncourageConventionActivityExpenditure::new).collect(Collectors.toList());
            String totalSum = collect.stream()
                    .map(EncourageConventionActivityExpenditure::getSum)
                    .filter(StringUtils::isNotEmpty)
                    .map(BigDecimal::new)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .toString();
            collect.forEach(item -> item.setTotalSum(totalSum));
            this.setActivityExpenditure(collect);
        }

        if (!CollectionUtils.isEmpty(vo.getActivityIncome())) {
            List<EncourageConventionActivityIncome> collect =
                    vo.getActivityIncome().stream().map(EncourageConventionActivityIncome::new).collect(Collectors.toList());
            String totalSum = collect.stream()
                    .map(EncourageConventionActivityIncome::getSum)
                    .filter(StringUtils::isNotEmpty)
                    .map(BigDecimal::new)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .toString();
            collect.forEach(item -> item.setTotalSum(totalSum));
            this.setActivityIncome(collect);
        }

        if (!CollectionUtils.isEmpty(vo.getProjectSupport())) {
            List<EncourageConventionProjectSupport> collect =
                    vo.getProjectSupport().stream().map(EncourageConventionProjectSupport::new).collect(Collectors.toList());
            this.setProjectSupport(collect);
        }

        if (!CollectionUtils.isEmpty(vo.getEstimatedSchedule())) {
            List<EncourageConventionEstimatedSchedule> collect =
                    vo.getEstimatedSchedule().stream().map(EncourageConventionEstimatedSchedule::new).collect(Collectors.toList());
            this.setEstimatedSchedule(collect);
        }
        if (!CollectionUtils.isEmpty(vo.getOrganizations())) {
            List<EncourageConventionOrganization> collect =
                    vo.getOrganizations().stream().map(EncourageConventionOrganization::new).peek(a -> a.setEncourageConvention(this)).collect(Collectors.toList());
            this.setOrganizations(collect);
        }

        if (!CollectionUtils.isEmpty(vo.getEventVenue())) {
            List<EncourageConventionEventVenue> collect =
                    vo.getEventVenue().stream().map(EncourageConventionEventVenue::new).collect(Collectors.toList());
            this.setEventVenue(collect);
        }

        Long liaisonSubId = vo.getLiaisonSubId();
        if (null != liaisonSubId) {
            Liaison liaisonSub = new Liaison();
            liaisonSub.setId(liaisonSubId);
            this.setLiaisonSub(liaisonSub);
        }
        List<MeetingDateVO> meetingDates = vo.getMeetingDates();
        if (!CollectionUtils.isEmpty(meetingDates)) {
            this.meetingDates = meetingDates.stream().map(MeetingDate::new).collect(Collectors.toList());
        }
        List<MeetingDateVO> activityDates = vo.getActivityDates();
        if (!CollectionUtils.isEmpty(activityDates)) {
            this.activityDates = activityDates.stream().map(MeetingDate::new).collect(Collectors.toList());
        }
        List<ConventionHistoryVO> conventionHistory = vo.getConventionHistory();
        if (!CollectionUtils.isEmpty(conventionHistory)) {
            this.conventionHistory = conventionHistory.stream().map(ConventionHistory::new).collect(Collectors.toList());
        }
        List<ConventionOpenTimeVO> openTimes = vo.getOpenTimes();
        if (!CollectionUtils.isEmpty(openTimes)) {
            this.openTimes = openTimes.stream().map(ConventionOpenTime::new).collect(Collectors.toList());
        }
        List<MeetingRoomVO> meetingRooms = vo.getMeetingRooms();
        if (!CollectionUtils.isEmpty(meetingRooms)) {
            this.meetingRooms = meetingRooms.stream().map(MeetingRoom::new).collect(Collectors.toList());
        }
        List<ConventionDiningVO> conventionDinings = vo.getConventionDinings();
        if (!CollectionUtils.isEmpty(conventionDinings)) {
            this.conventionDinings = conventionDinings.stream().map(ConventionDining::new).collect(Collectors.toList());
        }
        List<MeetingRoomVO> exMeetingRooms = vo.getExMeetingRooms();
        if (!CollectionUtils.isEmpty(exMeetingRooms)) {
            this.exMeetingRooms = exMeetingRooms.stream().map(MeetingRoom::new).collect(Collectors.toList());
        }
        List<ConventionOtherGovVO> conventionOtherGovs = vo.getConventionOtherGovs();
        if (!CollectionUtils.isEmpty(conventionOtherGovs)) {
            this.conventionOtherGovs = conventionOtherGovs.stream().map(ConventionOtherGov::new).collect(Collectors.toList());
        }
        List<EncourageConventionRelatedTradeVO> relatedGrade = vo.getRelatedGrade();
        if (!CollectionUtils.isEmpty(relatedGrade)){
            this.relatedGrade = relatedGrade.stream().map(EncourageConventionRelatedTrade::new).collect(Collectors.toList());
        }
        if (null != vo.getRecordPreUserId()) {
            User applicant = new User();
            applicant.setId(vo.getRecordPreUserId());
            this.setRecordPreUser(applicant);
        }
        if (null != vo.getRecordSetUserId()) {
            User applicant = new User();
            applicant.setId(vo.getRecordSetUserId());
            this.setRecordSetUser(applicant);
        }
        //會議及展覽匯總參展人數
        int macaoExhibitorsNum = ObjectUtil.isNotEmpty(vo.getMacaoExhibitorsNum())?vo.getMacaoExhibitorsNum():0;
        int chinaExhibitors = ObjectUtil.isNotEmpty(vo.getChinaExhibitors())?vo.getChinaExhibitors():0;
        int overseaExhibitors = ObjectUtil.isNotEmpty(vo.getOverseaExhibitors())?vo.getOverseaExhibitors():0;
        //参展商总数
        this.totalExhibitorsNum = macaoExhibitorsNum + chinaExhibitors + overseaExhibitors;
        int overseaBooth = ObjectUtil.isNotEmpty(vo.getOverseaBooth())?vo.getOverseaBooth():0;
        int macaoBooth = ObjectUtil.isNotEmpty(vo.getMacaoBooth())?vo.getMacaoBooth():0;
        int chinaBooth = ObjectUtil.isNotEmpty(vo.getChinaBooth())?vo.getChinaBooth():0;
        //當中設 9 平方米或以上標準展位的參展商
        this.totalBooth = overseaBooth + macaoBooth + chinaBooth;
        int macaoBuyer = ObjectUtil.isNotEmpty(vo.getMacaoBuyer())?vo.getMacaoBuyer():0;
        int chinaBuyer = ObjectUtil.isNotEmpty(vo.getChinaBuyer()) ? vo.getChinaBuyer() : 0;
        int overseaBuyer = ObjectUtil.isNotEmpty(vo.getOverseaBuyer())?vo.getOverseaBuyer():0;
        //買家
        this.totalBuyer = macaoBuyer + chinaBuyer + overseaBuyer;
        int macaoAudience = ObjectUtil.isNotEmpty(vo.getMacaoAudience())?vo.getMacaoAudience():0;
        int chinaAudience = ObjectUtil.isNotEmpty(vo.getChinaAudience())?vo.getChinaAudience():0;
        int overseaAudience = ObjectUtil.isNotEmpty(vo.getOverseaAudience())?vo.getOverseaAudience():0;
        //專業觀眾
        this.totalAudience = macaoAudience+chinaAudience+overseaAudience;
        int macaoPublic = ObjectUtil.isNotEmpty(vo.getMacaoPublic())?vo.getMacaoPublic():0;
        int chinaPublic = ObjectUtil.isNotEmpty(vo.getChinaPublic())?vo.getChinaPublic():0;
        int overseaPublic = ObjectUtil.isNotEmpty(vo.getOverseaPublic())?vo.getOverseaPublic():0;
        //觀展公眾
        this.totalPublic = macaoPublic + chinaPublic+overseaPublic;

        //其他
        int exhibitionMacaoOther = ObjectUtil.isNotEmpty(vo.getExhibitionMacaoOther())?vo.getExhibitionMacaoOther():0;
        int exhibitionChinaOther = ObjectUtil.isNotEmpty(vo.getExhibitionChinaOther())?vo.getExhibitionChinaOther():0;
        int exhibitionOverseaOther = ObjectUtil.isNotEmpty(vo.getExhibitionOverseaOther())?vo.getExhibitionOverseaOther():0;
        this.exhibitionTotalOther = exhibitionChinaOther+exhibitionMacaoOther+exhibitionOverseaOther;

        int totalParticipants = ObjectUtil.isNotEmpty(vo.getTotalParticipants())?vo.getTotalParticipants():0;
        int totalSpeakers = ObjectUtil.isNotEmpty(vo.getTotalsSpeakers())?vo.getTotalsSpeakers():0;
        int totalOther = ObjectUtil.isNotEmpty(vo.getTotalOther())?vo.getTotalOther():0;
        this.total=totalParticipants+totalSpeakers+totalOther;
        int macaoParticipants = ObjectUtil.isNotEmpty(vo.getMacaoParticipants())?vo.getMacaoParticipants():0;
        int macaoSpeakers = ObjectUtil.isNotEmpty(vo.getMacaoSpeakers())?vo.getMacaoSpeakers():0;
        int macaoOther = ObjectUtil.isNotEmpty(vo.getMacaoOther())?vo.getMacaoOther():0;
        this.macaoTotal=macaoParticipants+macaoSpeakers+macaoOther;
        int chinaParticipants = ObjectUtil.isNotEmpty(vo.getChinaParticipants())?vo.getChinaParticipants():0;
        int chinaSpeakers = ObjectUtil.isNotEmpty(vo.getChinaSpeakers())?vo.getChinaSpeakers():0;
        int chinaOther = ObjectUtil.isNotEmpty(vo.getChinaOther())?vo.getChinaOther():0;
        this.chinaTotal=chinaParticipants+chinaSpeakers+chinaOther;
        int overseaParticipants = ObjectUtil.isNotEmpty(vo.getOverseaParticipants())?vo.getOverseaParticipants():0;
        int overseaSpeakers = ObjectUtil.isNotEmpty(vo.getOverseaSpeakers())?vo.getOverseaSpeakers():0;
        int overseaOther = ObjectUtil.isNotEmpty(vo.getOverseaOther())?vo.getOverseaOther():0;
        this.overseaTotal=overseaParticipants+overseaSpeakers+overseaOther;
    }

    @Override
    public EncourageConventionVO toVO() {
        return toVO(false);
    }


    /*
    * 轉換為公監辦接口
    * */
    public GPSAPADDReq toGpsapAddReq(EncourageConventionReportVO conventionReportVO, List<String> ids, Integer gpsapUpdateId){
        GPSAPADDReq addreq = new GPSAPADDReq();
        EncourageConventionVO convention = conventionReportVO.getEncourageConventionVO();
        List<GPSAPADDReq.AddReq> list = new ArrayList<>();
        SimpleDateFormat year = new SimpleDateFormat("yyyy");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        SimpleDateFormat dateFormatApply = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        GPSAPADDReq.AddReq req = new GPSAPADDReq.AddReq();
        req.setSyncId(convention.getCode());
        /*申请编号*/
        req.setApplyNo(convention.getCode());
        /*申请年度*/
        req.setApplyYear(ObjectUtil.isNotEmpty(convention.getApplyTime())?Long.valueOf(year.format(convention.getApplyTime())):Long.valueOf(year.format(new Date())));
        req.setFundingSchemeId(gpsapUpdateId);
        req.setContactPerson(ObjectUtil.isNotEmpty(convention.getLiaison())?convention.getLiaison().getNameZh():"IPIM");
        req.setTelephone(ObjectUtil.isNotEmpty(convention.getLiaison())?convention.getLiaison().getPhone():"66666666");
        /*受资助人[機構名稱]*/
        req.setGranteeNameTc(ObjectUtil.isNotEmpty(convention.getInstitution())?convention.getInstitution().getNameZh():null);

        req.setGranteeNamePt(ObjectUtil.isNotEmpty(convention.getInstitution())?ObjectUtil.isNotEmpty(convention.getInstitution().getNameEn())?convention.getInstitution().getNameEn():convention.getInstitution().getNameZh():null);


        req.setSubmittedTime(ObjectUtil.isNotEmpty(convention.getApplyTime())?dateFormatApply.format(convention.getApplyTime()):dateFormatApply.format(new Date()));
        req.setStatus("SUBMITTED");
        //收入
        List<GPSAPADDReq.ProjectPart> projectPartList = new ArrayList<>();
        List<GPSAPADDReq.Income> incomeList = new ArrayList<>();
        List<EncourageConventionReportActivityIncomeVO> applyItems = conventionReportVO.getReportActivityIncome();
        if (ObjectUtil.isNotEmpty(applyItems)){
            int i = 0;
           for (EncourageConventionReportActivityIncomeVO activityIncome : applyItems) {
               GPSAPADDReq.Income income = new GPSAPADDReq.Income();
                income.setTitle(StringUtils.isNotEmpty(activityIncome.getDescription())?activityIncome.getDescription():"");
                income.setAccountingCategory(StringUtils.isNotEmpty(activityIncome.getIncomeItem())?activityIncome.getIncomeItem():"04-03-00-00-02");
                income.setAmount(StringUtils.isNotEmpty(activityIncome.getSum())?Double.valueOf(activityIncome.getSum()):0);
                income.setFund(false);
                income.setApprovedAmount(StringUtils.isNotEmpty(activityIncome.getSum())?Double.valueOf(activityIncome.getSum()):null);
                income.setOrders(i++);
                income.setInvoiceNo(null);
                incomeList.add(income);
            }
        }
        GPSAPADDReq.ProjectPart projectPart = new GPSAPADDReq.ProjectPart();
        //[活動項目名稱（受資助）]

        log.info("{}同步請求公監局URL：{}","【澳門特別行政區政府公共資產監督管理局】",convention.getActivityName());
        projectPart.setProjectName(ObjectUtil.isNotEmpty(convention.getActivityName())?convention.getActivityName():null);
        projectPart.setProjectStartDate(ObjectUtil.isNotEmpty(convention)?dateFormat.format(convention.getMeetingFrom()):null);
        projectPart.setProjectEndDate(ObjectUtil.isNotEmpty(convention)?dateFormat.format(convention.getMeetingTo()):null);
        //主办机构
        projectPart.setOrganiserOther(ObjectUtil.isNotEmpty(convention.getActivity())?convention.getActivity().getOrganizerStrs():"");
        //协办机构
        projectPart.setCoOrganiser(ObjectUtil.isNotEmpty(convention.getActivity())?convention.getActivity().getHelperStrs():"");
        projectPart.setNumberOfStaff(ObjectUtil.isNotEmpty(conventionReportVO.getActualTotalParticipants())?conventionReportVO.getActualTotalParticipants():0 );
        projectPart.setNumberOfExpected(ObjectUtil.isNotEmpty(convention.getTotalParticipants())?convention.getTotalParticipants():0 );
        projectPart.setNumberOfGuests(ObjectUtil.isNotEmpty(conventionReportVO.getActualTotalParticipants())?conventionReportVO.getActualTotalParticipants():0 );
        projectPart.setNumberOfActual(ObjectUtil.isNotEmpty(conventionReportVO.getActualTotalsSpeakers())?conventionReportVO.getActualTotalsSpeakers():0 );


        //判断举办地点是否是澳门：
        if (ObjectUtil.isNotEmpty(convention.getActivity())?"81000".equals(convention.getActivity().getProvinceZh()):false){
            projectPart.setProjectLocation("both");
        }else {
            //暂时未知
            projectPart.setProjectLocation("both");
        }
        projectPart.setOrganiserOther(ObjectUtil.isNotEmpty(convention.getInstitution())?convention.getInstitution().getNameZh():null);


        projectPart.setLocation("Macau");
        projectPart.setIncomeList(incomeList);
        //支出
        List<GPSAPADDReq.SpendingList> spendingList = new ArrayList<>();
        List<EncourageConventionReportExpenditureVO> activityExpenditure = conventionReportVO.getReportExpenditure();
        if (ObjectUtil.isNotEmpty(activityExpenditure)){
            int i = 0;
            for (EncourageConventionReportExpenditureVO expenditure : activityExpenditure) {
                GPSAPADDReq.SpendingList spend = new GPSAPADDReq.SpendingList();
                spend.setTitle(StringUtils.isNotEmpty(expenditure.getDescription())?expenditure.getDescription():"");
                spend.setAccountingCategory(expenditure.getExpenditureItem());
                spend.setAmount(Double.valueOf(expenditure.getSum()));
                spend.setOrders(i++);
                spend.setApprovedAmount(Double.valueOf(expenditure.getSum()));
                spend.setInvoiceNo(null);
                spendingList.add(spend);
            }
        }
        projectPart.setSpendingList(spendingList);
        //關聯關係
        List<GPSAPADDReq.RelatedPartyList>  relatedPartyList = new ArrayList<>();
        List<EncourageConventionRelatedTradeVO> relatedGrade = convention.getRelatedGrade();
        if (ObjectUtil.isNotEmpty(relatedGrade)){
            for (EncourageConventionRelatedTradeVO expenditure : relatedGrade) {
                int i = 0;
                GPSAPADDReq.RelatedPartyList spend = new GPSAPADDReq.RelatedPartyList();
                spend.setOrders(i++);
                spend.setAccountingCategory("05-02-01-00-99");
                spend.setRelatedPartyCompanyName(expenditure.getVendorName());
                spend.setRelatedPartyRelationshipBetween(expenditure.getVendorAndApplicantsRelated());
                spend.setTitle(expenditure.getServiceContents());
                spend.setAmount(1d);
                spend.setRemark(expenditure.getExplaination());
                spend.setInvoiceNo("IPIM");
                spend.setRelatedPartyNameA("IPIM");
                spend.setRelatedPartyContactA("IPIM");
                spend.setRelatedPartyPositionOnCounterpartyA("IPIM");
                spend.setRelatedPartyNameB("IPIM");
                spend.setRelatedPartyContactB("IPIM");
                spend.setRelatedPartyPositionOnCounterpartyB("IPIM");
                relatedPartyList.add(spend);
            }
            projectPart.setRelatedPartyList(relatedPartyList);
        }

        GPSAPADDReq.AttachmentList attachmentList =new GPSAPADDReq.AttachmentList();
        List<GPSAPADDReq.AttachmentList> attachmentLists=new ArrayList<>();
        List<GPSAPADDReq.Attachment> annexList  = new ArrayList<>();
        if (CollectionUtils.isEmpty(ids) || ids.size()<=0) {
            attachmentList.setAttachment(annexList);
            attachmentLists.add(attachmentList);
            projectPart.setAttachmentList(attachmentLists);
        }else {
            for (String id:ids){
                GPSAPADDReq.Attachment  attachment= new GPSAPADDReq.Attachment();
                attachment.setId(id);
                attachment.setName("attachment");
                annexList.add(attachment);
            }
            attachmentList.setTitle("photo");
            attachmentList.setAttachment(annexList);
            attachmentLists.add(attachmentList);
            projectPart.setAttachmentList(attachmentLists);
        }
        projectPartList.add(projectPart);
        req.setProjectPart(projectPartList);
        list.add(req);

        GPSAPADDReq.Attachments attachments = new GPSAPADDReq.Attachments();
        List<Object> add = new ArrayList<>();
        List<Object> remove = new ArrayList<>();
        if (CollectionUtils.isEmpty(ids) || ids.size()<=0) {
            attachments.setAdd(add);
        }else {
            attachments.setAdd(Collections.singletonList(ids));
        }
        attachments.setRemove(remove);
        addreq.setAttachments(attachments);
        addreq.setData(list);
        return addreq;
    }
    public EncourageConventionVO toVO(boolean includeLazy) {
        EncourageConventionVO vo = new EncourageConventionVO();
        copyProperties(this, vo, IGNORE_PROPERTIES);
        // add by ryan 1109
        if(this.getInstitution()!=null){
            vo.setInstitutionShareholders(this.getInstitution().getInstitutionShareholders());
        }
        if (includeLazy) {
            if (null != liaisonSub) {
                vo.setLiaisonSubId(liaisonSub.getId());
                vo.setLiaisonSub(liaisonSub.toVO());
            }
            if (!CollectionUtils.isEmpty(meetingDates)) {
                vo.setMeetingDates(activityDates.stream().map(MeetingDate::toVO).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(activityDates)) {
                vo.setActivityDates(activityDates.stream().map(MeetingDate::toVO).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(conventionHistory)) {
                vo.setConventionHistory(conventionHistory.stream().map(ConventionHistory::toVO).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(openTimes)) {
                vo.setOpenTimes(openTimes.stream().map(ConventionOpenTime::toVO).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(meetingRooms)) {
                vo.setMeetingRooms(meetingRooms.stream().map(MeetingRoom::toVO).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(conventionDinings)) {
                vo.setConventionDinings(conventionDinings.stream().map(ConventionDining::toVO).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(exMeetingRooms)) {
                vo.setMeetingRooms(exMeetingRooms.stream().map(MeetingRoom::toVO).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(conventionOtherGovs)) {
                vo.setConventionOtherGovs(conventionOtherGovs.stream().map(ConventionOtherGov::toVO).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(relatedGrade)){
                vo.setRelatedGrade(relatedGrade.stream().map(EncourageConventionRelatedTrade::toVO).collect(Collectors.toList()));
            }

            //发起人
            User applicant = this.getRecordPreUser();
            if (null != applicant) {
                vo.setRecordPreUserId(applicant.getId());
                vo.setRecordPreUserName(applicant.getName() + applicant.getLastName() + "(" + applicant.getAccount() + ")");
                vo.setRecordPreUserAccount(applicant.getAccount());
            }
            //发起人
            User setUser = this.getRecordSetUser();
            if (null != setUser) {
                vo.setRecordSetUserId(setUser.getId());
                vo.setRecordSetUserName(setUser.getName() + setUser.getLastName() + "(" + setUser.getAccount() + ")");
                vo.setRecordSetUserAccount(setUser.getAccount());
            }

            if (!CollectionUtils.isEmpty(applyItems)) {
                vo.setApplyItemsSum(applyItems.get(0).getTotalSum());
                vo.setApplyItems(applyItems.stream().map(EncourageConventionApplyItems::toVO).collect(Collectors.toList()));
            }else{
                vo.setApplyItems(Collections.emptyList());
            }

            if (!CollectionUtils.isEmpty(activityExpenditure)) {
                vo.setActivityExpenditureSum(activityExpenditure.get(0).getTotalSum());
                vo.setActivityExpenditure(activityExpenditure.stream().map(EncourageConventionActivityExpenditure::toVO).collect(Collectors.toList()));
            }else{

                EncourageConventionActivityExpenditure activityExpenditure1 = new EncourageConventionActivityExpenditure();
                activityExpenditure1.setExpenditureItem("");
                activityExpenditure.add(activityExpenditure1);
            }

            if (!CollectionUtils.isEmpty(activityIncome)) {
                vo.setActivityIncomeSum(activityIncome.get(0).getTotalSum());
                vo.setActivityIncome(activityIncome.stream().map(EncourageConventionActivityIncome::toVO).collect(Collectors.toList()));
            }else{

                EncourageConventionActivityIncome activityIncomes = new EncourageConventionActivityIncome();
                activityIncomes.setIncomeItem("");
                activityIncome.add(activityIncomes);
            }

            if (!CollectionUtils.isEmpty(projectSupport)) {
                vo.setProjectSupport(projectSupport.stream().map(EncourageConventionProjectSupport::toVO).collect(Collectors.toList()));
            }else{

                EncourageConventionProjectSupport projectSupports = new EncourageConventionProjectSupport();
                projectSupports.setApplicantProject("");
                projectSupport.add(projectSupports);
            }

            if (!CollectionUtils.isEmpty(estimatedSchedule)) {
                vo.setEstimatedSchedule(estimatedSchedule.stream().map(EncourageConventionEstimatedSchedule::toVO).collect(Collectors.toList()));
            }else{

                EncourageConventionEstimatedSchedule estimatedSchedules = new EncourageConventionEstimatedSchedule();
                estimatedSchedules.setPlace("");
                estimatedSchedule.add(estimatedSchedules);
            }
            if (!CollectionUtils.isEmpty(organizations)) {
                vo.setOrganizations(organizations.stream().map(EncourageConventionOrganization::toVO).collect(Collectors.toList()));
            }else{
                vo.setOrganizations(Collections.emptyList());
            }
            if (!CollectionUtils.isEmpty(eventVenue)) {
                vo.setEventVenue(eventVenue.stream().map(EncourageConventionEventVenue::toVO).collect(Collectors.toList()));
            }else{
                EncourageConventionEventVenue eventVenues = new EncourageConventionEventVenue();
                eventVenues.setVenueName("");
                eventVenue.add(eventVenues);
            }

        }
        return vo;
    }
    public GPSAPUPDATEReq toGPSAPUPDATEReq(EncourageConventionVO convention){
        List<GPSAPUPDATEReq.UpdateReq> list = new ArrayList<>();
        GPSAPUPDATEReq.UpdateReq updateReq = new GPSAPUPDATEReq.UpdateReq();
        updateReq.setSyncId(convention.getCode());
        updateReq.setFundId("gpsap");
        updateReq.setHidden(false);
        updateReq.setNamePt(convention.getActivityNameEn());
        updateReq.setNameTc(convention.getActivityName());
        list.add(updateReq);
        GPSAPUPDATEReq req = new GPSAPUPDATEReq();
        req.setData(list);
        return req;
    }
}
