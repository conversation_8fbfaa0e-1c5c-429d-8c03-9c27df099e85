package com.exhibition.domain.apply;

import com.exhibition.vo.apply.ParticipateAttendAbroadVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: ParticipateAttendBroad
 * @description: 境外參展
 * @author: ShiXin
 * @create: 2020-03-12 11:33
 **/
@Data
@NoArgsConstructor
@Entity
@Table(name = "participate_abroad")
public class ParticipateAttendAbroad extends Participate<ParticipateAttendAbroad,ParticipateAttendAbroadVO>
        implements Serializable {

    private static final long serialVersionUID = -2863057685173892213L;


    /**
     * 場地登記編號（營業稅檔案編）
     */
    private String siteRegistration;


    /**
     * 參展產品或者服務
     */
    private String exhibitionProduct;

    /**
     * 為稅務效力並在財政局登記最少滿兩年
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_registration")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> registrationOfBureauFiles;


    /**
     * 至少由澳門居民擁有50%或以上的股權及控股權
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_macao")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> macaoShareholderFiles;

    /**
     * 不具備上述資格之申請單位，但具“澳門製造”、“澳門品牌”、“代理葡語國家產品”證明之企業可獲酌情考慮有關申請資格；
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_other")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> otherFiles;

    /**
     * 納稅人名稱相同之企業商號獲本局接納參展後，不得以同一納稅人名稱屬下之其他企業商號在同一展會再次向本局報名參展；
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_taxpayer")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> taxpayerFiles;

    /**
     * 50%或以上股東相同之有限公司或由同一企業主以不同名稱開設之企業商號，不得重覆在同一展會內向本局報名參展；
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_shareholder")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> shareholderSamesFiles;

    /**
     * 由相同/不同之納稅人開設之同名企業商號，不得重覆在同一展會內向本局報名參展。
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_different")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> differentTaxpayerFiles;

    /**
     * 申請單位簡介
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_unit")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> unitIntroductionFiles;

    /**
     * 個人企業主/具有50%控股權之澳門居民身份證明文件副本
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_card")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> IDCardFiles;

    /**
     * 備註
     */
    private String remarks;

    /**
     * 是否知悉
     */
    private Boolean know;

    /**
     * 現聲明本申請單位於展會內重覆申請或受惠其他政府部門或機構之資助
     */
    private Boolean reset;

    /**
     * 商業登記證明副本(三個月內有效)
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_business")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> businessRegistrationFiles;

    /**
     * 財政局發出之無欠稅證明書
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_certificate")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> certificateBureauFiles;

    /**
     * 營業稅—最初開業
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_open")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> salesTaxOpenFiles;

    /**
     * 營業稅—徵稅憑單 M/8副本(最近一年)
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_sales")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> salesTaxFiles;

    /**
     *  企業主的有效身份證明文件副本及倘有的授權書
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_powerattorney")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> powerAttorneyFiles;
    /**
     * 財政局發出之營業開業M/1或開業聲明書副本；
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_declaration")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> declarationFiles;

    /**
     * 財政局發出無欠稅證明副本
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_certificate")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> certificateFiles;
    /**
     * 50%或以上企業股東的澳門身份證明文件副本
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_identification")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> identificationFiles;
    /**
     * 參展商簡介
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_exhibitor")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> exhibitorFiles;
    /**
     * 符合「澳門製造」資格：商品必須為澳門生產，並提供由澳門經濟及科技發展局發出的產地來源證明書副本及工業准照(即廠牌)副本；
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_manufacturing")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> manufacturingFiles;
    /**
     * 符合「澳門品牌」資格：須提供由澳門經濟及科技發展局發出的商標註冊證明副本；
     * (+加入上載附件框)
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_brand")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> brandFiles;
    /**
     * 符合「澳門代理海外品牌商品」資格：提供有關商品之代理授權書。
     * (+加入上載附件框)
     */
    @ElementCollection
        @CollectionTable(name = "participate_abroad_overseas")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> overseasFiles;
    /**
     * 其他文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_others")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> othersFiles;

    /**
     * 銀行資料
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_bank")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> bankInformationFiles;
    /**
     * 四大產業及專業服務，證明文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_professional")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> professionalServiceFiles;
    /**
     * 業務介紹文件或執業證明文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_business_or_practice")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> businessOrPracticeFiles;

    @ElementCollection
    private List<String> isBusinessOrPracticeFiles;
    @ElementCollection
    private List<String> isProfessionalServiceFiles;

    /**
     * 參展產品/服務類別
     */
    @ElementCollection
    private List<String> exhibit;

    /**
     * 退款方式
     */
    private EncourageReceive            receive;

    /**
     * 銀行轉賬記錄
     */
    private AttendAbroadBanktransfer            banktransfer;

    /**
     * 支票名稱
     */
    private String            chequeName;

    /**
     * 電商平台是否有
     */
    private Boolean ecommerce;

    /**
     * 電商平台名稱
     */
    private String ecommercename;

    /**
     * 參展目的
     */
//
//    @ElementCollection
//    @CollectionTable(name = "participate_abroad_exhibitionpurpose")
//    @Enumerated(EnumType.STRING)
//    private List<Exhibitionpurpose> exhibitionpurpose;

    @ElementCollection
    private List<String> exhibitionpurpose;
    /**
     * 參展目的其它
     */
    private String otherpurpose;

    /**
     * 选项
     */
    private String natureOption;
    /**
     * 选项
     */
    private String commercial;
    /**
     * m1營業稅
     */
    @ElementCollection
    private List<String> isDeclaration;

    @ElementCollection
    private List<String> isPowerAttorney;

    /**
     * 50%股東證明
     */
    @ElementCollection
    private List<String> isIdentification;
    /**
     * 是否商業及動產登記局
     */
    @ElementCollection
    private List<String> isBusinessRegistration;
    /**
     * 是否其它文件
     */
    @ElementCollection
    private List<String> isOtherRegistration;
    /**
     * 社會保障基金
     */
    @ElementCollection
    private List<String> isFss;
    /**
     * 財政局A
     */
    @ElementCollection
    private List<String> isDsfa;
    /**
     * 財政局B
     */
    @ElementCollection
    private List<String> isDsfb;
    /**
     * 最近一年的合同
     */
    @ElementCollection
    private List<String> isContract;
    /**
     * 照片
     */
    @ElementCollection
    private List<String> isPhone;
    /**
     * 參展商簡介
     */
    @ElementCollection
    private List<String> isExhibitor;

    /**
     * 社會保障基金
     */
    @ElementCollection
    private List<String> isManufacturingFiles;
    @ElementCollection
    private List<String> isBrandFiles;
    @ElementCollection
    private List<String> isOverseasFiles;
    @ElementCollection
    private List<String> isGbaFiles;
    @ElementCollection
    private List<String> isBusinessCopeFiles;
    @ElementCollection
    private List<String> isOthersFiles;
    private String handletterArea;
    private String foodArea;
    private String homeArea;
    private String culturalArea;

    /**
     * 其它文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_otherRegistrationFiles")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> otherRegistrationFiles;
    /**
     * 社會保障基金文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_fssFiles")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> fssFiles;
    /**
     * 財政局A
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_dsfaFiles")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> dsfaFiles;
    /**
     * 財政局B
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_dsfbFiles")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> dsfbFiles;
    /**
     * 最近一年業務相關的商業合同/購貨/銷售性質的單據或證明副本五份(只接受經雙方核實的發票及收據等單據)
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_contractFiles")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> contractFiles;
    /**
     * 營運場所照片兩張，須清晰顯示參展商名稱及其營運環境(適用於場所登記地址為非住宅類別)
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_phoneFiles")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> phoneFiles;
    /**
     * 橫琴粵澳深度合作區
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_gbaFiles")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> gbaFiles;
    /**
     * 經營業務範疇
     */
    @ElementCollection
    @CollectionTable(name = "participate_abroad_businessCopeFiles")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> businessCopeFiles;

    /**\
     * 參展商簡介
     */
    @Column(columnDefinition = "text")
    private String companyProfile;
    /**
     * 提供具葡語國家特色工作坊
     */
    private Boolean isWorkShop;
    /**
     * 工作坊内容
     */
    @Column(columnDefinition = "text")
    private String workShop;
    /**
     * 直播“帶貨”
     */
    private Boolean isLive;


    /**
     * 上傳支付憑證
    @ElementCollection
    @CollectionTable(name = "participate_abroad_pay_order")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert>  payOrderFiles;*/

    public ParticipateAttendAbroad(ParticipateAttendAbroadVO vo) {
        copyProperties(vo, this);
    }

    @Override
    public ParticipateAttendAbroadVO toVO() {
        return toVO(false);
    }

    public ParticipateAttendAbroadVO toVO(boolean includeLazy) {
        ParticipateAttendAbroadVO vo = new ParticipateAttendAbroadVO();
        if (exhibit.contains("1")) {
            vo.setOne(true);
        }
        if (exhibit.contains("2")) {
            vo.setTwo(true);
        }
        if (exhibit.contains("3")) {
            vo.setThree(true);
        }
        if (exhibit.contains("4")) {
            vo.setFour(true);
        }
        if (exhibit.contains("5")) {
            vo.setFive(true);
        }
        if (exhibit.contains("6")) {
            vo.setSix(true);
        }
        if (exhibit.contains("7")) {
            vo.setSeven(true);
        }
        if (exhibit.contains("8")) {
            vo.setEight(true);
        }
        if (exhibit.contains("9")) {
            vo.setNine(true);
        }
        if (exhibit.contains("10")) {
            vo.setTen(true);
        }
        if (exhibit.contains("11")) {
            vo.setEleven(true);
        }
        if (exhibitionpurpose.contains("1")) {
            vo.setExOne(true);
        }
        if (exhibitionpurpose.contains("2")) {
            vo.setExTwo(true);
        }
        if (exhibitionpurpose.contains("3")) {
            vo.setExThree(true);
        }
        if (exhibitionpurpose.contains("4")) {
            vo.setExFour(true);
        }
        if (exhibitionpurpose.contains("5")) {
            vo.setExFive(true);
        }
        if (exhibitionpurpose.contains("6")) {
            vo.setExSix(true);
        }
        if (exhibitionpurpose.contains("OTHER")) {
            vo.setExSeven(true);
        }
        copyProperties(this, vo);
        return vo;
    }

}
