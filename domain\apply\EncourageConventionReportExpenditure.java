package com.exhibition.domain.apply;

import com.exhibition.vo.apply.EncourageConventionReportExpenditureVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Embeddable
public class EncourageConventionReportExpenditure implements Serializable {
    //序
    @Column(length = 20)
    private String serial;
    //支出項目
    @Column(length = 20)
    private String expenditureItem;
    //詳細說明
    @Column(columnDefinition = "text")
    private String description;
    //金額(澳門元)
    @Column(length = 20)
    private String sum;

    public EncourageConventionReportExpenditure(EncourageConventionReportExpenditureVO vo) {
        BeanUtils.copyProperties(vo,this);

    }

    public EncourageConventionReportExpenditureVO toVO() {
        return toVO(false);
    }

    public EncourageConventionReportExpenditureVO toVO(boolean includeLazy) {
        EncourageConventionReportExpenditureVO vo = new EncourageConventionReportExpenditureVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }
}
