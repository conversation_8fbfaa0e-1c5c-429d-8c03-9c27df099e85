package com.exhibition.domain.pay;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.apply.Encourage;
import com.exhibition.vo.pay.StatementVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;

import javax.persistence.CollectionTable;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.List;

//对账
@Data
@NoArgsConstructor
@Entity
@Table(name = "statement")
@JsonView(Encourage.EncourageSimpleView.class)
public class Statement extends BaseEntity {

    private Integer mopSuccessOrders;
    private BigDecimal mopSuccessAmount;
    private Integer hkdSuccessOrders;
    private BigDecimal hkdSuccessAmount;

    @ElementCollection
    @CollectionTable(name = "statement_detail")
    @Fetch(FetchMode.SELECT)
    private List<StatementDetail> details;

    private String orderDate;

    public Statement(StatementVO vo) {
        BeanUtils.copyProperties(vo, this);
    }

    public StatementVO toVO() {
        StatementVO vo = new StatementVO();
        BeanUtils.copyProperties(this, vo);
        return vo;
    }
}
