package com.exhibition.domain.apply;

import com.exhibition.vo.apply.EncourageConventionGradeBudgetVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date 2023/10/19 10:39
 * @describe
 */
@Data
@NoArgsConstructor
@Embeddable
public class EncourageConventionGradeBudget implements Serializable {

    @Column(length = 500)
    private String serial;
    @Column(length = 500)
    private String budgetProject;
    @Column(length = 20,columnDefinition = "decimal(19,2) default 0")
    private BigDecimal budgetPriceMax;
    @Column(length = 20,columnDefinition = "decimal(19,2) default 0")
    private BigDecimal budgetPriceMin;
    @Column(length = 20,columnDefinition = "decimal(19,2) default 0")
    private BigDecimal budgetPriceSupply;
    @Column(length = 20,columnDefinition = "decimal(19,2) default 0")
    private BigDecimal budgetScore;

    @PrePersist
    @PreUpdate
    void prePersistSaveOrUpdate() {
        if (budgetPriceMax == null ) {
            budgetPriceMax = BigDecimal.valueOf(0.00);
        }
        if (budgetPriceMin == null ) {
            budgetPriceMin = BigDecimal.valueOf(0.00);
        }
        if (budgetPriceSupply == null ) {
            budgetPriceSupply = BigDecimal.valueOf(0.00);
        }
        if (budgetScore == null ) {
            budgetScore = BigDecimal.valueOf(0.00);
        }
    }

    public EncourageConventionGradeBudget(EncourageConventionGradeBudgetVO vo){
        BeanUtils.copyProperties(vo,this);
    }

    public EncourageConventionGradeBudget(EncourageConventionGradeBudget encourageConventionGradeBudget) {
        EncourageConventionGradeBudgetVO vo = new EncourageConventionGradeBudgetVO();
        BeanUtils.copyProperties(vo,encourageConventionGradeBudget);
    }


    public EncourageConventionGradeBudgetVO toVO(){
        return toVO(false);
    }

    public EncourageConventionGradeBudgetVO toVO(boolean includeLazy){
        EncourageConventionGradeBudgetVO vo = new EncourageConventionGradeBudgetVO();
        BeanUtils.copyProperties(this,vo);

        return vo;
    }
}
