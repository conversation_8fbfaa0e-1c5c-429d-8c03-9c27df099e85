package com.exhibition.domain.activity;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @desc
 * @date 2020-03-03
 * @since 1.0.0
 */
public enum  ActivityStatus {
    /**
     * 已发布(只用于操作改变展会的状态)
     */
    PUBLISH("發佈"),
    /**
     * 未发布
     */
    UNPUBLISH("不發佈"),
    /**
     * 未开始
     */
    NOTSTART("未開始"),
    /**
     *进行中
     */
    PROGRESS("進行中"),
    /**
     * 已结束
     */
    END("已結束"),
    /**
     * 已取消
     */
    CANCEL("取消");

    private final String name;

    ActivityStatus( String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ActivityStatus getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }

}
