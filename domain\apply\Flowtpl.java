package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.sys.User;
import com.exhibition.vo.apply.FlowtplVO;
import com.exhibition.vo.sys.UserVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cascade;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.MapKeyColumn;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.vo.apply
 * @ClassName: Flowtpl
 * @description: 流程模板
 * @author: ShiXin
 * @create: 2020-03-03 11:36
 **/
@Data
@NoArgsConstructor
@Entity
@Table(name = "flowtpl")
@BatchSize(size = 10)
@JsonView(Flowtpl.FlowtplSimpleView.class)
public class Flowtpl extends BaseEntity implements Serializable {

    private static final long   serialVersionUID     = 9219077569697035915L;

    /**
     * 视图
     */
    public interface FlowtplSimpleView extends Encourage.GlobalSimpleView {};

    /**
     * 本局主办之展会审批人
     */
    public static final  String ACTIVITY             = "ACTIVITY";
    /**
     * 本局组织之境外展会审批人
     */
    public static final  String DEPUTATION           = "DEPUTATION";
    /**
     * 支持及鼓励措施审批人
     */
    public static final  String ENCOURAGE            = "ENCOURAGE";
    /**
     * 035
     */
    public static final String APPOTHER             = "APPOTHER";
    /**
     * 机构认证审批人
     */
    public static final  String INSTITUTION          = "INSTITUTION";
    /**
     * 展会图片审批人
     */
    public static final  String PICTURE              = "PICTURE";
    /**
     * 031非牟利社團赴境外展會設置展位
     */
    public static final  String ENCOURAGE_ATTEND     = "ENCOURAGE_ATTEND";
    /**
     * 032非牟利社團赴境外展會
     */
    public static final  String ENCOURAGE_MISSION     = "ENCOURAGE_MISSION";
    /**
     * 033企業參與本地或境外展會
     */
    public static final  String ENCOURAGE_ENTERPRISE = "ENCOURAGE_ENTERPRISE";
    /**
     * 034會議及展覽資質計劃
     */
    public static final  String ENCOURAGE_CONVENTION = "ENCOURAGE_CONVENTION";
    /**
     * 035會展及商務旅遊展
     */
    public static final  String ENCOURAGE_MICES = "ENCOURAGE_MICES";
    /**
     * 036-1培訓課程或公開考試-保薦現職僱員參與課程及/或考試 
     */
    public static final  String ENCOURAGE_TRAINING = "ENCOURAGE_TRAINING";
    /**
     * 036-2培訓課程或公開考試-籌辦課程
     */
    public static final  String ENCOURAGE_COURSE = "ENCOURAGE_COURSE";

    /**
     * 038 電子商務B2B推廣
     */
    public static final  String ENCOURAGE_ECB2B = "ENCOURAGE_ECB2B";
    /**
     * 039 電子商務B2C推廣
     */
    public static final  String ENCOURAGE_ECB2C = "ENCOURAGE_ECB2C";
    /**
     * 0310 國際會議專業募集計劃
     */
    public static final  String ENCOURAGE_CONFERENCE = "ENCOURAGE_CONFERENCE";
    /**
     *內地赴澳證明申請表
     */
    public static final String APPOTHER_MAINLAND = "APPOTHER_MAINLAND";
    /**
     *電子商務推廣資助計劃
     */
    public static final String ELECOMMERCE = "ELECOMMERCE";


    /**
     * 支持及鼓励措施展后报告审批人
     */
    public static final  String             ENCOURAGE_ENTERPRISE_REPORT = "ENCOURAGE_ENTERPRISE_REPORT";
    /**
     * Attend参展展后报告审批人
     */
    public static final  String             ENCOURAGE_ATTEND_REPORT = "ENCOURAGE_ATTEND_REPORT";
    /**
     * 會議及展覽資助計劃展后报告审批人
     */
    public static final  String             ENCOURAGE_CONVENTION_REPORT = "ENCOURAGE_CONVENTION_REPORT";

    /**
     * Mission参展展后报告审批人
     */
    public static final  String             ENCOURAGE_MISSION_REPORT = "ENCOURAGE_MISSION_REPORT";    /**
     * 流程代码
     */
    @Column(nullable = false, updatable = true, unique = true, length = 100)
    private String             code;
    /**
     * 模版名称
     */
    private String             name;
    /**
     * 展会id
     */
    private Integer exhibitionId;
    /**
     * 审批人员
     */
    @ManyToMany
    @JoinTable(name = "flowtpl_approver",
            inverseJoinColumns = @JoinColumn(name = "approver"))
    @MapKeyColumn(name = "step", length = 20)
    private Map<Integer, User> approvers;

    @Column(columnDefinition = "tinyint default 0")
    private Boolean choose;

    @ManyToMany
    @JoinTable(name = "flowtpl_sonlist", joinColumns = @JoinColumn(name = "flowtpl_id"),
            inverseJoinColumns = @JoinColumn(name = "son_id"))
    @Fetch(FetchMode.SELECT)
    @Cascade(org.hibernate.annotations.CascadeType.ALL)
    private List<FlowtplSon> flowtplSon;


    public Flowtpl(FlowtplVO vo) {
        BeanUtils.copyProperties(vo, this, "approverList", "approvers");
        Map<Integer, UserVO> approvers = vo.getApprovers();
        if (null != approvers) {
            this.approvers = new TreeMap<>();
            approvers.forEach((key, value) -> this.approvers.put(key, new User(value)));
        }
    }

    public FlowtplVO toVO() {
        return toVO(false);
    }

    private FlowtplVO toVO(boolean b) {
        FlowtplVO flowtplVO = new FlowtplVO();
        BeanUtils.copyProperties(this, flowtplVO, "approvers");

        if (!CollectionUtils.isEmpty(flowtplSon)) {

            int a=0;
            Map<Integer, UserVO> approversTreeMap1 = new TreeMap<>();
//            Map<Integer, UserVO> approversTreeMap2 = new TreeMap<>();
//            Map<Integer, UserVO> approversTreeMap3 = new TreeMap<>();
//            Map<Integer, UserVO> approversTreeMap4 = new TreeMap<>();
//            Map<Integer, UserVO> approversTreeMap5 = new TreeMap<>();
            List<UserVO>      list1     = new ArrayList<>();
//            List<UserVO>      list2     = new ArrayList<>();
//            List<UserVO>      list3     = new ArrayList<>();
//            List<UserVO>      list4     = new ArrayList<>();
//            List<UserVO>      list5     = new ArrayList<>();
            if (this.flowtplSon.get(0) != null) {
                this.flowtplSon.get(0).getApprovers().forEach((key,value)->approversTreeMap1.put(key,value.toVO()));
                a++;
            }
//            if (this.flowtplSon.get(1) != null) {
//                this.flowtplSon.get(1).getApprovers().forEach((key,value)->approversTreeMap2.put(key,value.toVO()));
//                a++;
//            }
//            if (this.flowtplSon.get(2) != null) {
//                this.flowtplSon.get(2).getApprovers().forEach((key,value)->approversTreeMap3.put(key,value.toVO()));
//                a++;
//            }
//            if (this.flowtplSon.get(3) != null) {
//                this.flowtplSon.get(3).getApprovers().forEach((key,value)->approversTreeMap4.put(key,value.toVO()));
//                a++;
//            }
//            if (this.flowtplSon.get(4) != null) {
//                this.flowtplSon.get(4).getApprovers().forEach((key,value)->approversTreeMap5.put(key,value.toVO()));
//                a++;
//            }
            Set<Integer> integers = approversTreeMap1.keySet();
            integers.stream().sorted(Comparator.reverseOrder());
            Iterator<Integer> iterator = integers.iterator();
            while (iterator.hasNext()) {
                Integer next = iterator.next();
                if (this.flowtplSon.get(0).getChoose() != null) {
                    //approversTreeMap1.get(next).setChoose(this.flowtplSon.get(0).getChoose());
                    approversTreeMap1.get(next).setOption(this.flowtplSon.get(0).getChoose());
                }
                list1.add(approversTreeMap1.get(next));
            }
//            Set<Integer> integers1 = approversTreeMap2.keySet();
//            integers1.stream().sorted(Comparator.reverseOrder());
//            Iterator<Integer> iterator1 = integers1.iterator();
//            while (iterator1.hasNext()) {
//                Integer next = iterator1.next();
//                if (this.flowtplSon.get(1).getChoose() != null) {
//                    //approversTreeMap2.get(next).setChoose(this.flowtplSon.get(1).getChoose());
//                    approversTreeMap2.get(next).setOption(this.flowtplSon.get(1).getChoose());
//                }
//                list2.add(approversTreeMap2.get(next));
//            }
//            Set<Integer> integers2 = approversTreeMap3.keySet();
//            integers2.stream().sorted(Comparator.reverseOrder());
//            Iterator<Integer> iterator2 = integers2.iterator();
//            while (iterator2.hasNext()) {
//                Integer next = iterator2.next();
//                if (this.flowtplSon.get(2).getChoose() != null) {
//                    //approversTreeMap3.get(next).setChoose(this.flowtplSon.get(2).getChoose());
//                    approversTreeMap3.get(next).setOption(this.flowtplSon.get(2).getChoose());
//                }
//                list3.add(approversTreeMap3.get(next));
//            }
//            Set<Integer> integers3 = approversTreeMap4.keySet();
//            integers3.stream().sorted(Comparator.reverseOrder());
//            Iterator<Integer> iterator3 = integers3.iterator();
//            while (iterator3.hasNext()) {
//                Integer next = iterator3.next();
//                if (this.flowtplSon.get(3).getChoose() != null) {
//                    //approversTreeMap4.get(next).setChoose(this.flowtplSon.get(3).getChoose());
//                    approversTreeMap4.get(next).setOption(this.flowtplSon.get(3).getChoose());
//                }
//                list4.add(approversTreeMap4.get(next));
//            }
//            Set<Integer> integers4 = approversTreeMap5.keySet();
//            integers4.stream().sorted(Comparator.reverseOrder());
//            Iterator<Integer> iterator4 = integers4.iterator();
//            while (iterator4.hasNext()) {
//                Integer next = iterator4.next();
//                if (this.flowtplSon.get(4).getChoose() != null) {
//                    //approversTreeMap5.get(next).setChoose(this.flowtplSon.get(4).getChoose());
//                    approversTreeMap5.get(next).setOption(this.flowtplSon.get(4).getChoose());
//                }
//                list5.add(approversTreeMap5.get(next));
//            }
            flowtplVO.setApproverSize(a);
            List<List<UserVO>> listList=new ArrayList<>();
            listList.add(list1);
//            listList.add(list2);
//            listList.add(list3);
//            listList.add(list4);
//            listList.add(list5);
            flowtplVO.setApproverList(listList);

        } else {
            flowtplVO.setApproverList(Collections.emptyList());
        }
        return flowtplVO;
    }

}
