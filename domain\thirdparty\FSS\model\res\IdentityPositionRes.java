package com.exhibition.domain.thirdparty.FSS.model.res;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/***
 * <AUTHOR>
 * @date 2024/12/18 13:38
 * @describe 身份证明局接口
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class IdentityPositionRes implements Serializable {

    @JsonProperty("asso_cname")
    private String assoCname;
    @JsonProperty("asso_pname")
    private String assoPname;
    @JsonProperty("asso_ename")
    private String assoEname;
    @JsonProperty("asso_piname")
    private String assoPiname;
    @JsonProperty("minute_period_year")
    private String minutePeriodYear;
    @JsonProperty("minute_date")
    private String minuteDate;
    @JsonProperty("expired")
    private Boolean expired;
    @JsonProperty("positions")
    private List<PositionsDTO> positions;
    @JsonProperty("success")
    private Boolean success;
    @JsonProperty("error_data")
    private ErrorDataDTO errorData;
    @JsonProperty("version")
    private String version;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString
    public static class PositionsDTO implements Serializable {
        @JsonProperty("char_cname")
        private String charCname;
        @JsonProperty("char_pname")
        private String charPname;
        @JsonProperty("position_cname")
        private String positionCname;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString
    public static class ErrorDataDTO implements Serializable {
        @JsonProperty("msg")
        private String msg;
    }
}
