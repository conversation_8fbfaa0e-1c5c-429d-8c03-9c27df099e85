package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.sys.User;
import com.exhibition.vo.apply.EncourageElecommerceReceiptVO;
import com.exhibition.vo.apply.EncourageElecommerceVO;
import com.exhibition.vo.apply.EncourageEnterpriseActivityReceiptVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/23 11:44
 * @describe 0311收据
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_elecommerce_receipt")
public class EncourageElecommerceReceipt extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 618231878864931826L;
    private static final String[] IGNORE_PROPERTIES =
            new String[]{ "encourageElecommerce"};
    /*
    申請表 (須填妥並於每版蓋上企業主印章或負責人簽名)
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile1;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe1;
    /*
    * 財政局無欠稅證明文件(申請日前90天內發出)
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile2;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe2;
    /*
    *如屬法人商業企業主，提交商業登記證明副本(申請日前90天內發出)
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile3;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe3;
    /*第二部分：*/
    /*
    * 澳門居民身份證明文件(正面及背面)副本
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile4;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe4;
    /*
    * 營業稅-開業/更改申報表(M/1表格)或由財政局發出的開業聲明書副本
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile5;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe5;
    /*
    * 最近一個財政年度發出之營業稅-徵稅憑單(M/8格式)副本
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile6;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe6;
    /*
    * 報價單(申請日前90天內發出)
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile7;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe7;
    /*
    * 公司簡介資料及擬於本局認可B2B/B2C電商平台推廣之產品資料
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile8;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe8;
    /*第三部分：*/
    //產品聲明書及相關證明文件
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile9;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe9;
    /*-----*/
    /*
    * 最近一個財政年度所得補充稅 A/B 組收益申報書副本(A組收益申報書包括其附列文件
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile10;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe10;
    /*
    * 最近一季由社會保障基金發出的供款記錄副本，或沒有聘用員工之聲明書
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe11;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile11;
    /*
    * 在澳實質營運的證明：銷售單副本 份、企業購貨單副本
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile12;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe12;
    /*
    * 通訊地址證明-具申請者名稱任一單據，如水費、電費、電訊費或銀行月結單
    * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile13;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe13;
    /*
     * 其他
     * */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile14;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe14;
    private String other1;
    /**
     * 記錄日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date recordTime;
    private String                   memo;
    /**
     * 管理员
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private User recordUser;
    /**
     * 展會实体
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "encourage_elecommerce_id", referencedColumnName = "id")
    private EncourageElecommerce encourageElecommerce;

    public EncourageElecommerceReceipt(EncourageElecommerceReceiptVO v) {
        BeanUtils.copyProperties(v, this, IGNORE_PROPERTIES);
        if (v.getEncourageElecommerceVO() != null) {
            EncourageElecommerce ee = new EncourageElecommerce();
            ee.setId(v.getEncourageElecommerceId());
            this.setEncourageElecommerce(ee);
        }
        if (null != v.getRecordUserId()) {
            User applicant = new User();
            applicant.setId(v.getRecordUserId());
            this.setRecordUser(applicant);
        }
    }

    public EncourageElecommerceReceiptVO toVO() {
        EncourageElecommerceReceiptVO vo = new EncourageElecommerceReceiptVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);

        // 展後報告
        EncourageElecommerce encourageElecommerce = this.getEncourageElecommerce();
        if (null != encourageElecommerce) {
            vo.setEncourageElecommerceVO(encourageElecommerce.toVO());
            vo.setEncourageElecommerceId(encourageElecommerce.getId());
        }
        //发起人
        User recordUser = this.getRecordUser();
        if (null != recordUser) {
            vo.setRecordUserId(recordUser.getId());
            vo.setRecordUserName(recordUser.getName()+recordUser.getLastName()+"("+recordUser.getAccount()+")");
            vo.setRecordUserAccount(recordUser.getAccount());
        }
        return vo;
    }
}
