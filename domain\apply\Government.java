package com.exhibition.domain.apply;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Embeddable;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 涉及的政府部門、機構的資助
 * @date 2020-05-27
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Embeddable

public class Government implements Serializable {
    /**
     * 政府部門/機構名稱
     */
    private String name;
    /**
     * 申請/受惠資質内容
     */
    private String supportContent;
    /**
     * 申請結果待回覆/批准/不批准
     */
    private String supportRes;
    /**
     * 申請金額
     */
    private Double supportMoney;

}
