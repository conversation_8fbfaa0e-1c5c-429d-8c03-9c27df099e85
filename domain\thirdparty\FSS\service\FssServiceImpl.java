package com.exhibition.domain.thirdparty.FSS.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.exhibition.base.repository.BaseRepository;
import com.exhibition.base.service.impl.BaseServiceImpl;
import com.exhibition.domain.sys.Institution;
import com.exhibition.domain.thirdparty.DockingType;
import com.exhibition.domain.thirdparty.ExternalApiLog;
import com.exhibition.domain.thirdparty.FSS.model.entity.FSS;
import com.exhibition.domain.thirdparty.FSS.model.req.FssReq;
import com.exhibition.domain.thirdparty.FSS.model.res.FssRes;
import com.exhibition.domain.thirdparty.FSS.model.vo.FssSimpleVO;
import com.exhibition.domain.thirdparty.FSS.model.vo.FssVo;
import com.exhibition.domain.thirdparty.FSS.repository.IFssRepository;
import com.exhibition.service.sys.InstitutionService;
import com.exhibition.service.thirdparty.ExternalApiLogService;
import com.exhibition.util.AESUtils;
import com.exhibition.util.Result;
import com.exhibition.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/29 14:05
 * @describe
 */
@Service
@Slf4j
public class FssServiceImpl extends BaseServiceImpl<FSS, FssVo, Long> implements IFssService {
    @Autowired
    private IFssRepository repository;
    @Value("${fss.PRD}")
    private String PRD;
    @Value("${fss.Authorization}")
    private String AUTHORIZATION;

    private static Integer statusCode;
    @Resource
    private InstitutionService institutionService;

    @Resource
    private ExternalApiLogService externalApiLogService;

    @Value("${WEB.address}")
    private String serviceUrl;

    @Override
    protected BaseRepository<FSS, Long> getRepository() {
        return repository;
    }

    @Override
    public List<JSONObject> findFss(String isVerity) {
        return repository.findFss(isVerity);
    }

    @Override
    public FssRes queryFss(FssReq req,String localToken) throws IOException, InterruptedException {
        //獲得當前時間點
        DateTime currentDate = DateUtil.date();
        log.info("=========================");
        log.info("【社会保障基金查询服务】 接收到的对象：{}", JSON.toJSONString(req));
        CloseableHttpClient closeableHttpClient = HttpClientBuilder.create().build();
        //需删除84-96
        HttpPost httpPost = new HttpPost(PRD);
        log.info("【社会保障基金查询服务】 请求地址:{}",httpPost);
        httpPost.addHeader("Content-Type","application/json");
        //httpPost.addHeader("Authorization","APPCODE "+AUTHORIZATION);
        httpPost.addHeader("Authorization", AUTHORIZATION);
        String json = JSON.toJSONString(req);
        log.info("【社会保障基金查询服务】请求报文：{}",json);
        StringEntity entity = new StringEntity(json, ContentType.create("application/json", "UTF-8"));
        httpPost.setEntity(entity);
        CloseableHttpResponse response = closeableHttpClient.execute(httpPost);
        log.info("實體為：{}",JSON.toJSONString(response));
        statusCode = response.getStatusLine().getStatusCode();
        if (statusCode == HttpStatus.SC_OK){
            String res = EntityUtils.toString(response.getEntity());
            FssRes fssRes = JSON.parseObject(res, FssRes.class);
            log.info("【社会保障基金查询服务】返回的参数：{}",JSON.toJSONString(fssRes));
            log.info("=======结束=======");
            fssRes.setStatusCode(HttpStatus.SC_OK);
            /*log.info("【外部接口日志】：{}",apiLogRepository.save(new ExternalApiLog(currentDate, PRD,
                    json, "社保局", ObjectUtil.isNotEmpty(fssRes.getData())
                    ?fssRes.getData().getFssEmpChnName():"查詢失敗", HttpPost.METHOD_NAME,
                    statusCode.toString(), JSON.toJSONString(fssRes), DateUtil.date())));*/
            ExternalApiLog externalApiLog = new ExternalApiLog(currentDate,PRD,
                    AESUtils.encryptHex(JSON.toJSONString(json)),
                    "社保局",fssRes.getRequestId(),HttpPost.METHOD_NAME,
                    JSON.toJSONString(statusCode),AESUtils.encryptHex(JSON.toJSONString(fssRes)),
                    DateUtil.date());
            Result result = externalApiLogService.commonPostInterface(externalApiLog,
                    localToken, serviceUrl);

            return fssRes;
        }else {
            log.info("【社会保障基金查询服务】查询失败");
            FssRes fssRes = new FssRes();
            fssRes.setSuccess(false);
            fssRes.setStatusCode(HttpStatus.SC_OK);
            /*log.info("【外部接口日志】：{}",apiLogRepository.save(new ExternalApiLog(currentDate,PRD,
                    json,"社保局","查詢失敗",HttpPost.METHOD_NAME,
                    statusCode.toString(),JSON.toJSONString(fssRes),DateUtil.date())));*/
            ExternalApiLog externalApiLog = new ExternalApiLog(currentDate,PRD,
                    AESUtils.encryptHex(JSON.toJSONString(json)),
                    "社保局",fssRes.getRequestId(),HttpPost.METHOD_NAME,
                    JSON.toJSONString(statusCode),AESUtils.encryptHex(JSON.toJSONString(fssRes)),
                    DateUtil.date());
            Result result = externalApiLogService.commonPostInterface(externalApiLog,
                    localToken, serviceUrl);
            return fssRes;
        }
    }

    @Override
    public List<FSS> findByisVerify(Boolean verify, DockingType type) {
        return repository.findByisVerifyAndType(verify,type);
    }

    @Override
    public List<FSS> list(FssSimpleVO simpleVO) {
        return repository.findAll(getSpecification(simpleVO));
    }


    private Specification<FSS> getSpecification(FssSimpleVO fssSimpleVO){
        return (Specification<FSS>)(root,query,cb) ->{
            List<Predicate> predicates = new ArrayList<>();

            // 展團機構名稱
            String name = fssSimpleVO.getInstitutionNameZh();
            if (StringUtils.isNotEmpty(name)) {
                Predicate zh = cb.like(root.get("institution").get("nameZh"), StringUtils.wrapByPercent(name));
                Predicate en = cb.like(root.get("institution").get("nameEn"), StringUtils.wrapByPercent(name));
                Predicate pt = cb.like(root.get("institution").get("namePt"), StringUtils.wrapByPercent(name));
                predicates.add(cb.or(zh, en, pt));
            }
            String dsfNo = fssSimpleVO.getDsfNo();
            if (dsfNo!=null){
                predicates.add(cb.equal(root.get("dsfNo"),dsfNo));
            }
            DockingType type = fssSimpleVO.getType();
            if (type!=null){
                predicates.add(cb.equal(root.get("type"),type));
            }
            String fssEmpNo = fssSimpleVO.getFssEmpNo();
            if (fssEmpNo!=null){
                predicates.add(cb.equal(root.get("fssEmpNo"),fssEmpNo));
            }
            String financialPremisesCode = fssSimpleVO.getFinancialPremisesCode();
            if (financialPremisesCode!=null){
                predicates.add(cb.equal(root.get("financialPremisesCode"),financialPremisesCode));
            }
            query.distinct(true);
            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }


    @Override
    public Page<FSS> findByPage(FssSimpleVO example, Pageable pageable) {
        Page<FSS> page = repository.findAll(getSpecification(example), pageable);
        return page;
    }

    @Override
    public List<FSS> findByAccount(String account, DockingType fundoDeSegurancaSocial) {
        return repository.findByAccountAndType(account,fundoDeSegurancaSocial);
    }

    private Specification<FSS> getSpecification1(FssSimpleVO fssSimpleVO) {
        return (Specification<FSS>) (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            String name = fssSimpleVO.getInstitutionNameZh();
                //獲得機構實體，使用ID還進行比較
                Institution institution = institutionService.findByNameZh(name);
                if (null != institution) {
                    Long id = institution.getId();
                    predicates.add(cb.equal(root.get("institution"), id));
                }
                String dsfNo = fssSimpleVO.getDsfNo();
                if (StringUtils.isNotEmpty(dsfNo)) {
                    predicates.add(cb.equal(root.get("dsfNo"), dsfNo));
                }
                String fssEmpNo = fssSimpleVO.getFssEmpNo();
                if (StringUtils.isNotEmpty(fssEmpNo)) {
                    predicates.add(cb.equal(root.get("fssEmpNo"), fssEmpNo));
                }
                String financialPremisesCode = fssSimpleVO.getFinancialPremisesCode();
                if (StringUtils.isNotEmpty(financialPremisesCode)) {
                    predicates.add(cb.equal(root.get("financialPremisesCode"), financialPremisesCode));
                }
            query.distinct(true);
            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }
}
