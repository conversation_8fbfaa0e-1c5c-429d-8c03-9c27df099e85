package com.exhibition.domain.apply;

import com.exhibition.vo.apply.EncourageConventionReportScheduleVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@Embeddable
public class EncourageConventionReportSchedule implements Serializable {

    //活動類別(會議/展覽/社區活動/特色或體驗活動/其他：請說明)
    @Column(length = 50)
    private String activityCategory;
    //舉辦日期
    @Temporal(TemporalType.TIMESTAMP)
    private Date holdDate;
    // 地點
    @Column(length = 20)
    private String Place;
    //時數
    private Integer hours;

    public EncourageConventionReportSchedule(EncourageConventionReportScheduleVO vo) {
        BeanUtils.copyProperties(vo,this);

    }

    public EncourageConventionReportScheduleVO toVO() {
        return toVO(false);
    }

    public EncourageConventionReportScheduleVO toVO(boolean includeLazy) {
        EncourageConventionReportScheduleVO vo = new EncourageConventionReportScheduleVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }
}
