package com.exhibition.domain.apply;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @desc 034活動類型
 * @date 2020-06-15
 * @since 1.0.0
 */
public enum ConventionType {
    /**
     * 一般會議
     */
    GENERAL_MEET("一般會議"),
    /**
     * 國際性會議
     */
    INT_MEET("國際性會議"),
    /**
     * 一般展覽
     */
    GENERAL_EXHIBIT("一般展覽"),
    /**
     * 專業展覽
     */
    SPECIALTY_EXHIBIT("專業展覽"),
    /**
     * 結合會議及展覽
     */
    MEET_EXHIBIT("結合會議及展覽");

    private final String name;

    ConventionType( String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ConventionType getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
