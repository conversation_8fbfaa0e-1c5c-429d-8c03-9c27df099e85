package com.exhibition.domain.apply;

import com.exhibition.domain.activity.ActivityApplyType;
import com.exhibition.domain.sys.InstitutionalNature;
import com.exhibition.vo.apply.ParticipateRecordVO;
import com.exhibition.vo.apply.ParticipateVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "participate_record")
public class ParticipateRecord extends Participate<ParticipateRecord,ParticipateVO> implements Serializable {

    private static final long serialVersionUID = -4836489452535889196L;
    /**
     * 参会方式
     */
    @Enumerated(EnumType.STRING)
    private AttendType attendType;

    @Enumerated(EnumType.STRING)
    private ActivityApplyType type;
    /**
     * 入住酒店名称
     */
    private String     intoTavern;
    /**
     * 入住日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date       intoTime;
    /**
     * 退房日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date       checkOutTime;
    /**
     * 入住晚数
     */
    private Integer    intoDay;
    /**
     * 入住费用
     */
    private Double     intoCost;
    /**
     * 出發地點
     */
    private String     departPlace;
    /**
     * 出發日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date       departTime;
    /**
     * 離開日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date       leaveTime;
    /**
     * 種類
     */
    @Enumerated(EnumType.STRING)
    private SubsidyType subsidyType;
    /**
     * 補貼費用
     */
    private Double      subsidyCost;
    /**
     * 交通費用
     */
    private Double     trafficCost;
    /**
     * 交通費用支付備注
     */
    private String     trafficRemark;
    /**
     * 簽約方
     */
    private String     contract;
    /**
     * 簽約金額
     */
    private Double     contractCost;
    /**
     * 簽約日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date       contractTime;
    /**
     * 參展簽約信息備注
     */
    private String     contractRemark;

    /**
     *
     * 申請日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date       recordApplyTime;

    /**
     *
     * day1
     */
    private String     day1;
    /**
     *
     * day2
     */
    private String     day2;
    /**
     *
     * day3
     */
    private String     day3;


    /**
     * 是否同意個人資料及公司資料用於2022MIECF線上展廳？
     */
    private String stringField1;

    /**
     * 是否推送31
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isMeeting;
    /**
     * 31推送訂單號
     */
    @Column(columnDefinition = "text")
    private String orderNo;
    /**
     * 机构名称(中文)
     */
    private String nameZh;
    /**
     * 机构名称(英文)
     */
    private String nameEn;
    /**
     * 机构性质
     */
    @Enumerated(value = EnumType.STRING)
    private InstitutionalNature nature;
    /**
     * 国家/地区（中文）
     */
    private String countryZh;
    /**
     * 省份（中文）
     */
    private String provinceZh;
    /**
     * 城市（中文）
     */
    private String cityZh;
    /**
     * 街道（中文）
     */
    private String streetZh;

    /**
     * 貴公司單位業務類別
     */
    private String recordNature;
    private String recordNatureGroupName;
    private String recordNatureGroupNo;
    /**
     * 其它貴公司單位業務類別
     */
    private String otherRcordNature;

    /**
     * 行業分類
     */
    @ElementCollection
    private List<String> businessScope;
    private String otherBusinessScope;
    /**
     * 請說明參加目的
     */
    @ElementCollection
    private List<String> destination;
    private String otherDestination;
    /**
     * 感興趣的產品及服務
     */
    @ElementCollection
    private List<String> products;
    private String otherProducts;
    /**
     * 目標市場
     */
    @ElementCollection
    private List<String> targetMarket;
    private String otherTargetMarket;
    /**\
     * 公司简介
     */
    @Column(columnDefinition = "text")
    private String companyProfile;
    /**\
     * 公司简介EN
     */
    @Column(columnDefinition = "text")
    private String companyProfileEn;
    //是否同意將個人及公司資料用於線上參展
    @Column(columnDefinition = "tinyint default 0")
    private Boolean  isAgree;
    //是否成為尊貴的賣家
    @Column(columnDefinition = "tinyint default 0")
    private Boolean  isBuyer;
    /**\
     * 職稱
     */
    private String titleName;
    /**\
     * 職稱EN
     */
    private String titleNameEn;

    /**
     * 行業分類
     */
    @ElementCollection
    private List<String> businessCategory;
    /**
     * 專業地位
     */
    @ElementCollection
    private List<String> professional;
    /**
     * 擬出席配對時間
     */
    @ElementCollection
    private List<String> pairingTime;
    /**\
     * 採購意向 (供採購商填寫)
     */
    private String purchaseIntention;
    /**\
     * 可供應的產品/服務 (供參展商填寫)
     */
    private String availableProducts;
    /**\
     * 參展商的展位編號
     */
    private String positionNumber;

    /**
     * 擬邀演講嘉賓 (如超過一位演講嘉賓，請複製此欄)
     */
    @ElementCollection
    @CollectionTable(name = "participate_record_person")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<ParticipateRecordPerson> persons;
    /**
     * 入住记录
     */
    @ElementCollection
    @CollectionTable(name = "participate_record_accommodation")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<ParticipateRecordAccommodation> accommodations;
    /**
     * 機構代碼
     */
    private String institutionCode;
    /**
     * 所屬行業
     */
    private String industrySector;
    private String industrySectorOther;
    private String services;
    private String representative;
    private String gender;
    private String phone;
    private String wechatId;
    private String eMail;
    private String website;

    public ParticipateRecord(ParticipateRecordVO vo) {

        copyProperties(vo, this);

        if (!CollectionUtils.isEmpty(vo.getPersons())) {
            List<ParticipateRecordPerson> collect =
                    vo.getPersons().stream().map(ParticipateRecordPerson::new).collect(Collectors.toList());
            this.setPersons(collect);
        }
        if (!CollectionUtils.isEmpty(vo.getAccommodations())) {
            List<ParticipateRecordAccommodation> collect =
                    vo.getAccommodations().stream().map(ParticipateRecordAccommodation::new).collect(Collectors.toList());
            this.setAccommodations(collect);
        }
    }


    @Override
    public ParticipateRecordVO toVO() {
        return toVO(false);
    }

    public ParticipateRecordVO toVO(boolean includeLazy) {
        ParticipateRecordVO vo = new ParticipateRecordVO();
        if (!CollectionUtils.isEmpty(persons)) {
            vo.setPersons(persons.stream().map(ParticipateRecordPerson::toVO).collect(Collectors.toList()));
        }else{
            vo.setPersons(Collections.emptyList());
        }
        if (!CollectionUtils.isEmpty(accommodations)) {
            vo.setAccommodations(accommodations.stream().map(ParticipateRecordAccommodation::toVO).collect(Collectors.toList()));
        }else{
            vo.setAccommodations(Collections.emptyList());
        }
        copyProperties(this, vo);
        return vo;
    }
}
