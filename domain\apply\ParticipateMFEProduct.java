package com.exhibition.domain.apply;

import java.util.Arrays;

public enum ParticipateMFEProduct {

    /**
     * 潮流服飾
     */
    FASHIONABLE_COSTUME("潮流服飾"),
    /**
     * 餐飲業
     */
    CATERING("餐飲業"),
    /**
     * 品牌代理中介
     */
    BRAND_AGENCY("品牌代理中介"),
    /**
     * 服務業
     */
    SERVICE_INDUSTRY("服務業"),

    /**
     * 零售業
     */
    RETAIL("零售業"),
    /**
     * 休閒娛樂業
     */
    LEISURE_INDUSTRY("休閒娛樂業"),
    /**
     * 品牌顧問及設計
     */
    BRAND_CONSULTING_AND_DESIGN("品牌顧問及設計"),
    /**
     * 餐飲/開業設備及技術
     */
    EQUIPMENT_AND_TECHNOLOGY("餐飲/開業設備及技術"),
    /**
     *餐飲業
     */
    ENERGY_EFFICIENCY("餐飲業"),
    /**
     *品牌代理中介
     */
    WASTE_MANAGEMENT_SOLUTIONS("品牌代理中介"),
    /**
     *休閒娛樂業
     */
    WATER("休閒娛樂業"),
    /**
     *零售貿易
     */
    AIR_QUALITY("零售貿易"),
    /**
     *知識產權產業
     */
    ENVIRONMENTAL_PRODUCTS_AND_SERVICES("知識產權產業"),
    /**
     *品牌顧問及設計
     */
    SOIL_REMEDIATION("品牌顧問及設計"),
    /**
     *新零售
     */
    ENVIRONMENTAL_MONITORING("新零售"),
    /**
     *新零售
     */
    GREEN_BUILDING("新零售"),
    /**
     *新零售
     */
    RENEWABLE_ENERGY("新零售"),
    /**
     *新零售
     */
    GREEN_TRANSPORTATION("新零售"),
    CATERINGFood("食品及餐飲類"),
    ONE("家電、日用品、廚房用品"),
    TWO("服裝飾品、文創產品"),
    THREE("技術設備"),
    FOUR("品牌代理"),
    FIVE("顧問諮詢"),
    /**
     * 其他
     */
    OTHER("其他");

    private final String name;

    ParticipateMFEProduct(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ParticipateMFEProduct getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
