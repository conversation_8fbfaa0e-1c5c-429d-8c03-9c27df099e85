
package com.exhibition.domain.apply;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Embeddable;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 涉及的政府部門、機構的資助
 * @date 2020-05-27
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Embeddable
public class SourceRevenue implements Serializable {
    /**
     * 收入來源
     */
    private String name;
    /**
     * 收入revenue/支出expenses
     */
    private String type;
    /**
     * 申請金額
     */
    private Double supportMoney=0.00;

    @PrePersist
    @PreUpdate
    void prePersist() {
//        super.prePersist();
        if (supportMoney == null) {
            supportMoney = 0.00;
        }

    }
}
