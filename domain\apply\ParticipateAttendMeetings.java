package com.exhibition.domain.apply;

import com.exhibition.vo.apply.ParticipateAttendMeetingsVO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;

/**
 * @description: 参会
 **/
@Data
@NoArgsConstructor
@Entity
@Table(name = "participate_meeting")
public class ParticipateAttendMeetings extends Participate<ParticipateAttendMeetings,ParticipateAttendMeetingsVO>
        implements Serializable {

    private static final long serialVersionUID = -2863057685173892213L;



    /**
     * 請説明贵公司/單位業務類別
     */
    @ElementCollection
    private List<String> serviceCategory;

    /**
     * 閣下所事的行菜
     */
    @ElementCollection
    private List<String> business;

    /**
     * 請説明參加目的
     */
    @ElementCollection
    private List<String> aim;

    /**
     * 請選擇感舆趣的產品/服務
     */
    @ElementCollection
    private List<String> service;
    /**
     * 請選擇目標市場
     */
    @ElementCollection
    private List<String> market;

    /**
     * 請選擇你感興趣的活動
     */
    @ElementCollection
    private List<String> interestedActivities;



    public ParticipateAttendMeetings(ParticipateAttendMeetingsVO vo) {
        copyProperties(vo, this);
    }

    @Override
    public ParticipateAttendMeetingsVO toVO() {
        return toVO(false);
    }

    public ParticipateAttendMeetingsVO toVO(boolean includeLazy) {
        ParticipateAttendMeetingsVO vo = new ParticipateAttendMeetingsVO();
        copyProperties(this, vo);
        return vo;
    }

}
