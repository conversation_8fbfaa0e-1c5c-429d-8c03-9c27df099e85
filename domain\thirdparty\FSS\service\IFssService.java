package com.exhibition.domain.thirdparty.FSS.service;

import cn.hutool.json.JSONObject;
import com.exhibition.base.service.BaseService;
import com.exhibition.domain.thirdparty.DockingType;
import com.exhibition.domain.thirdparty.FSS.model.entity.FSS;
import com.exhibition.domain.thirdparty.FSS.model.req.FssReq;
import com.exhibition.domain.thirdparty.FSS.model.res.FssRes;
import com.exhibition.domain.thirdparty.FSS.model.vo.FssSimpleVO;
import com.exhibition.domain.thirdparty.FSS.model.vo.FssVo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/29 14:24
 * @describe IFSSm
 */
public interface IFssService extends BaseService<FSS,FssVo,Long> {


    List<JSONObject> findFss(String isVerity);

    FssRes queryFss(FssReq req,String localToken) throws IOException, InterruptedException;
    @Transactional(readOnly = true)
    List<FSS> findByisVerify(Boolean verify, DockingType type);

    List<FSS> list(FssSimpleVO simpleVO);

    Page<FSS> findByPage(FssSimpleVO vo, Pageable pageable);

    List<FSS> findByAccount(String account, DockingType fundoDeSegurancaSocial);
}
