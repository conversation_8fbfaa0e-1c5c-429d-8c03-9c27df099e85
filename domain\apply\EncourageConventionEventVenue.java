package com.exhibition.domain.apply;

import com.exhibition.vo.apply.EncourageConventionEventVenueVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Embeddable
public class EncourageConventionEventVenue implements Serializable {

    //場地名稱
    @Column(length = 500)
    private String venueName;

    //租用面積(每日平方米)
    @Column(length = 20)
    private String leasedArea;

    //實際使用面積(每日平方米)(會議/展覽及公共設施面積)
    @Column(length = 20)
    private String actualUseArea;

    public EncourageConventionEventVenue(EncourageConventionEventVenueVO vo) {
        BeanUtils.copyProperties(vo,this);

    }

    public EncourageConventionEventVenueVO toVO() {
        return toVO(false);
    }

    public EncourageConventionEventVenueVO toVO(boolean includeLazy) {
        EncourageConventionEventVenueVO vo = new EncourageConventionEventVenueVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }
}
