package com.exhibition.domain.pay;

import com.exhibition.vo.pay.SettlementRecordDetailVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Embeddable
public class SettlementRecordDetail {
        private String settlementDate;      // 清算日期
        private String entryDate;           // 入帳日期
        private String merchantNumber;      // 商戶號
        private String storeNumber;         // 門店號
        private String entryCount;             // 入帳筆數
        private String channel;             // 渠道方
        private String currency;            // 交易貨幣
        private String transactionAmount;      // 交易金額
        private String transactionFee;         // 手續費
        private String merchantCurrency;    // 商戶入帳貨幣
        private String settlementAmount;       // 結算金額

        public SettlementRecordDetail(SettlementRecordDetailVO vo) {
                BeanUtils.copyProperties(vo, this);
        }

        public SettlementRecordDetailVO toVO() {
                SettlementRecordDetailVO vo = new SettlementRecordDetailVO();
                BeanUtils.copyProperties(this, vo);
                return vo;
        }
}
