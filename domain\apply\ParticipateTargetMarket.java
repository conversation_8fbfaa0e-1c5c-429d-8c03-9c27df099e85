package com.exhibition.domain.apply;

import java.util.Arrays;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: Targetmarket
 * @description: 目標市場
 * @author: ShiXin
 * @create: 2020-03-17 16:55
 **/
public enum ParticipateTargetMarket {

    /**
     *澳門
     */
    MACAO("澳門"),
    /**
     *中國內地
     */
    MAINLAND_CHINA("中國內地"),
    /**
     *香港
     */
    HONGKONG("香港"),
    /**
     * 大灣區
     */
    GREATER_BAY_AREA("大灣區"),
    /**
     *台灣地區
     */
    TAIWAN_REGION("台灣地區"),
    /**
     *東南亞地區
     */
    SOUTHEAS_ASIA_REGION("東南亞地區"),
    /**
     *東亞（日本/韓國）
     */
    SOUTHEAS_ASIA_REGION_JH("東亞（日本/韓國）"),
    /**
     *印度及中東地區
     */
    INDIA_AND_MIDDLE_EASE_AREA("印度及中東地區"),
    /**
     *歐洲地區
     */
    EUROPE("歐洲地區"),
    /**
     *葡語係國家及地區
     */
    PORTUGUESE_SPEAKING_COUNTRIES_REGIONS("葡語系國家及地區"),
    /**
     *其他亞洲地區
     */
    OTHER_ASIA_AREA("其他亞洲地區"),
    /**
     *其他亞洲地區
     */
    PORTUGUESE("葡语国家/地区"),
    NORTHAmerica("北美"),
    SOUTHAmerica("南美"),
    HongMacaoTai("港澳台"),
    /**
     *其他國家地區
     */
    OTHER_COUNTRIES("其它国家/地区"),
    /**
     *全球性業務
     */
    WORLDWIDE_COVERAGE("全球性業務");

    private final String name;
    ParticipateTargetMarket(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ParticipateTargetMarket getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
