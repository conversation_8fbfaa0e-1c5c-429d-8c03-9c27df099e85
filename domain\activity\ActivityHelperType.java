package com.exhibition.domain.activity;

import java.util.Arrays;

public enum ActivityHelperType {
    /**
     * 支持單位
     */
    A("支持單位"),
    /**
     * 特邀支持單位
     */
    B("特邀支持單位"),
    /**
     * 官方支持單位
     */
    C("官方支持單位"),
    /**
     * 業界支持單位
     */
    D("業界支持單位"),
    /**
     * 官方承辦單位
     */
    E("官方承辦單位"),
    /**
     * 協辦單位
     */
    F("協辦單位"),
    /**
     * 官方協辦單位
     */
    G("官方協辦單位"),
    /**
     * 特邀協辦單位
     */
    H("特邀協辦單位"),

    I("合作單位"),

    J("承办单位"),

    K("活動主辦單位"),

    L("活動籌辦單位"),

    M("活動協辦單位"),

    O("其他");

    private final String name;

    ActivityHelperType(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ActivityHelperType getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }
        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
