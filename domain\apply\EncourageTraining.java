package com.exhibition.domain.apply;

import com.exhibition.vo.apply.EncourageTrainingVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import javax.persistence.CollectionTable;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 036培訓課程或公開考試-保薦現職僱員參與課程及/或考試 
 * @date 2020-06-09
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_training")
public class EncourageTraining extends Encourage<EncourageTraining, EncourageTrainingVO> implements Serializable {

    private static final long serialVersionUID = 7326230087183142648L;

    //------申請保薦全職僱員參與的活動資料--------------------------------------------------------------------------------------------------
    /**
     * 培訓課程或公開考試名稱（中文）
     */
    private String                    nameZh;
    /**
     * 雇員總數
     */
    private Integer                  employeesCount;
    /**
     * 申請人類型
     */
    private String                   applicantType;
    /**
     * 培訓課程或公開考試名稱（英文）
     */
    private String                    nameEn;
    /**
     * 活動屬於
     */
    private TrainingType              trainingType;
    /**
     * 活動舉辦地址
     */
    private String                    address;
    /**
     * 培訓活動或公開考試的類別
     */
    private TrainingTargetType        targetType;
    /**
     * 授課實體、提供考試或 頒發證照/證明書的機 構名稱
     */
    private String                    certificationBody;
    /**
     * 培訓活動或公開考試的範圍
     */
    @ElementCollection
    private List<String>              scopes;
    /**
     * 報讀或報考期限
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date                       applyDeadline;
    /**
     * 開始時間
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date                       applyStartTime;
    /**
     * 結束時間
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date                       applyEndTime;
    /**
     * 培訓課程的形式及内容簡介
     */
    @ElementCollection
    @CollectionTable(name = "encourage_training_training")
    @Fetch(FetchMode.SELECT)
    private List<TrainingDesc>         trainings;
    /**
     * 公開考試的形式及内容
     */
    @ElementCollection
    @CollectionTable(name = "encourage_training_exam")
    @Fetch(FetchMode.SELECT)
    private List<ExamDesc>             exams;
    /**
     * 保薦參與活動人數
     */
    private Integer                    totalParticipants;
    /**
     * 獲保薦僱員資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_training_employee")
    @Fetch(FetchMode.SELECT)
    private List<SponsorEmployee>      sponsorEmployees;

    /**
     * 申請參加培訓課程或公開考試的財務預算
     */
    @ElementCollection
    @CollectionTable(name = "encourage_training_budget")
    @Fetch(FetchMode.SELECT)
    private List<EncourageBudget>      budgets;
    /**
     * 申請參加培訓課程或公開考試的財務支出
     */
    @ElementCollection
    @CollectionTable(name = "encourage_training_income")
    @Fetch(FetchMode.SELECT)
    private List<EncourageBudget>      incomes;
    /**
     * 总支出
     */
    private Integer                    totalBudget;
    /**
     * 總收入
     */
    private Integer                    totalIncome;
    /**
     * 申請財務支持金額
     */
    private Double                     applyAmount;
    /**
     * 保證金制度
     */
    private String                     marginSystem;
    /**
     * 附件
     */
    @ElementCollection
    @CollectionTable(name = "encourage_training_attachment")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCertAttachment> attachments;
    /**
     * 於其他政府機關就同一活動之支持申請
     */
    @ElementCollection
    @CollectionTable(name = "encourage_training_gov_support")
    @Fetch(FetchMode.SELECT)
    private List<GovSupport>          govSupports;
    /**
     * 声明
     */
    private Boolean             stateAgree;

    public EncourageTraining(EncourageTrainingVO vo) {
        copyProperties(vo, this);
    }

    @Override
    public EncourageTrainingVO toVO() {
        EncourageTrainingVO vo = new EncourageTrainingVO();
        copyProperties(this, vo);
        return vo;
    }
}