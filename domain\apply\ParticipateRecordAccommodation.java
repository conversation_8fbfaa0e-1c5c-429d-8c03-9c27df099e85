package com.exhibition.domain.apply;

import com.exhibition.vo.apply.ParticipateRecordAccommodationVO;
import com.exhibition.vo.apply.ParticipateRecordPersonVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@Embeddable
public class ParticipateRecordAccommodation implements Serializable {
    /**
     * 姓名
     */
    private String name;
    /**
     * 職稱
     */
    private String titleName;
    /**
     * 入住酒店名称
     */
    private String     intoTavern;
    /**
     * 入住日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date intoTime;
    /**
     * 退房日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date       checkOutTime;
    /**
     * 入住晚数
     */
    private Integer    intoDay;
    /**
     * 入住费用
     */
    private Double     intoCost;

    public ParticipateRecordAccommodation(ParticipateRecordAccommodationVO vo) {
        BeanUtils.copyProperties(vo,this);

    }



    public ParticipateRecordAccommodationVO toVO() {
        return toVO(false);
    }

    public ParticipateRecordAccommodationVO toVO(boolean includeLazy) {
        ParticipateRecordAccommodationVO vo = new ParticipateRecordAccommodationVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }
}
