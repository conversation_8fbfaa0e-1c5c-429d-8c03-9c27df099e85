package com.exhibition.domain.apply;

import com.exhibition.vo.apply.EncourageMissionVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;

import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.JoinTable;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: EncourageMission
 * @description: 032Mission代表团
 * @author: ShiXin
 * @create: 2020-03-11 11:17
 **/
@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_mission")
public class EncourageMission
        extends Encourage<EncourageMission, EncourageMissionVO>
        implements Serializable {

    private static final long serialVersionUID = 6425572661567053584L;

    /**
     * 備註
     */
    private String remarks;

    /**
     * 社團名稱
     */
    private String communityName;

    /**
     * 刊登社團設立之《政府公報》副本
     */
    @ElementCollection
    @JoinTable(name = "encourage_mission_newspapers")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> newspapers;
    /**
     * 身份證明局發出之社團登記證明書副本，含領導架構之組成
     */
    @ElementCollection
    @JoinTable(name = "encourage_mission_registration")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> registration;
    /**
     * 申請表格簽署人之身份證明文件副本
     */
    @ElementCollection
    @JoinTable(name = "encourage_mission_identification")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> identification;

    /**
     * 是否 參與展覽會
     */
    @ApiModelProperty("是否 參與展覽會")
    private Boolean isAttendExhibition;

    /**
     * 是否 參與會議
     */
    @ApiModelProperty("是否 參與會議")
    private Boolean isAttendMeeting;
    /**
     * 參展目的
     */
    private String objective;

    /**
     * 預期成效
     */
    private String expectedResults;

    /** 申請附隨文件 代表團名單 */
    @ElementCollection
    @JoinTable(name = "encourage_mission_mission_name_list")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> missionNameList;

    /** 申請附隨文件 代表團日程 */
    @ElementCollection
    @JoinTable(name = "encourage_mission_mission_schedule")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> missionSchedule;

    /** 申請附隨文件 代表團活動收支預算表 */
    @ElementCollection
    @JoinTable(name = "encourage_mission_mission_budget_sheet")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> missionBudgetSheet;

    /**
     * 代表團人數
     */
    private Integer peopleNum;

    /**
     * 機票
     */
    private Boolean haveAirTicket;
    private Integer airTicketNum;
    private Double airTicketFee;
    /**
     * 船票
     */
    private Boolean haveBoatTicket;
    private Integer boatTicketNum;
    private Double boatTicketFee;
    /**
     * 鐵路
     */
    private Boolean haveRailTicket;
    private Integer railTicketNum;
    private Double railTicketFee;
    /**
     * 其他
     */
    private Boolean haveTranOther;
    private String tranOtherNote;
    private Integer tranOtherNum;
    private Double tranOtherFee;
    /**
     * 住宿酒店一
     */
    private String hotelName1;
    private Integer hotelNum1;
    private Double hotelFee1;
    /**
     * 住宿酒店二(如適用)
     */
    private String hotelName2;
    private Integer hotelNum2;
    private Double hotelFee2;
    /**
     * 住宿酒店三(如適用)
     */
    private String hotelName3;
    private Integer hotelNum3;
    private Double hotelFee3;
    /**
     *  是否有 交通費用報價/發票/收據
     */
    private Boolean haveTransportationCost;

    /**
     * 是否有 住宿費用報價/發票/收據
     */
    private Boolean haveAccommodationCost;

    /**
     * 申請及獲批給資助或以其他方式受惠　有／沒有
     */
    private Boolean             haveReceive;
    /**
     * 申請及獲批給資助或以其他方式受惠 注明
     */
    private String              receiveStatement;
    /**
     * 資助項目向參展代表收取費用　有／沒有
     */
    private Boolean             haveCharge;
    /**
     * 資助項目向參展代表收取費用注明
     */
    private String              chargeStatement;

    /**
     * 申請資助項目說明文件
     */
    @ElementCollection
    @JoinTable(name = "encourage_mission_description")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> descriptionFiles;

    /**
     * 聲明
     */
    private String statement;

    public EncourageMission(EncourageMissionVO vo) {
        copyProperties(vo, this);
    }
    public EncourageMission(Encourage e) {
        BeanUtils.copyProperties(e, this);
    }

    public EncourageMissionVO toVO() {
        return toVO(false);
    }
    public EncourageMissionVO toVO(boolean includeLazy) {
        EncourageMissionVO vo = new EncourageMissionVO();

        //super.copyProperties(this, vo);
        copyProperties(this, vo);
        return vo;
    }

}
