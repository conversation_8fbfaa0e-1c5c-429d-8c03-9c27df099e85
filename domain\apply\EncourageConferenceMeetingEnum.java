package com.exhibition.domain.apply;

import java.util.Arrays;

public enum EncourageConferenceMeetingEnum {
    /**
     * “國際會議協會”(ICCA)認證的會議
     */
    MEETING("國際會議協會”(ICCA)認證的會議"),
    /**
     * □具條件申請“國際會議協會”(ICCA)認證之會議
     */
    POTENTIAL_MEETING("具條件申請“國際會議協會”(ICCA)認證之會議");


    private final String name;

    EncourageConferenceMeetingEnum(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static EncourageConferenceMeetingEnum getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }

}
