package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.sys.User;
import com.exhibition.vo.apply.EncourageEnterpriseReceiptVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc 033企業參與本地或境外展會收據
 * @date 2022-12-13
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_enterprise_receipt")
public class EncourageEnterpriseReceipt extends BaseEntity implements Serializable {
  
    private static final long serialVersionUID = 618151878864931826L;
  
    private static final String[] IGNORE_PROPERTIES =
            new String[]{ "encourageEnterprise"};
    /**
     * 申請表 (正本)
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe1;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile1;
    /**
     * 企業/團體：
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe2;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe222;

    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile2;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile211;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile212;
    /**
     * 企業: 營業稅-最初開業M/1/更改申報表M/1副本
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe3;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile3;
    /**
     * 企業: 財政局發出之無欠稅證明書副本
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe4;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile4;
    /**
     * 企業:最近一年之所得補充稅M/1A組或B組收益申報書副本
     *             (如M/1B組收益申報書內之營業結果1至3點金額均為"0"，請提供三份申請單位之營運證明)
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe5;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile5;
    /**
     * 社保近兩季供款證明文件副本 (按展會日計)
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe55;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe552;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe553;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe554;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe555;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe556;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile551;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile552;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile553;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile554;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile555;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile556;
    /**
     * 企業:社會保障基金供款單副本(最近一季) / 無僱員聲明書
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe6;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile6;
    /**
     * 企業:50%股東為澳門居民/全資澳門企業擁有之證明
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe7;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile7;
    /**
     * 企業
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe8;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile8;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile81;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile82;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe88;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile88;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe89;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile89;
    /**
     * 團體: 團體設立之澳門政府公報副本
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe9;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile9;
    /**
     * 團體: 身份證明局發出之登記證明書副本
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe10;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile10;
    /**
     * 團體: 團體代表之身份證副本
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe11;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile11;
    /**
     * 團體:
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe12;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile12;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile121;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile122;
    /**
     * 展位製作費  - 報價單
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe13;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile13;
    /**
     * 展位製作費  - 設計圖
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe14;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile14;
    /**
     * 大會場刊廣告費 - 報價單
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe15;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile15;
    /**
     * 大會場刊廣告- 樣本
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe16;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile16;
    /**
     * 大會網站廣告- 報價單
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe17;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile17;
    /**
     * 大會網站廣告- 樣本
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe18;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile18;
    /**
     * 製作印刷品費用 (宣傳單張、小冊子) - 報價單  【必須為澳門公司報價，需列明物料、尺吋、數量、單價】
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe19;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile19;
    /**
     * 製作印刷品(宣傳單張、小冊子) - 樣本
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe20;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile20;
    /**
     * 製作視聽材料費用- 報價單  (必須為澳門公司報價)
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe21;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile21;
    /**
     * 製作視聽材料 - 樣本
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe22;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile22;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe212;

    /**
     * 機票費用 - 報價單（只適用於境外展會）
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe23;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile23;
    /**
     * 產品運輸費 - 報價單（只適用於境外展會） 【必須為澳門公司報價，需列明體積、重量、運輸方式海運/空運)】
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe24;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile24;
    /**
     * 本地銀行轉帳支付授權書
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe25;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile25;
    /**
     * 授權提交申請文件之聲明書
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe26;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile26;
    /**
     * 關聯交易聲明書(與以上申請項目之服務供應商)
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe27;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile27;
    /**
     * 关联关系申报书
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isOwe28;
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isFile28;

    /**
     * 記錄日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date                   recordTime;
    private String                   memo;
    /**
     * 管理员
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private User recordUser;
    /**
     * 展會实体
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "encourage_enterprise_id", referencedColumnName = "id")
    private EncourageEnterprise encourageEnterprise;

    public EncourageEnterpriseReceipt(EncourageEnterpriseReceiptVO v) {
        BeanUtils.copyProperties(v, this, IGNORE_PROPERTIES);
        if (v.getEncourageEnterpriseId() != null) {
            EncourageEnterprise ee = new EncourageEnterprise();
            ee.setId(v.getEncourageEnterpriseId());
            this.setEncourageEnterprise(ee);
        }
        if (null != v.getRecordUserId()) {
            User applicant = new User();
            applicant.setId(v.getRecordUserId());
            this.setRecordUser(applicant);
        }
    }

    public EncourageEnterpriseReceiptVO toVO() {
        EncourageEnterpriseReceiptVO vo = new EncourageEnterpriseReceiptVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);

        // 展後報告
        EncourageEnterprise encourageEnterprise = this.getEncourageEnterprise();
        if (null != encourageEnterprise) {
            vo.setEncourageEnterpriseVO(encourageEnterprise.toVO());
            vo.setEncourageEnterpriseId(encourageEnterprise.getId());
        }
         //发起人
        User recordUser = this.getRecordUser();
        if (null != recordUser) {
            vo.setRecordUserId(recordUser.getId());
            vo.setRecordUserName(recordUser.getName()+recordUser.getLastName()+"("+recordUser.getAccount()+")");
            vo.setRecordUserAccount(recordUser.getAccount());
        }
        return vo;
    }

}