package com.exhibition.domain.apply;

import com.exhibition.vo.apply.ParticipateSpVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.util.CollectionUtils;

import javax.persistence.CollectionTable;
import javax.persistence.Column;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: S&P
 **/
@Data
@NoArgsConstructor
@Entity
@Table(name = "participate_sp")
public class ParticipateSp extends Participate<ParticipateSp, ParticipateSpVO>
        implements Serializable {

    private static final long serialVersionUID = -2863057685173892213L;


    /**
     * 活動日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date activityTime;
    /**
     * 活動時段 morning：上午 afternoon：下午
     */
    @ElementCollection
    private List<String> activityPeriod;

    /**
     * 活動時間（開始）
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date activityTimeStart;

    /**
     * 活動時間（結束）
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date activityTimeEnd;

    /**
     * 活動相關的主題（1+4的選項）
     */
    private String theme;
    private String themeOther;
    private String place;
    private String placeEn;
    private String placePt;

    /**
     * 擬主辨的人数
     * 1: "50人以下",
     * 2: "50 - 100人",
     * 3: "100 - 150人",
     * 4: "150 - 200人",
     * 5: "200人以上",
     */
    private Integer num;

    /**
     * 活動簡介
     */
    @Column(columnDefinition = "text")
    private String intro;
    /**
     * 活動英文簡介
     */
    @Column(columnDefinition = "text")
    private String introEn;

    /**
     * 申请信函附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_sp_letter")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> letter;

    /**
     * 參展聲明書附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_sp_declaration_files")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> declaration;
    /**
     * 參展產品清單附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_sp_product_list_files")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> productList;

    /**
     * 擬邀主要/重要嘉賓 (如超過一位主要/重要嘉賓，請複製此欄)
     */
    @ElementCollection
    @CollectionTable(name = "participate_sp_guests")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<ParticipateSpGuests> guests;

    /**
     * 擬邀演講嘉賓 (如超過一位演講嘉賓，請複製此欄)
     */
    @ElementCollection
    @CollectionTable(name = "participate_sp_speaker")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<ParticipateSpInvitedSpeaker> invitedSpeaker;

    /**
     * 活動名稱中文
     */
    private String eventName;
    /**
     * 活動名稱英文
     */
    private String eventNameEn;
    /**
     * 活動名稱葡文
     */
    private String eventNamePt;

    /**
     * 是否同意報名須知
     */
    private Boolean isAgreeFirstStep;
    /**
     * 申請豁免場地租金
     */
    private Boolean isApplyExemptionRent;

    /**
     * 最終人數
     */
    private Integer finalNum;
    /**
     * 出席人數
     */
    private Integer attendNum;
    /**
     * 邀請招商投資促進局作為本次活動的支持單位
     */
    private Boolean isInviteOrganizer;
    /**
     * 是否对外显示
     */
    private Boolean isExternal;

    /**
     * 其他要求
     */
    private String otherRequirements;

    /**
     * 邀請出席活動身份（多選），
     * A、邀請本局領導作為主禮嘉賓
     * B 、邀請本局領導作為頒獎嘉賓
     * C、邀請本局領導致辭
     * D、邀請本局領導出席活動
     */
    @ElementCollection
    private List<String> activitytDentity;

    /**
     * 主禮嘉賓
     * 1、主禮擬進行之時段(請加空格須申請者填寫)字符串
     */
    @Column(columnDefinition = "VARCHAR(100)")
    private String proceedingTime;


    /**
     * 主禮嘉賓
     * 2、活動類別:開幕儀式、新品發佈的揭幕儀式、就職典禮、其他(請加空格須申請者填寫)，
     */
    @Column(columnDefinition = "VARCHAR(200)")
    private String activityCategory;
    /**
     * 主禮嘉賓
     * 3、活動介紹及流程(請加空格須申請者填寫)
     */
    @ElementCollection
    @CollectionTable(name = "participate_sp_officiating_guest_files")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> eventIntroductionAndProcess1;

    /**
     * 主禮嘉賓
     * 4、其他主禮嘉賓名單(請加空格須申請者填寫)
     */
    @Column(columnDefinition = "VARCHAR(200)")
    private String otherGuestList;
    /**
     * 頒獎嘉賓
     * 5、頒獎擬進行之時段(請加空格須申請者填寫)
     */
    @Column(columnDefinition = "VARCHAR(100)")
    private String awardTime;
    /**頒獎嘉賓
     * 6、頒獎對象(請加空格須申請者填寫)
     */
    @Column(columnDefinition = "VARCHAR(100)")
    private String awardRecipient;
    /**
     * 頒獎嘉賓
     * 7、頒發獎項(請加空格須申請者填寫)
     */
    @Column(columnDefinition = "VARCHAR(200)")
    private String award;
    /**
     * 頒獎嘉賓
     * 8、活動介紹及流程(請加空格須申請者填寫)
     */
    @ElementCollection
    @CollectionTable(name = "participate_sp_awards_guest_files")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> eventIntroductionAndProcess2;

    /**
     * 頒獎嘉賓
     * 9、其他頒獎嘉賓名單(請加空格須申請者填寫)
     */
    @Column(columnDefinition = "VARCHAR(200)")
    private String otherPresenters;
    /**
     * 致辭
     * 10要求語言:普通話、廣東話、英語、其他(請在"其他"後面加空格須申請者填寫)
     */
    @Column(columnDefinition = "VARCHAR(100)")
    private String language;
    /**
     * 致辭
     * 11、要求時長:1-3分鐘、3-5分鐘、其他(請在"其他"後面加空格須申請者填寫)
     * 字符串
     */
    @Column(columnDefinition = "VARCHAR(100)")
    private String duration;
    /**
     * 致辭
     * 12、要求致辭主題(請加空格須申請者填寫)
     */
    @Column(columnDefinition = "VARCHAR(200)")
    private String speechTheme;
    /**
     * 致辭
     * 13、是否需要做Powerpoint簡報
     */
    private Boolean isPowerpoint;

    /**
     * 致辭
     * 14、活動介紹及流程(請加空格須申請者填寫)
     */
    @ElementCollection
    @CollectionTable(name = "participate_sp_resignation_files")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> eventIntroductionAndProcess3;
    /**
     * 邀請本局領導出席活動
     * 15、如邀請見證簽約請提供簽約內容及資料（請加空格須申請者填寫）
     */
    @Column(columnDefinition = "VARCHAR(200)")
    private String contractContent;
    /**
     * 邀請本局領導出席活動
     * 16、活動合照（前面加一格供申請知者選擇）
     */
    @Column(columnDefinition = "VARCHAR(200)")
    private String activityPhoto;

    /**
     * 邀請本局領導出席活動
     * 17、活動介紹及流程（請加空格須申請者填寫）
     */
    @ElementCollection
    @CollectionTable(name = "participate_sp_leadership_files")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> eventIntroductionAndProcess4;

    /**
     * 簽約內容及資料
     */
    @ElementCollection
    @CollectionTable(name = "participate_sp_details_and_documents_files")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> detailsAndDocumentsFiles;
    /**
     * 其它擬邀出席名單、
     */
    @ElementCollection
    @CollectionTable(name = "participate_sp_other_invitations_are_listed_files")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> otherInvitationsAreListedFiles;

    /**
     *     其它擬邀頒獎嘉賓名稱、
     */
    @ElementCollection
    @CollectionTable(name = "participate_sp_other_award_presenters_files")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> otherAwardPresentersFiles;

    /**
     *     是、否（其它要求）；
     */
    private Boolean isOtherRequirements;

    /**
     *     活動時間
     */
    private String eventPeriod;
    /**
     *     其它活動類別
     */
    private String otherActivityCategory;
    /**
     *     是、否（曾经邀请过）；
     */
    private Boolean isOnceInvited;
    /**
     *     本局是否在2024年澳門國際環保合作發展論壇及展覽中擔任過 貴活動支持單位
     */
    private Boolean invitations;
    /**
     *     出席代表姓名
     */
    private String invitationName;

    /**
     * 擬邀演講嘉賓 (如超過一位演講嘉賓，請複製此欄)
     */
    @ElementCollection
    @CollectionTable(name = "participate_sp_organiser")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<ParticipateSpOrganiser> organiser;
    /**
     * 展位號碼
     */
    @Column(columnDefinition = "VARCHAR(50)")
    private String boothNumber;
    /**
     * 演講題目
     */
    @Column(columnDefinition = "VARCHAR(500)")
    private String speechTopic;
    /**
     * 演講題目
     */
    @Column(columnDefinition = "VARCHAR(500)")
    private String speechTopicEn;
    /**
     * 演講題目
     */
    @Column(columnDefinition = "VARCHAR(50)")
    private String speakerCnFaily;
    /**
     * 演講題目
     */
    @Column(columnDefinition = "VARCHAR(50)")
    private String speakerCnGiven;
    /**
     * 演講題目
     */
    @Column(columnDefinition = "VARCHAR(50)")
    private String speakerEnFaily;
    /**
     * 演講題目
     */
    @Column(columnDefinition = "VARCHAR(50)")
    private String speakerEnGiven;
    @Column(columnDefinition = "VARCHAR(100)")
    private String speakerNation;
    @Column(columnDefinition = "VARCHAR(100)")
    private String speakerTitle;
    @Column(columnDefinition = "VARCHAR(100)")
    private String speakerTitleEn;
    private String speakerCalls;
    /**
     * 演講語言
     */
    @Column(columnDefinition = "VARCHAR(300)")
    private String preCond;
    /**
     * 演講語言其它
     */
    @Column(columnDefinition = "VARCHAR(300)")
    private String preCondOther;

    /**
     * 演講內容
     */
    @Column(columnDefinition = "text")
    private String speechContent;
    /**
     * 申请信函附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_sp_speech_content_files")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> speechContentFiles;
    /**
     * 推薦會
     */
    private Boolean seminar;
    @ElementCollection
    private List<String> seminars;

    @Temporal(TemporalType.TIMESTAMP)
    private Date priority1;
    @Temporal(TemporalType.TIMESTAMP)
    private Date priority2;
    @Temporal(TemporalType.TIMESTAMP)
    private Date priority3;
    /**
     * 觀眾人數
     */
    private String audienceNumber;
    /**
     * 設施及服務
     */
    @ElementCollection
    private List<String> chargeableServices;


    public ParticipateSp(ParticipateSpVO vo) {
        copyProperties(vo, this);
        if (!CollectionUtils.isEmpty(vo.getGuests())) {
            List<ParticipateSpGuests> collect =
                    vo.getGuests().stream().map(ParticipateSpGuests::new).collect(Collectors.toList());
            this.setGuests(collect);
        }
        if (!CollectionUtils.isEmpty(vo.getOrganiser())) {
            List<ParticipateSpOrganiser> collect =
                    vo.getOrganiser().stream().map(ParticipateSpOrganiser::new).collect(Collectors.toList());
            this.setOrganiser(collect);
        }
        if (!CollectionUtils.isEmpty(vo.getInvitedSpeaker())) {
            List<ParticipateSpInvitedSpeaker> collect =
                    vo.getInvitedSpeaker().stream().map(ParticipateSpInvitedSpeaker::new).collect(Collectors.toList());
            this.setInvitedSpeaker(collect);
        }
    }

    @Override
    public ParticipateSpVO toVO() {
        return toVO(false);
    }

    public ParticipateSpVO toVO(boolean includeLazy) {
        ParticipateSpVO vo = new ParticipateSpVO();
        if (!CollectionUtils.isEmpty(organiser)) {
            vo.setOrganiser(organiser.stream().map(ParticipateSpOrganiser::toVO).collect(Collectors.toList()));
        } else {
            vo.setOrganiser(Collections.emptyList());
        }
        if (!CollectionUtils.isEmpty(guests)) {
            vo.setGuests(guests.stream().map(ParticipateSpGuests::toVO).collect(Collectors.toList()));
        } else {
            vo.setGuests(Collections.emptyList());
        }
        if (!CollectionUtils.isEmpty(invitedSpeaker)) {
            vo.setInvitedSpeaker(invitedSpeaker.stream().map(ParticipateSpInvitedSpeaker::toVO).collect(Collectors.toList()));
        } else {
            vo.setInvitedSpeaker(Collections.emptyList());
        }
        copyProperties(this, vo);
        return vo;
    }

}
