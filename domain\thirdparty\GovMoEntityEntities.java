package com.exhibition.domain.thirdparty;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Embeddable;


@Data
@NoArgsConstructor
@Embeddable
public class GovMoEntityEntities {
    private String code;
    private String nameZhHant;
    private String namePt;
    /**
     * 電話
     */
    private String contactPhone;
    /**
     * 郵箱
     */
    private String email;
    /**
     * 地址
     */
    private String address;
    /**
     * 通讯地址
     */
    private String contactAddress;
    /**
     * 商業登記編號
     */
    private String commercialCode;
    /**
     * 財政局營業稅納稅人編號
     */
    private String dsfTaxpayerCode;
    private String g2eEntityId;
    private String abbr;


}
