package com.exhibition.domain.activity;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.sys.Institution;
import com.exhibition.vo.activity.ApproveImageVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "approve_image")
public class ApproveImage extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -5707527166234110975L;

    private static final String[] IGNORE_PROPERTIES =
            new String[]{"activity", "institution"};

    /**
     * 展會
     */
    @ManyToOne (cascade=CascadeType.ALL)
    @JoinColumn(name="activity_id")
    private Activity                    activity;
    /**
     * 機構
     */
    @ManyToOne (cascade=CascadeType.ALL)
    @JoinColumn(name="institution_id")
    private Institution                 institution;
    /**
     * 圖片名稱
     */
    private String                      name;
    /**
     * url
     */
    private String                      url;

    public ApproveImage(ApproveImageVO vo) {
        BeanUtils.copyProperties(vo, this, IGNORE_PROPERTIES);
    }

    public ApproveImageVO toVO() {
        ApproveImageVO vo = new ApproveImageVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);
        return vo;
    }
}
