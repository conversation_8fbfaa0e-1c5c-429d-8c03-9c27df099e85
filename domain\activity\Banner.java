package com.exhibition.domain.activity;

import com.exhibition.base.entity.BaseEntity;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.activity
 * @ClassName: Banner
 * @description: banner圖
 * @author: ShiXin
 * @create: 2020-03-03 09:36
 **/
@Data
@NoArgsConstructor
@Entity
@Table(name = "banner")
public class Banner extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 2636684716998994906L;

    /**
     * Banner 序號
     */
    @Column(unique = true,nullable = false)
    private Long serialNumber;

    /**
     * Banner標題
     */
    private String title;

    /**
     * 跳轉鏈接
     */
    private String url;

    /**
     * 配圖
     */
    private String pic;

    /**
     * 關聯的活動的ID
     */
    private Long activityId;
}
