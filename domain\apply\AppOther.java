package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.activity.Activity;
import com.exhibition.domain.sys.*;
import com.exhibition.util.CopyUtils;
import com.exhibition.vo.apply.EncourageLetterVO;
import com.exhibition.vo.apply.EncourageVO;
import com.exhibition.vo.apply.AppOtherVo;
import com.exhibition.vo.sys.InstitutionSimpleVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 支持及鼓勵措施
 * @date 2020-02-25
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "appother")
@Inheritance(strategy = InheritanceType.JOINED)
public class AppOther<T extends AppOther, V extends AppOtherVo> extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 7579368546235549151L;

    private static final String[] IGNORE_PROPERTIES =
            new String[]{"activity", "institution", "liaison", "applicant", "tasks", "liaison"};

    /**
     * 设置视图
     */
    public interface AppOtherSimpleView {
    }

    ;

    public interface GlobalSimpleView extends AppOtherSimpleView {
    }

    ;

    /**
     * 收遞編號
     */
    private String putNo;

    @ElementCollection
    @CollectionTable(name = "appother_accessory")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> accessoryFiles;

    private Boolean savingCost;

    //tag
    private String tag;
    /**
     * 申請編號
     */
    private String code;
    /**
     * 支持及鼓勵措施的類型
     * <p>
     * ATTEND("非牟利社團赴境外展會設置展位"),EN_MISSION("非牟利社團組織代表團參與境外展會"),ENTERPRISE("企業參與本地或境外展會"),CONVENTION("會議及展覽資質計劃"),TOUR("展會及商務旅游展支持計劃"),TRAIN("展會專業人才培訓支援計劃");
     */
    @Enumerated(EnumType.STRING)
    private EncourageType type;
    /**
     * 展會
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private Activity activity;
    /**
     * 機構
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private Institution institution;
    /**
     * 組團機構
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private Institution institutionCluster;
    /**
     * 機構聯絡人
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private Liaison liaison;
    /**
     * 直接与本人联系
     */
    private Boolean contactMe;
    /**
     * 发起人
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private User applicant;
    /**
     * 申请日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date applyTime;

    /**
     * 收遞日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date putTime;

    /**
     * 收遞人
     */
    private String putName;


    /**
     * 申请金额
     */
    private Double applyAmount;
    /**
     * 審核狀態
     */
    @Enumerated(EnumType.STRING)
    private Status status;
    /**
     * 審批操作記錄
     */
    @OneToMany(mappedBy = "appother", cascade = {CascadeType.ALL}, fetch = FetchType.LAZY)
    private List<AdmTaskAppOther> tasks;


    /**
     * 總的獲批金额
     */
    private Double approvedAmount;
    /**
     * 總的實際金额
     */
    private Double actualAmount;

    /*審批*/
    /**
     * 建議書編號
     */
    private String proposalnumber;
    /*結算*/
    /**
     * 內部通訊號碼
     */
    private String communicationnumber;
    /**
     * 內部通訊發出日期
     */
    private Date communicationdate;
    /**
     * 結算情況
     */
    @Enumerated(EnumType.STRING)
    private Settlement settlement;
    /**
     * 備註
     */
    private String comments;


    /**
     * 当前审批人id
     */
    private Long currentApproverId;
    /**
     * 图片申请id
     */
    private Long applyPictureId;
    /**
     * 图片申请状态
     */
    @Enumerated(EnumType.STRING)
    private Status applyPictureStatus;
    /**
     * 展会问卷结果id
     */
    private Long questionnaireAnswerId;

    /**
     * 來源單號
     */
    private String srcdoc;
    /**
     * 來源类型
     */
    private String srctype;
    /**
     * 是否存在收據
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isReceipt;
    /**
     * 是否存在補交資料
     */
    private Boolean isSupplementary;
    /**
     * 信函发出日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date sendEmailDate;
    /**
     * 信函接受日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date receiveEmailDate;


    public AppOther(Long id, String code, EncourageType type, Activity activity, Institution institution,
                    Liaison liaison, User applicant, Date applyTime, Double applyAmount, Status status, Long currentApproverId,
                    Long applyPictureId, Status applyPictureStatus, Long questionnaireAnswerId,
                    Date putTime, String putName, Boolean isReceipt, Boolean isSupplementary, String srcdoc, String srctype,
                    Date sendEmailDate, Date receiveEmailDate) {
        this.setId(id);
        this.code = code;
        this.type = type;
        this.sendEmailDate = sendEmailDate;
        this.receiveEmailDate = receiveEmailDate;
        this.activity = activity;
        this.institution = institution;
        this.liaison = liaison;
        this.applicant = applicant;
        this.applyTime = applyTime;
        this.applyAmount = applyAmount;
        this.status = status;
        this.currentApproverId = currentApproverId;
        this.applyPictureId = applyPictureId;
        this.applyPictureStatus = applyPictureStatus;
        this.questionnaireAnswerId = questionnaireAnswerId;
        this.putTime = putTime;
        this.putName = putName;
        this.isReceipt=isReceipt;
        this.isSupplementary=isSupplementary;
        this.srcdoc=srcdoc;
        this.srctype=srctype;
    }

    public AppOther(AppOtherVo v) {
        BeanUtils.copyProperties(v, this, IGNORE_PROPERTIES);
        if (null != v.getActivityId()) {
            Activity activity = new Activity();
            activity.setId(v.getActivityId());
            this.setActivity(activity);
        }

        if (null != v.getInstitutionId()) {
            Institution institution = new Institution();
            institution.setId(v.getInstitutionId());
            this.setInstitution(institution);
        }
        if (null != v.getInstitutionClusterId()) {
            Institution institution = new Institution();
            institution.setId(v.getInstitutionClusterId());
            this.setInstitutionCluster(institution);
        }
        if (null != v.getApplicantId()) {
            User applicant = new User();
            applicant.setId(v.getApplicantId());
            this.setApplicant(applicant);
        }

        if (null != v.getLiaisonId()) {
            Liaison liaison = new Liaison();
            liaison.setId(v.getLiaisonId());
            this.setLiaison(liaison);
        }
    }

    public InstitutionSimpleVO toInVO() {
        InstitutionSimpleVO i = new InstitutionSimpleVO();
        // 机构
        Institution institution = this.getInstitution();
        if (null != institution) {
            i = institution.toSimpleVO();
        }
        return i;
    }

    public AppOtherVo toVO() {
        AppOtherVo v = new AppOtherVo();
        BeanUtils.copyProperties(this, v, IGNORE_PROPERTIES);
        // 展会
        Activity activity = this.getActivity();
        if (null != activity) {
            v.setActivityId(activity.getId());
            v.setActivityName(activity.getNameZh());
            v.setActivityScope(activity.getActivityScope());
            v.setActivity(activity.toSimpleVO());
        }
        // 机构
        Institution institution = this.getInstitution();
        if (null != institution) {
            v.setInstitutionId(institution.getId());
            v.setInstitutionName(institution.getNameZh());
            v.setInstitution(institution.toVO());
        }
        // 组团机构
        Institution institutionCluster = this.getInstitutionCluster();
        if (null != institutionCluster) {
            v.setInstitutionClusterId(institutionCluster.getId());
            v.setInstitutionCluster(institutionCluster.toSimpleVO());
        }
        //審批流
        List<AdmTaskAppOther> tasks = this.getTasks();
        if (null != tasks) {
            v.setTasks(tasks.stream().map(AdmTask::toVO).collect(Collectors.toList()));
            // 当前审批节点
            v.setCurrentTask(v.getTasks().stream()
                    .filter(t -> t.getStatus() == Status.approving)
                    .findFirst().orElse(null));
        }

        // 发起人
        User applicant = this.getApplicant();
        if (null != applicant) {
            v.setApplicantId(applicant.getId());
            v.setApplicantName(applicant.getName()+applicant.getLastName()+"("+applicant.getAccount()+")");
            v.setApplicantAccount(applicant.getAccount());
            v.setApplicantPhone(applicant.getPhone());
        }
        // 联络人
        Liaison liaison = this.getLiaison();
        if (null != liaison) {
            v.setLiaisonId(liaison.getId());
            v.setLiaison(liaison.toVO());
        }
        return v;
    }

    protected void copyProperties(T t, V v, String... ignoreProperties) {
        if (ignoreProperties.length == 0) {
            BeanUtils.copyProperties(t, v, IGNORE_PROPERTIES);
        } else {
            String[] ignores = CopyUtils.concat(IGNORE_PROPERTIES, ignoreProperties);
            BeanUtils.copyProperties(t, v, ignores);
        }
        // 展会
        Activity activity = this.getActivity();
        if (null != activity) {
            v.setActivityId(activity.getId());
            v.setActivityName(activity.getNameZh());
            v.setActivityScope(activity.getActivityScope());
            v.setActivity(activity.toSimpleVO());
        }
        // 机构
        Institution institution = this.getInstitution();
        if (null != institution) {
            v.setInstitutionId(institution.getId());
            v.setInstitutionName(institution.getNameZh());
            v.setInstitution(institution.toVO());
        }
        // 组团机构
        Institution institutionCluster = this.getInstitutionCluster();
        if (null != institutionCluster) {
            v.setInstitutionClusterId(institutionCluster.getId());
            v.setInstitutionCluster(institutionCluster.toSimpleVO());
        }
        //審批流
        List<AdmTaskAppOther> tasks = this.getTasks();
        if (null != tasks) {
            v.setTasks(tasks.stream().map(AdmTask::toSimpleVO).collect(Collectors.toList()));
            // 当前审批节点
            v.setCurrentTask(v.getTasks().stream()
                    .filter(e -> e.getStatus() == Status.approving)
                    .findFirst().orElse(null));
        }

        // 发起人
        User applicant = this.getApplicant();
        if (null != applicant) {
            v.setApplicantId(applicant.getId());
            v.setApplicantName(applicant.getName()+applicant.getLastName()+"("+applicant.getAccount()+")");
            v.setApplicantAccount(applicant.getAccount());
            v.setApplicantPhone(applicant.getPhone());
        }
        // 联络人
        Liaison liaison = this.getLiaison();
        if (null != liaison) {
            v.setLiaisonId(liaison.getId());
            v.setLiaisonName(liaison.getNameZh());
            v.setLiaison(liaison.toVO());
        }

    }

    protected void copyProperties(V v, T t, String... ignoreProperties) {
        if (ignoreProperties.length == 0) {
            BeanUtils.copyProperties(v, t, IGNORE_PROPERTIES);
        } else {
            String[] ignores = CopyUtils.concat(IGNORE_PROPERTIES, ignoreProperties);
            BeanUtils.copyProperties(v, t, ignores);
        }
        if (null != v.getActivityId()) {
            Activity activity = new Activity();
            activity.setId(v.getActivityId());
            this.setActivity(activity);
        }

        if (null != v.getInstitutionId()) {
            Institution institution = new Institution();
            institution.setId(v.getInstitutionId());
            this.setInstitution(institution);
        }
        if (null != v.getInstitutionClusterId()) {
            Institution institution = new Institution();
            institution.setId(v.getInstitutionClusterId());
            this.setInstitutionCluster(institution);
        }
        if (null != v.getApplicantId()) {
            User applicant = new User();
            applicant.setId(v.getApplicantId());
            this.setApplicant(applicant);
        }

        if (null != v.getLiaisonId()) {
            Liaison liaison = new Liaison();
            liaison.setId(v.getLiaisonId());
            this.setLiaison(liaison);
        }
    }
}
