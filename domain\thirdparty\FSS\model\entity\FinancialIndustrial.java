package com.exhibition.domain.thirdparty.FSS.model.entity;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.thirdparty.FSS.model.vo.FssVo;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Embeddable;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/5/11 10:09
 * @describe
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "financial_industrial")
public class FinancialIndustrial extends BaseEntity<FSS, FssVo> implements Serializable {

    private static final long serialVersionUID = 6425572691567053584L;

    private String isActive;

    private Integer dataIcc;

    private Integer dataFcc;
}
