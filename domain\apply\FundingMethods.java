package com.exhibition.domain.apply;

import java.util.Arrays;

/**
 * Funding Methods
 */
public enum FundingMethods {
    /**
     * 資助計劃
     */
    FUNDING_PLAN("資助計劃"),
    /**
     * 特別資助
     */
    ESPECIALLY("特別資助");

    private final String name;

    FundingMethods(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static FundingMethods getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
