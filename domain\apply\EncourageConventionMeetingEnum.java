package com.exhibition.domain.apply;

import java.util.Arrays;

public enum EncourageConventionMeetingEnum {
    /**
     * 已確定的會議
     */
    MEETING("已確定的會議"),
    /**
     * 擬籌辦具潛力的會議
     */
    POTENTIAL_MEETING("擬籌辦具潛力的會議"),
    /**
     * 已確定的展覽
     */
    EXHIBITIONS("已確定的展覽"),
    /**
     * 擬籌辦具潛力的展覽
     */
    POTENTIAL_EXHIBITIONS("擬籌辦具潛力的展覽");


    private final String name;

    EncourageConventionMeetingEnum(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static EncourageConventionMeetingEnum getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }

}
