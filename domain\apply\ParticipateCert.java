package com.exhibition.domain.apply;

import com.exhibition.vo.apply.ParticipateCertVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 2020-03-03
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Embeddable
public class ParticipateCert implements Serializable {
    private static final long serialVersionUID = 7209939721804282684L;
    /**
     * uid
     */
    private String          uid;

    /**
     *文件名
     */
    private String          oriname;
    /**
     * url
     */
    private String          url;

    public ParticipateCert(ParticipateCertVO vo) {
        BeanUtils.copyProperties(vo, this);
    }

    public ParticipateCertVO toVO() {
        return toVO(false);
    }

    public ParticipateCertVO toVO(boolean includeLazy) {
        ParticipateCertVO vo = new ParticipateCertVO();
        BeanUtils.copyProperties(this, vo);
        return vo;
    }
}
