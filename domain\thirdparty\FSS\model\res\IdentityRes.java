package com.exhibition.domain.thirdparty.FSS.model.res;

import com.exhibition.domain.thirdparty.FSS.model.entity.FinancialComplementarDate;
import com.exhibition.domain.thirdparty.FSS.model.entity.FinancialIndustrial;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2024/12/9 17:44
 * @describe 财政局接口
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class IdentityRes {
    /*    success	請求標誌	Boolean
        message	相關原因	String
        data		String[]
        residentMacau	「自然人」是否為澳門居民	Boolean
        structure	「社團」組織架構(按身份證明局格式返回）	string

    */
    private Boolean success;
    private String message;
    private Data data;

    @AllArgsConstructor
    @NoArgsConstructor
    @ToString
    @Getter
    @Setter
    public static class Data {
        private Boolean residentMacau;
        private String structure;
    }
}
