package com.exhibition.domain.apply;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.activity.SetAttributeConverter;
import com.exhibition.vo.apply.EncourageConventionGradeVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.springframework.beans.BeanUtils.copyProperties;


@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_convention_grade")
@BatchSize(size = 20)
public class EncourageConventionGrade extends BaseEntity<EncourageConventionGrade,EncourageConventionGradeVO> implements Serializable {

    private static final long serialVersionUID = -6952482751920495341L;
    private static final String[] IGNORE_PROPERTIES = new String[]{"encourageConvention","applyItems", "tasks"};

    /**
     * 活動性質
     */
    @Column
    private String activityProperties;
    /**
     * 活動性質得分
     */
    @Column
    private Integer activityPropertiesScore;

    /**
     * 活動性質級別
     */
    @Column
    private Character activityPropertiesLevel;
    /**
     * 活動規模
     */
    @Column
    private String activityScale;
    /**
     * 活動規模得分
     */
    @Column
    private Integer activityScaleScore;
    /**
     * 活動規模級別
     */
    @Column
    private Character activityScaleLevel;
    /**
     * 豐富活動體驗及促進社區經濟級別
     */
    @Column
    private Character economicDevelopmentLevel;
    /**
     * 豐富活動體驗及促進社區經濟得分
     */
    @Column
    private Integer economicDevelopmentScore;
    /**
     * 豐富活動體驗及促進社區經濟
     */
    @Convert(converter = SetAttributeConverter.class)
    private Set<String> economicDevelopment;

    /**
     * 活動國際化程度
     */
    @Column
    private String activityI18N;
    /**
     * 活動國際化程度級別
     */
    @Column
    private Character activityI18NLevel;
    /**
     * 活動國際化程度得分
     */
    @Column
    private Integer activityI18NScore;
    /**
     * 專業化程度
     */
    @Column
    private String activitySpeciality;
    /**
     * 專業化程度级别
     */
    @Column
    private Character activitySpecialityLevel;
    /**
     * 專業化程度得分
     */
    @Column
    private Integer activitySpecialityScore;
    /**
     *市場化程度
     */
    @Column
    private String activityMarket;
    /**
     * 市場化程度级别
     */
    @Column
    private Character activityMarketLevel;
    /**
     * 市場化程度得分
     */
    @Column
    private Integer activityMarketScore;
    /**
     * 數字化程度
     */
    @Convert(converter = SetAttributeConverter.class)
    private Set<String> digitizing;
    /**
     * 數字化程度级别
     */
    @Column
    private Character digitizingLevel;
    /**
     * 數字化程度的得分
     */
    @Column
    private Integer digitizingScore;
    /**
     * 數字化程度備注1
     */
    @Column
    private String digitizingRemark1;
    /**
     * 數字化程度備注2
     */
    @Column
    private String digitizingRemark2;
    /**
     * 數字化程度備注3
     */
    @Column
    private String digitizingRemark3;
    /**
     * 綠色化程度
     */
    @Convert(converter = SetAttributeConverter.class)
    private Set<String> green;
    /**
     * 綠色化程度级别
     */
    @Column
    private Character greenLevel;
    /**
     * 綠色化程度的得分
     */
    @Column
    private Integer greenScore;
    /**
     * 其他1
     */
    @Column
    private String greenRemark1;
    /**
     * 其他2
     */
    @Column
    private String greenRemark2;
    /**
     * 其他3
     */
    @Column
    private String greenRemark3;
    /**
     * 活動組織架構的能力
     */
    @Column
    private String activityArchitecture;
    /**
     * 活動組織架構的能力級別
     */
    @Column
    private Character activityArchitectureLevel;
    /**
     * 活動組織架構的能力得分
     */
    @Column
    private Integer activityArchitectureScore;
    /**
     * 執行單位經驗
     */
    @Column
    private String activityUnitExperience;
    /**
     * 執行單位經驗級別
     */
    @Column
    private Character activityUnitExperienceLevel;
    /**
     * 執行單位經驗得分
     */
    @Column
    private Integer activityUnitExperienceScore;
    /**
     * 預算合理性
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_grade_budget")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<EncourageConventionGradeBudget> gradeBudget;
    /**
     * 預算合理性分值
     */
    @Column
    private Double budgetScore;
    /**
     * 原因1
     */
    @Convert(converter = SetAttributeConverter.class)
    private Set<String> budgetCause;
    @Column
    private Integer exhibitionsScore;
    @Column
    private Integer developgradeScore;
    @Column
    private Integer gradeSumScore;
    @Column
    private Double percent;
    /**
     * 一會兩地
     */
    @Column
    private String meeting;
    /**
     * 展會申請实体
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "encourage_convention_id", referencedColumnName = "id")
    private EncourageConvention encourageConvention;

    /**
     * 資助比例
     */
    @Column
    private Double FundingRatio;

    //展覽部分
    /**
     * 活動性質
     */
    @Column
    private String eactivityProperties;
    /**
     * 活動性質得分
     */
    @Column
    private Integer eactivityPropertiesScore;

    /**
     * 活動性質級別
     */
    @Column
    private Character eactivityPropertiesLevel;
    /**
     * 活動規模
     */
    @Column
    private String eactivityScale;
    /**
     * 活動規模得分
     */
    @Column
    private Integer eactivityScaleScore;
    /**
     * 活動規模級別
     */
    @Column
    private Character eactivityScaleLevel;
    /**
     * 豐富活動體驗及促進社區經濟級別
     */
    @Column
    private Character eeconomicDevelopmentLevel;
    /**
     * 豐富活動體驗及促進社區經濟得分
     */
    @Column
    private Integer eeconomicDevelopmentScore;
    /**
     * 豐富活動體驗及促進社區經濟
     */
    @Convert(converter = SetAttributeConverter.class)
    private Set<String> eeconomicDevelopment;

    /**
     * 活動國際化程度
     */
    @Column
    private String eactivityI18N;
    /**
     * 活動國際化程度級別
     */
    @Column
    private Character eactivityI18NLevel;
    /**
     * 活動國際化程度得分
     */
    @Column
    private Integer eactivityI18NScore;
    /**
     * 專業化程度
     */
    @Column
    private String eactivitySpeciality;
    /**
     * 專業化程度级别
     */
    @Column
    private Character eactivitySpecialityLevel;
    /**
     * 專業化程度得分
     */
    @Column
    private Integer eactivitySpecialityScore;
    /**
     *市場化程度
     */
    @Column
    private String eactivityMarket;
    /**
     * 市場化程度级别
     */
    @Column
    private Character eactivityMarketLevel;
    /**
     * 市場化程度得分
     */
    @Column
    private Integer eactivityMarketScore;
    /**
     * 數字化程度
     */
    @Convert(converter = SetAttributeConverter.class)
    private Set<String> edigitizing;
    /**
     * 數字化程度级别
     */
    @Column
    private Character edigitizingLevel;
    /**
     * 數字化程度的得分
     */
    @Column
    private Integer edigitizingScore;
    /**
     * 數字化程度備注1
     */
    @Column
    private String edigitizingRemark1;
    /**
     * 數字化程度備注2
     */
    @Column
    private String edigitizingRemark2;
    /**
     * 數字化程度備注3
     */
    @Column
    private String edigitizingRemark3;
    /**
     * 綠色化程度
     */
    @Convert(converter = SetAttributeConverter.class)
    private Set<String> egreen;
    /**
     * 綠色化程度级别
     */
    @Column
    private Character egreenLevel;
    /**
     * 綠色化程度的得分
     */
    @Column
    private Integer egreenScore;
    /**
     * 其他1
     */
    @Column
    private String egreenRemark1;
    /**
     * 其他2
     */
    @Column
    private String egreenRemark2;
    /**
     * 其他3
     */
    @Column
    private String egreenRemark3;
    /**
     * 活動組織架構的能力
     */
    @Column
    private String eactivityArchitecture;
    /**
     * 活動組織架構的能力級別
     */
    @Column
    private Character eactivityArchitectureLevel;
    /**
     * 活動組織架構的能力得分
     */
    @Column
    private Integer eactivityArchitectureScore;
    /**
     * 執行單位經驗
     */
    @Column
    private String eactivityUnitExperience;
    /**
     * 執行單位經驗級別
     */
    @Column
    private Character eactivityUnitExperienceLevel;
    /**
     * 執行單位經驗得分
     */
    @Column
    private Integer eactivityUnitExperienceScore;
    /**
     * 原因1
     */
    @Convert(converter = SetAttributeConverter.class)
    private Set<String> ebudgetCause;
    @Column
    private Integer eexhibitionsScore;
    @Column
    private Integer edevelopgradeScore;
    @Column
    private Integer egradeSumScore;
    @Column
    private Double epercent;
    /**
     * 一會兩地
     */
    @Column
    private String emeeting;
    /**
     * 資助比例
     */
    @Column
    private Double eFundingRatio;
    /**
     * 預算合理性
     */
    @ElementCollection
    @CollectionTable(name = "encourage_convention_grade_ebudget")
    @Fetch(FetchMode.SELECT)
    @JsonIgnore
    private List<EncourageConventionGradeBudget> egradeBudget;
    /**
     * 預算合理性分值
     */
    @Column
    private Double ebudgetScore;


    public EncourageConventionGrade(EncourageConventionGradeVO vo){
        copyProperties(vo,this,IGNORE_PROPERTIES);
        if (!CollectionUtils.isEmpty(vo.getGradeBudget())) {
            this.setGradeBudget(vo.getGradeBudget());
        }
        if (!CollectionUtils.isEmpty(vo.getEgradeBudget())) {
            this.setEgradeBudget(vo.getEgradeBudget());
        }
      /*  if (!CollectionUtils.isEmpty(vo.getEgradeBudget())) {
            this.setEgradeBudget(vo.getEgradeBudget());
        }*/
        if (vo.getEncourageConventionId()!=null){
            EncourageConvention ec = new EncourageConvention();
            ec.setId(vo.getEncourageConventionId());
            this.setEncourageConvention(ec);
        }
    }
    public EncourageConventionGradeVO toVo(boolean includeLazy){
        EncourageConventionGradeVO v = new EncourageConventionGradeVO();
        copyProperties(this,v,IGNORE_PROPERTIES);
        if (includeLazy){
            EncourageConvention encourageConvention = this.getEncourageConvention();
            if (null != encourageConvention){
                v.setEncourageConventionVO(encourageConvention.toVO());
                v.setEncourageConventionId(encourageConvention.getId());
            }
        }
        return v;
    }

    public EncourageConventionGradeVO toVo(){
        return toVo(false);
    }
}
