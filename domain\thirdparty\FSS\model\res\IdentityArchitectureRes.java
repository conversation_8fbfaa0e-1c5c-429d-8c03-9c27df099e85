package com.exhibition.domain.thirdparty.FSS.model.res;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/***
 * <AUTHOR>
 * @date 2024/12/18 13:38
 * @describe 身份证明局接口
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class IdentityArchitectureRes implements Serializable {

    @JsonProperty("regnum")
    private String regnum;
    @JsonProperty("cname")
    private String cname;
    @JsonProperty("pname")
    private String pname;
    @JsonProperty("ename")
    private String ename;
    @JsonProperty("piname")
    private String piname;
    @JsonProperty("first_bo_page")
    private String first_bo_page; // FIXME check this code
    @JsonProperty("first_bo_issue")
    private String first_bo_issue; // FIXME check this code
    @JsonProperty("first_bo_date")
    private String first_bo_date; // FIXME check this code
    @JsonProperty("last_minute_date")
    private String last_minute_date;
    @JsonProperty("last_valid_date")
    private String last_valid_date;
    @JsonProperty("expired")
    private Boolean expired;
    @JsonProperty("success")
    private Boolean success;
    @JsonProperty("error_data")
    private ErrorDataDTO error_data;
    @JsonProperty("version")
    private String version;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString
    public static class ErrorDataDTO implements Serializable {
        @JsonProperty("msg")
        private String msg;
    }
}
