package com.exhibition.domain.apply;

import com.exhibition.vo.apply.MeetingDateVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Embeddable;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: MeetingDate
 * @description: 全天會議
 * @author: ShiXin
 * @create: 2020-03-13 17:30
 **/
@Data
@NoArgsConstructor
@Embeddable
public class MeetingDate implements Serializable {

    private static final long serialVersionUID = -6294823073702812942L;

    /**
     * 預計展會開始時間
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date   date;
    /**
     * 小時
     */
    private Double hours;

    public MeetingDate(MeetingDateVO vo) {
        BeanUtils.copyProperties(vo,this);
    }

    public MeetingDateVO toVO() {
        MeetingDateVO v = new MeetingDateVO();
        BeanUtils.copyProperties(this,v);
        return v;
    }
}
