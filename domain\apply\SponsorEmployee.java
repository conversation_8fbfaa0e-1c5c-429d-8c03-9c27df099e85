package com.exhibition.domain.apply;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Embeddable;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @desc 獲保薦僱員
 * @date 2020-06-09
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Embeddable
public class SponsorEmployee implements Serializable {
    
    private static final long serialVersionUID = 2042787695929550422L;
    
    /**
     * 僱員姓名(中文)
     */
    private String                nameZh;
    /**
     * 僱員姓名(英文)
     */
    private String                nameEn;
    /**
     * 出生日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date                  birthDate;
    /**
     * 澳門居民身份證號碼
     */
    private String                idCardNumber;
    /**
     * 僱主名稱(僱主申請者不適用)
     */
    private String                employerName;
    /**
     * 僱員最高學歷
     */
    private String                highestEducation;
}