package com.exhibition.domain.sys;

import java.util.Arrays;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.sys
 * @ClassName: Gender
 * @description: 性别
 * @author: ShiXin
 * @create: 2020-02-21 17:27
 **/
public enum  Gender {

    /**
     * 男
     */
    M("男"),
    /**
     * 女
     */
    F("女"),
    /**
     * 女
     */
    B("博士"),
    X("小姐"),
    /**
     * 其他
     */
    O("其他");

    private final String name;

    Gender( String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static Gender getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
