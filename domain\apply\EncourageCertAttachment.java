package com.exhibition.domain.apply;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Embeddable;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 2020-06-068
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Embeddable
public class EncourageCertAttachment extends EncourageCert implements Serializable {
  
    private static final long serialVersionUID = 1L;

    /**
     * 附件類型
     */
    private String            type;

    /**
     * uid
     */
    private String          uid;

    /**
     *文件名
     */
    private String          oriname;
    /**
     * url
     */
    private String          url;
  
}