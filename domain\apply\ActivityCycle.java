package com.exhibition.domain.apply;

import java.util.Arrays;

public enum ActivityCycle {
    /**
     * 每兩年一度
     */
    BIENNIAL("每兩年一度"),
    /**
     * 每年一度
     */
    ANNUAL("每年一度"),
    /**
     * 每季一度
     */
    QUARTERLY("每季一度"),
    /**
     * 其他
     */
    OTHER("其他");

    private final String name;

    ActivityCycle(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ActivityCycle getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
