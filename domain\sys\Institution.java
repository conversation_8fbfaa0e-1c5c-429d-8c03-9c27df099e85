package com.exhibition.domain.sys;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.apply.AdmTask;
import com.exhibition.domain.apply.AdmTaskInstitution;
import com.exhibition.domain.apply.Encourage;
import com.exhibition.domain.apply.EncourageCert;
import com.exhibition.domain.apply.ParticipateCert;
import com.exhibition.domain.record.BlackRank;
import com.exhibition.edm.vo.EdmInstitutionSimpleVO;
import com.exhibition.vo.apply.EncourageCertVO;
import com.exhibition.vo.apply.ParticipateCertVO;
import com.exhibition.vo.sys.InstitutionShareholderVO;
import com.exhibition.vo.sys.InstitutionSimpleVO;
import com.exhibition.vo.sys.InstitutionVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.CascadeType;
import javax.persistence.CollectionTable;
import javax.persistence.Column;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.sys
 * @ClassName: Institution
 * @description: 機構
 * @author: ShiXin
 * @create: 2020-02-20 16:19
 **/
@Data
@NoArgsConstructor
@Entity
@Table(name = "institution")
@JsonView(Encourage.EncourageSimpleView.class)
// @JsonView(Encourage.GlobalSimpleView.class)
public class Institution extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 2073650852198293856L;

    private static final String[] IGNORE_PROPERTIES =
            new String[]{"industries", "liaisons", "attachmentFiles", "businessRegistrationFiles",
                    "salesTaxOpenFiles", "salesTaxFiles", "shareholderSamesFiles",
                    "groupEstablishmentFiles", "identificationBureauFiles", "legalPersonFiles", "tasks", "firstLiaison"};

    /**
     * 机构编码（唯一）
     */
    /*@Column(nullable = false, unique = true, length = 100)
    private String institutionCode;*/
    /**
     * 机构名称(中文)
     */
    private String nameZh;
    /**
     * 机构名称(英文)
     */
    private String nameEn;
    /**
     * 机构名称(葡文)
     */
    private String namePt;
    /**
     * 企业logo
     */
    private String logo;
    /**
     * 成立日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateOfEstablishment;
    /**
     * 机构性质
     */
    @Enumerated(value = EnumType.STRING)
    private InstitutionalNature nature;
    /**
     * 国家/地区（中文）
     */
    private String countryZh;
    /**
     * 省份（中文）
     */
    private String provinceZh;
    /**
     * 城市（中文）
     */
    private String cityZh;
    /**
     * 街道（中文）
     */
    private String streetZh;
    /**
     * 国家/地区（英/葡）
     */
    private String countryEnOrPt;
    /**
     * 省份（英/葡）
     */
    private String provinceEnOrPt;
    /**
     * 城市（英/葡）
     */
    private String cityEnOrPt;
    /**
     * 街道（英/葡）
     */
    private String streetEnOrPt;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 區號
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private AreaCode areaCode;

    /**
     * 電話區號
     */
    private String telAreaCode;

    /**
     * 傳真區號
     */
    private String faxAreaCode;

    /**
     * 聯絡電話
     */
    private String tel;
    /**
     * 傳真
     */
    private String fax;

    /**
     * 股东成分
     */
    @ElementCollection
    @CollectionTable(name = "institution_shareholder")
    @Fetch(FetchMode.SELECT)
    private List<InstitutionShareholder> institutionShareholders;
    /**
     * M1 Number
     */
    private String m1Number;
    /**
     * M1商业及动产登记局登记编号
     */
    private String registrationNumber;
    /**
     * M8場地登記編號（營業稅編號）
     */
    private String siteRegistrationCode;
    /**
     * 纳税人名称
     */
    private String taxpayerName;
    /**
     * 纳税人编号
     */
    private String taxpayerNo;
    /**
     * 業務
     */
    private String business;
    /**
     * 註冊資本
     */
    private BigDecimal registeredCapital;
    /**
     * 幣種
     */
    @Enumerated(value = EnumType.STRING)
    private Currency currency;
    /**
     * 行业
     */
    @OneToMany(mappedBy = "institution", fetch = FetchType.LAZY, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @BatchSize(size = 20)
    private List<InstitutionIndustry> industries;

    /**
     * 行業編號（M/8 營業稅-徵稅憑單上顯示）
     */
    @ElementCollection
    private List<String> industryNumber;

    /**
     * 行業分类（資料庫需要字符串）
     */
    @ElementCollection
    private List<String> industryNumberList;

    /**
     * 行業編號（資料庫需要字符串）
     */
    private String industryNumbers;

    /**
     * 每個機構一年只有四次申請機會
     */
    @Column(insertable = false, columnDefinition = "int default 4")
    private Integer applyNumber;

    /**
     * 机构联络人
     */
    @OneToMany(mappedBy = "institution")
    @Fetch(FetchMode.SELECT)
    private List<Liaison> liaisons;
    /**
     * 身份证明局设立之登记号码
     */
    private String idnumber;
    /**
     * 负责人姓名
     */
    private String chargeName;
    /**
     * 审批状态
     */
    @Enumerated(EnumType.STRING)
    private Status status;
    /**
     * 股東成分
     */
    @Column(columnDefinition = "text")
    private String shareholderComponents;
    /**
     * 備註
     */
    private String memo;
    /**
     * 是否同意讀取澳門特別行政區政府財政局和商業及動產登記局資料庫內涉及本申請之相關稅務及商業登記資料？
     */
    private Boolean deal;
    /**
     * 管理员
     */
    @ManyToOne
    @Fetch(FetchMode.SELECT)
    private User user;
    /**
     * 主要市場
     */
    @ElementCollection
    @CollectionTable(name = "institution_tarket_market")
    @Enumerated(EnumType.STRING)
    private List<InstitutionTargetMarket> targetMarkets;
    /**
     * 公司/機構業務範圍
     */
    @ElementCollection
    private List<String> scopes;

    /**
     * 機構的附件
     */
    @ElementCollection
    @CollectionTable(name = "institution_attachment_file")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> attachmentFiles;
    /**
     * 商業登記證明副本
     */
    @ElementCollection
    @CollectionTable(name = "institution_business_registration")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> businessRegistrationFiles;
    /**
     * 營業稅—最初開業
     */
    @ElementCollection
    @CollectionTable(name = "institution_sales_tax_open")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> salesTaxOpenFiles;
    /**
     * 營業稅—徵稅憑單 M/8副本(最近一年)
     */
    @ElementCollection
    @CollectionTable(name = "institution_sales_tax")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> salesTaxFiles;
    /**
     * 50%或以上股東相同之有限公司或由同一企業主以不同名稱開設之企業商號，不得重覆在同一展會內向本局報名參展；
     */
    @ElementCollection
    @CollectionTable(name = "institution_shareholder_sames")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> shareholderSamesFiles;
    /**
     * 團體設立之澳門政府公報副本
     */
    @ElementCollection
    @CollectionTable(name = "institution_group_establishment")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> groupEstablishmentFiles;
    /**
     * 身份證明局發出之登記證明書副本
     */
    @ElementCollection
    @CollectionTable(name = "institution_identification_bureau")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> identificationBureauFiles;
    /**
     * 法人代表身份副本
     */
    @ElementCollection
    @CollectionTable(name = "institution_legal_person")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> legalPersonFiles;
    /**
     * 是否为导入数据
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean excelImport;
    /**
     * 认证申请日期
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date applyTime;
    /**
     * 机构认证状态
     */
    @Enumerated(EnumType.STRING)
    private InstitutionCertified certified;
    /**
     * 審批操作記錄
     */
    @OneToMany(mappedBy = "institution", fetch = FetchType.LAZY)
    private List<AdmTaskInstitution> tasks;
    /**
     * 列表展示的机构的第一位联络人
     */
    @Transient
    private Liaison firstLiaison;
    /**
     * 当前审批人id
     */
    private Long currentApproverId;
    /**
     * 当前审批人Name
     */
    @Transient
    private String currentApproverName;

    /**
     * 澳門製造 ，0-否，1-是
     */
    private Boolean madeInMacao;

    /**
     * 澳門品牌 ，0-否，1-是
     */
    private Boolean macaoBrand;

    /**
     * 代理葡語國家產品 ，0-否，1-是
     */
    private Boolean macaoAgency;
    /**
     * 澳門製造附件
     */
    @ElementCollection
    @CollectionTable(name = "institution_made_in_macao")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> madeInMacaoFiles;
    /**
     * 澳門品牌附件
     */
    @ElementCollection
    @CollectionTable(name = "institution_macao_brand")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> macaoBrandFiles;

    /**
     * 代理葡語國家產品附件
     */
    @ElementCollection
    @CollectionTable(name = "institution_macao_agency")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> macaoAgencyFiles;

    /**
     * 来源類型
     */
    @Enumerated(EnumType.STRING)
    private SourceType source;
    /**
     * 公司簡介
     *
     * @param vo
     */

    @Column(columnDefinition = "text")
    private String companyBrief;
    /**
     * 公司英文
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String companyBriefEn;
    /**
     * 公司简介附件
     */
    @ElementCollection
    @CollectionTable(name = "institution_company_profile_annex")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> companyProfileAnnex;

    /**
     * 擴展字段1（BM）
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField1;
    /**
     * 擴展字段2（BM）
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField2;
    /**
     * 擴展字段3（BM）
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField3;
    /**
     * 擴展字段4（BM）
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField4;
    /**
     * 擴展字段5（BM）
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField5;
    /**
     * 擴展字段6（BM）
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField6;
    /**
     * 擴展字段7（BM）
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField7;
    /**
     * 擴展字段8（BM）
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField8;
    /**
     * 擴展字段9（BM）
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField9;
    /**
     * 擴展字段10（BM）
     *
     * @param vo
     */
    @Column(columnDefinition = "text")
    private String bmDescField10;

    /**
     * 网页
     */
    private String webpage;

    //是否商匯館之商品
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isCommodity;
    //是否葡語國家食品展示中心之商品
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isCentreCommodity;
    //是否違規記錄
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isBlackList;
    //機構的違規等級
    @Enumerated(EnumType.STRING)
    private BlackRank blackRank;


    /**
     * 銀行轉載 提供銀行記錄
     */
    @ElementCollection
    @CollectionTable(name = "institution_bank_record")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> bankRecordFiles;
    /**
     * 銀行轉載  銀行授權書
     */
    @ElementCollection
    @CollectionTable(name = "institution_bank_authorize")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> bankAuthorizeFiles;
    /**
     * 機構認證資料確認書
     */
    @ElementCollection
    @CollectionTable(name = "institution_certification_authorize")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> certificationAuthorizeFiles;
    /**
     *所得補充稅M/1 A組或B組收益申報書副本
     */
    @ElementCollection
    @CollectionTable(name = "institution_income")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> incomeFiles;
    /**
     *營運場所 地點證明
     */
    @ElementCollection
    @CollectionTable(name = "institution_operating_site_files")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> operatingSiteFiles;
    /**
     *澳門社會保障基金近兩季發出的供款證明文件副本
     */
    @ElementCollection
    @CollectionTable(name = "institution_deposit")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> depositFiles;
    //银行名称
    private String bankName;
    //賬戶名稱
    private String accountName;
    //賬戶號碼
    private String accountNum;

    /**
     * 參展資助申請次數：本年度
     */
    private Integer enterpriseYearNum;
    /**
     * 參展資助申請次數：下一年度
     */
    private Integer enterpriseNextYearNum;

    private String website;
    //社保僱員編號
    private String socialSecurityNo;
    //社保僱員名稱
    private String socialSecurityName;


    public Institution(InstitutionVO vo) {
        if (vo != null) {
            BeanUtils.copyProperties(vo, this, IGNORE_PROPERTIES);
            Long areaCodeId = vo.getAreaCodeId();
            if (null != areaCodeId) {
                AreaCode code = new AreaCode();
                code.setId(areaCodeId);
                this.setAreaCode(code);
            }

            //機構的管理員
            if (null != vo.getAdminId()) {
                User user = new User();
                user.setId(vo.getAdminId());
                this.user = user;
            }

            if (!CollectionUtils.isEmpty(vo.getIndustries())) {
                List<InstitutionIndustry> collect = vo
                        .getIndustries()
                        .stream()
                        .map(InstitutionIndustry::new)
                        .peek(s -> s.setInstitution(this))
                        .collect(Collectors.toList());
                setIndustries(collect);
            } else {
                setIndustries(Collections.emptyList());
            }
            List<InstitutionShareholderVO> shareholderVOList = vo.getInstitutionShareholders();
            if (!CollectionUtils.isEmpty(shareholderVOList)) {

                this.setInstitutionShareholders(shareholderVOList.stream().map(InstitutionShareholder::new).collect(Collectors.toList()));
            }

            List<ParticipateCertVO> attachmentFiles = vo.getAttachmentFiles();
            if (!CollectionUtils.isEmpty(attachmentFiles)) {
                this.setAttachmentFiles(attachmentFiles.stream().map(ParticipateCert::new).collect(Collectors.toList()));
            }

            List<ParticipateCertVO> macaoBrandFiles = vo.getMacaoBrandFiles();
            if (!CollectionUtils.isEmpty(macaoBrandFiles)) {
                this.setMacaoBrandFiles(macaoBrandFiles.stream().map(ParticipateCert::new).collect(Collectors.toList()));
            }

            List<ParticipateCertVO> macaoAgencyFiles = vo.getMacaoAgencyFiles();
            if (!CollectionUtils.isEmpty(macaoAgencyFiles)) {
                this.setMacaoAgencyFiles(macaoAgencyFiles.stream().map(ParticipateCert::new).collect(Collectors.toList()));
            }
            List<ParticipateCertVO> companyProfileAnnex = vo.getCompanyProfileAnnex();
            if (!CollectionUtils.isEmpty(companyProfileAnnex)) {
                this.setCompanyProfileAnnex(companyProfileAnnex.stream().map(ParticipateCert::new).collect(Collectors.toList()));
            }
            List<ParticipateCertVO> madeInMacaoFiles = vo.getMadeInMacaoFiles();
            if (!CollectionUtils.isEmpty(madeInMacaoFiles)) {
                this.setMadeInMacaoFiles(madeInMacaoFiles.stream().map(ParticipateCert::new).collect(Collectors.toList()));
            }

            List<ParticipateCertVO> registrationFiles = vo.getBusinessRegistrationFiles();
            if (!CollectionUtils.isEmpty(registrationFiles)) {
                this.setBusinessRegistrationFiles(registrationFiles.stream().map(ParticipateCert::new).collect(Collectors.toList()));
            }

            List<ParticipateCertVO> openFiles = vo.getSalesTaxOpenFiles();
            if (!CollectionUtils.isEmpty(openFiles)) {
                this.setSalesTaxOpenFiles(openFiles.stream().map(ParticipateCert::new).collect(Collectors.toList()));
            }

            List<ParticipateCertVO> salesTaxFiles = vo.getSalesTaxFiles();
            if (!CollectionUtils.isEmpty(salesTaxFiles)) {
                this.setSalesTaxFiles(salesTaxFiles.stream().map(ParticipateCert::new).collect(Collectors.toList()));
            }

            List<ParticipateCertVO> samesFiles = vo.getShareholderSamesFiles();
            if (!CollectionUtils.isEmpty(samesFiles)) {
                this.setShareholderSamesFiles(samesFiles.stream().map(ParticipateCert::new).collect(Collectors.toList()));
            }

            List<ParticipateCertVO> establishmentFiles = vo.getGroupEstablishmentFiles();
            if (!CollectionUtils.isEmpty(establishmentFiles)) {
                this.setGroupEstablishmentFiles(establishmentFiles.stream().map(ParticipateCert::new).collect(Collectors.toList()));
            }

            List<ParticipateCertVO> bureauFiles = vo.getIdentificationBureauFiles();
            if (!CollectionUtils.isEmpty(bureauFiles)) {
                this.setIdentificationBureauFiles(bureauFiles.stream().map(ParticipateCert::new).collect(Collectors.toList()));
            }

            List<ParticipateCertVO> personFiles = vo.getLegalPersonFiles();
            if (!CollectionUtils.isEmpty(personFiles)) {
                this.setLegalPersonFiles(personFiles.stream().map(ParticipateCert::new).collect(Collectors.toList()));
            }
            List<EncourageCertVO> voIncomeFiles = vo.getIncomeFiles();
            if (!CollectionUtils.isEmpty(voIncomeFiles)) {
                this.setIncomeFiles(voIncomeFiles.stream().map(EncourageCert::new).collect(Collectors.toList()));
            }
            List<EncourageCertVO> voOperatingSiteFiles = vo.getOperatingSiteFiles();
            if (!CollectionUtils.isEmpty(voOperatingSiteFiles)) {
                this.setOperatingSiteFiles(voOperatingSiteFiles.stream().map(EncourageCert::new).collect(Collectors.toList()));
            }
            List<EncourageCertVO> voDepositFiles = vo.getDepositFiles();
            if (!CollectionUtils.isEmpty(voDepositFiles)) {
                this.setDepositFiles(voDepositFiles.stream().map(EncourageCert::new).collect(Collectors.toList()));
            }
        }
    }

    public InstitutionVO toVO() {
        return toVO(false);
    }

    public InstitutionVO toVO(boolean includeLazy) {
        InstitutionVO vo = new InstitutionVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);

        if (null != areaCode) {
            vo.setAreaCodeId(areaCode.getId());
            vo.setAreaCode(areaCode.toVO());
        }
        //審批流
        if (!CollectionUtils.isEmpty(this.tasks)) {
            // 当前审批节点
            vo.setCurrentTask(this.getTasks().stream()
                    .map(AdmTask::toSimpleVO)
                    .filter(t -> t.getStatus() == Status.approving)
                    .findFirst().orElse(null));
        }

        if (null != firstLiaison) {
            vo.setFirstLiaison(firstLiaison.toVO());
        }
        if (null != this.user) {
            vo.setAdminId(user.getId());
            if(null!=user.getLastName()){
                vo.setAdminName(user.getName()+user.getLastName()+"<"+user.getAccount()+">");
            }else{
                vo.setAdminName(user.getName()+"<"+user.getAccount()+">");
            }
            vo.setAdminNameAccount(user.getName()+"<"+(user.getAccount())+">");
        }
        if (institutionShareholders != null) {
            vo.setInstitutionShareholders(institutionShareholders.stream().map(InstitutionShareholder::toVO).collect(Collectors.toList()));

            String s = institutionShareholders.stream().map(p -> p.getName()).collect(Collectors.joining(","));
            String b = null;
            shareholderComponents = s;
            for (InstitutionShareholder institutionShareholder : institutionShareholders) {
                if (StringUtils.isNotEmpty(institutionShareholder.getName())) {
                    if (b != null) {
                        b = b + "," + institutionShareholder.getName() + " - " + institutionShareholder.getPercent() +"%";
                    } else {
                        b = institutionShareholder.getName() + " - " + institutionShareholder.getPercent() + "%";
                    }
                }
            }
            if (b != null) {
                vo.setShareholderComponentsPercent(b);
            }
        }

        if (includeLazy) {

            if (null != this.user) {
                vo.setAdminId(user.getId());
                if(null!=user.getLastName()){
                    vo.setAdminName(user.getName()+user.getLastName()+"<"+user.getAccount()+">");
                }else{
                    vo.setAdminName(user.getName()+"<"+user.getAccount()+">");
                }
                vo.setAdminNameAccount(user.getName().concat("<").concat(user.getAccount()).concat(">"));
            }


            if (!CollectionUtils.isEmpty(this.tasks)) {
                vo.setTasks(this.tasks.stream().map(AdmTask::toVO).collect(Collectors.toList()));
            }

            if (!CollectionUtils.isEmpty(industries)) {
                vo.setIndustries(industries.stream().map(InstitutionIndustry::toVO).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(liaisons)) {
                vo.setLiaisons(liaisons.stream().map(Liaison::toVO).collect(Collectors.toList()));
            }

            if (!CollectionUtils.isEmpty(attachmentFiles)) {
                vo.setAttachmentFiles(attachmentFiles.stream().map(ParticipateCert::toVO).collect(Collectors.toList()));
            }

            if (!CollectionUtils.isEmpty(businessRegistrationFiles)) {
                vo.setBusinessRegistrationFiles(businessRegistrationFiles.stream().map(ParticipateCert::toVO).collect(Collectors.toList()));
            }

            if (!CollectionUtils.isEmpty(salesTaxOpenFiles)) {
                vo.setSalesTaxOpenFiles(salesTaxOpenFiles.stream().map(ParticipateCert::toVO).collect(Collectors.toList()));
            }

            if (!CollectionUtils.isEmpty(salesTaxFiles)) {
                vo.setSalesTaxFiles(salesTaxFiles.stream().map(ParticipateCert::toVO).collect(Collectors.toList()));
            }

            if (!CollectionUtils.isEmpty(shareholderSamesFiles)) {
                vo.setShareholderSamesFiles(shareholderSamesFiles.stream().map(ParticipateCert::toVO).collect(Collectors.toList()));
            }

            if (!CollectionUtils.isEmpty(groupEstablishmentFiles)) {
                vo.setGroupEstablishmentFiles(groupEstablishmentFiles.stream().map(ParticipateCert::toVO).collect(Collectors.toList()));
            }

            if (!CollectionUtils.isEmpty(identificationBureauFiles)) {
                vo.setIdentificationBureauFiles(identificationBureauFiles.stream().map(ParticipateCert::toVO).collect(Collectors.toList()));
            }

            if (!CollectionUtils.isEmpty(legalPersonFiles)) {
                vo.setLegalPersonFiles(legalPersonFiles.stream().map(ParticipateCert::toVO).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(institutionShareholders)) {
                vo.setInstitutionShareholders(institutionShareholders.stream().map(InstitutionShareholder::toVO).collect(Collectors.toList()));
            }else  {
                InstitutionShareholder institutionShareholder = new InstitutionShareholder();
                institutionShareholder.setName("");
                institutionShareholder.setPercent("");
                institutionShareholder.setMacaoResidents(false);
                institutionShareholders.add(institutionShareholder);
                vo.setInstitutionShareholders(institutionShareholders.stream().map(InstitutionShareholder::toVO).collect(Collectors.toList()));

            }
        }
        return vo;
    }


    public InstitutionSimpleVO toSimpleVO() {
        InstitutionSimpleVO vo = new InstitutionSimpleVO();
        if (null != this.user) {
            vo.setAdminId(user.getId());
            if(null!=user.getLastName()){
                vo.setAdminName(user.getName()+user.getLastName()+"<"+user.getAccount()+">");
            }else{
                vo.setAdminName(user.getName()+"<"+user.getAccount()+">");
            }
            vo.setAdminNameAccount(user.getName().concat("<").concat(user.getAccount()).concat(">"));
        }
        if (institutionShareholders != null) {
            String s = institutionShareholders.stream().map(p -> p.getName()).collect(Collectors.joining(","));
            String b = null;
            shareholderComponents = s;
            for (InstitutionShareholder institutionShareholder : institutionShareholders) {
                if (StringUtils.isNotEmpty(institutionShareholder.getName())) {
                    if (b != null) {
                        b = b + "," + institutionShareholder.getName() + " - " + institutionShareholder.getPercent() +"%";
                    } else {
                        b = institutionShareholder.getName() + " - " + institutionShareholder.getPercent() + "%";
                    }
                }
            }
            if (b != null) {
                vo.setShareholderComponentsPercent(b);
            }
        }
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);
        return vo;
    }

    public EdmInstitutionSimpleVO toEdmVO() {
        EdmInstitutionSimpleVO vo = new EdmInstitutionSimpleVO();
        if (null != this.user) {
            vo.setAdminId(user.getId());
            if(null!=user.getLastName()){
                vo.setAdminName(user.getName()+user.getLastName()+"<"+user.getAccount()+">");
            }else{
                vo.setAdminName(user.getName()+"<"+user.getAccount()+">");
            }
            vo.setAdminNameAccount(user.getName().concat("<").concat(user.getAccount()).concat(">"));
            vo.setAdminEmail(user.getEmail());
            vo.setAdminTel(user.getTel());
            vo.setUser(user.toVO());
            vo.setInterestedCountries(user.getInterestedCountries());
            vo.setInterestedProducts(user.getInterestedProducts());
            vo.setInterestingContent(user.getInterestingContent());
        }
        if (institutionShareholders != null) {
            String s = institutionShareholders.stream().map(p -> p.getName()).collect(Collectors.joining(","));
            shareholderComponents = s;
        }
        if (industryNumberList != null) {
            vo.setIndustryNumberListVO(industryNumberList);
        }
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);
        return vo;
    }
}
