package com.exhibition.domain.sys;

import java.util.Arrays;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.sys
 * @ClassName: Gender
 * @description: 稱呼
 * @author: ShiXin
 * @create: 2020-02-21 17:27
 **/
public enum Call {

    /**
     * 男
     */
    M("先生"),
    /**
     * 女
     */
    F("女士"),
    /**
     * 小姐
     */
    O("小姐"),
    /**
     * 博士
     */
    B("博士");

    private final String name;

    Call(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static Call getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
