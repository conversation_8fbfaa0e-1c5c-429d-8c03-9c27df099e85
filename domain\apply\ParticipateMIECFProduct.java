package com.exhibition.domain.apply;

import java.util.Arrays;

public enum ParticipateMIECFProduct {
    /**
     * 綠色交通
     */
    GREEN_TRANSPORTATION("綠色交通"),
    /**
     * 再生能源
     */
    RENEWABLE_ENERGY("能源效益及再生能源"),
    /**
     * 綠色建築
     */
    CARBON("綠色建築"),
    /**
     * 空氣質素
     */
    GREEN_BUILDING("空氣質素"),
    /**
     * 廢棄物管理解決方案
     */
    AIR_QUALITY("廢棄物管理及解決方案"),
    /**
     * 水資源及廢水管理方案
     */
    WASTE_MANAGEMENT_SOLUTIONS("水資源及污水管理方案"),

    /**
     * 環保產品及服務
     */
    WATER("環保產品及服務"),
    /**
     * 土壤修復及葡區淨化
     */
    ENVIRONMENTAL_PRODUCTS_AND_SERVICES("土壤修復及舊區淨化"),
    /**
     * 環境監察及環境大數據服務
     */
    SOIL_REMEDIATION("環境監察及環境大數據服務"),
    /**
     * 生態城市整合方案
     */
    ENVIRONMENTAL_MONITORING("生態城市整合方案"),
    ECO_CITY_INTEGRATION_PLAN("减塑"),
    PLASTIC("碳交易方案"),
    /**
     * 其他
     */
    OTHER("其他");

    private final String name;

    ParticipateMIECFProduct(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ParticipateMIECFProduct getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }
}
