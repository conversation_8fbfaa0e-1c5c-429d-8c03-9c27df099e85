package com.exhibition.domain.thirdparty.FSS.model.req;

import com.exhibition.domain.thirdparty.FSS.model.res.IdentityPositionRes;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/***
 * <AUTHOR>
 * @date 2024/12/18 13:38
 * @describe 身份证明局接口
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class IdentityArchitectureReq implements Serializable {

    private String user_id;
    private RequestDataDTO request_data;
    private String sign;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString
    public static class RequestDataDTO implements Serializable {
        private String asso_no;
    }

}
