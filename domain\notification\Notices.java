package com.exhibition.domain.notification;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.apply.Encourage;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.BatchSize;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 2023年04月22日 13:53
 */
@Data
@NoArgsConstructor
@Entity
@BatchSize(size = 20)
@JsonView(Encourage.EncourageSimpleView.class)
public class Notices extends BaseEntity implements Serializable {

}
