package com.exhibition.domain.apply;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Embeddable;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 公開考試
 * @date 2020-06-09
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@Embeddable
public class ExamDesc implements Serializable {
    
    private static final long serialVersionUID = -1942529582693930718L;
    
    /**
     * 主要科目名稱
     */
    private String               name;
    /**
     * 考試地點
     */
    private String               address;
    /**
     * 考試方式
     */
    private String               pattern;
    /**
     * 合格方式
     */
    private Double               cutScore;
}