package com.exhibition.domain.apply;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Embeddable;
import java.io.Serializable;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: EnterpriseGroup
 * @description: 企業組團
 * @author: ShiXin
 * @create: 2020-03-19 15:25
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Embeddable
public class ParticipateEnterpriseGroup implements Serializable {

    private static final long serialVersionUID = 6001923175652822811L;

    /**
     * 企業名稱
     */
    private String enterpriseName;

    /**
     * 聯絡人名稱
     */
    private String liaisonName;

    /**
     * 聯絡電話
     */
    private String liaisonTel;

    /**
     * 電子郵件
     */
    private String email;


}
