package com.exhibition.domain.apply;

import java.util.Arrays;

public enum AppOtherMainlandFrequencyEnum {

    ONE("壹次"),

    TWO("贰次"),

    REPEATEDLY("多次");


    private final String name;

    AppOtherMainlandFrequencyEnum(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static AppOtherMainlandFrequencyEnum getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }

}
