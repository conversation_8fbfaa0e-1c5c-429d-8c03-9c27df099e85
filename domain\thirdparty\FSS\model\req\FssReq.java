package com.exhibition.domain.thirdparty.FSS.model.req;

import com.exhibition.domain.thirdparty.FSS.model.entity.FSS;
import lombok.*;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 * @date 2023/11/29 11:56
 * @describe: FSS 请求接口
 */
@Getter
@Setter
public class FssReq {
    /*用户名称*/
    private String username;
    /*部門名稱*/
    private String dept;
    /*財務納稅人編號*/
    private String dsfNo;
    /*社保雇主注冊編號（必須是10為長度數字符號）*/
    private String fssEmpNo;
    /*開始季度（YYYY,Q=1,2,3,4）*/
    private Integer strYQ;
    /*結束季度（YYYY,Q=1,2,3,4）*/
    private Integer endYQ;

}
