package com.exhibition.domain.thirdparty.FSS.model.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/12/9 17:38
 * @describe 經濟及科技發展局接口
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class IdentityReq {
    /*id	否	「自然人/社團」身份證號碼[不帶()]	*/
    private String idNumber;
    /*    birthday	否	「自然人」出生日期	*/
    private String birthday;
    /*    associationId	否	「社團」社團編號	*/
    private String associationId;
    /**
     * 机构名称(中文)
     */
    private String nameZh;
    /**
     * 机构名称(英文)
     */
    private String nameEn;
    /**
     * 机构名称(葡文)
     */
    private String namePt;
    //"P" for permanent; "N" for non-permanent;
    private String idType="P";
    //"M" for male; "F" for female;
    private String gender="M";

}
