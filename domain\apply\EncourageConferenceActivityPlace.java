package com.exhibition.domain.apply;

import com.exhibition.domain.activity.ActivityHelperType;
import com.exhibition.domain.sys.Liaison;
import com.exhibition.vo.apply.EncourageConferenceActivityPlaceVO;
import com.exhibition.vo.apply.EncourageConferenceEstimatedScheduleVO;
import com.exhibition.vo.apply.EncourageConferenceVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/27 16:27
 * @describe
 */
@Data
@NoArgsConstructor
@Embeddable
public class EncourageConferenceActivityPlace  implements Serializable {

    @Column(length = 500)
    private String activityPlace;

    @Column(length = 20)
    private Integer sortId;

    public EncourageConferenceActivityPlace(EncourageConferenceActivityPlaceVO vo) {
        BeanUtils.copyProperties(vo,this);
    }

    public EncourageConferenceActivityPlaceVO toVO() {
        return toVO(false);
    }

    public EncourageConferenceActivityPlaceVO toVO(boolean includeLazy) {
        EncourageConferenceActivityPlaceVO vo = new EncourageConferenceActivityPlaceVO();
        BeanUtils.copyProperties(this, vo);

        return vo;
    }
}
