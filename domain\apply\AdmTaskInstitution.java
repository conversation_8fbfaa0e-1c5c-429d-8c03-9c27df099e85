package com.exhibition.domain.apply;

import com.exhibition.domain.sys.Institution;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: AdmTaskInstitution
 * @description: 機構審批任務
 * @author: ShiXin
 * @create: 2020-03-14 09:54
 **/
@Data
@NoArgsConstructor
@Entity
@DiscriminatorValue(Flowtpl.INSTITUTION)
public class AdmTaskInstitution extends AdmTask {

    private static final long serialVersionUID = 6224041033089026576L;

    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    private Institution institution;


}
