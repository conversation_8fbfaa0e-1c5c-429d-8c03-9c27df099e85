package com.exhibition.domain.thirdparty;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.exhibition.base.entity.BaseEntity;
import com.exhibition.vo.apply.EncourageConventionVO;
import com.exhibition.vo.thirdparty.ExternalApiLogVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

import static org.springframework.beans.BeanUtils.copyProperties;

/**
 * <AUTHOR>
 * @date 2024/5/31 16:30
 * @describe 用于存储外部API接口返回的日志信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Entity
@Table(name = "externalApiLog")
public class ExternalApiLog extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 7569368546235547151L;

    @ApiModelProperty(value = "Api发送时间")
    @Temporal(TemporalType.TIMESTAMP)
    private Date apiSeedTime;

    @ApiModelProperty(value = "请求地址")
    private String apiRequestAddress;

    @ApiModelProperty(value = "請求參數")
    @Column(columnDefinition = "text",length = 500)
    private String apiRequestParam;

    @ApiModelProperty(value = "類型")
    private String apiRequestType;

    @ApiModelProperty(value = "標題")
    private String apiTitle;

    @ApiModelProperty(value = "接口類型")
    private String apiInterfaceType;

    @ApiModelProperty(value = "接口狀態")
    private String apiInterfaceStatus;

    @ApiModelProperty(value = "返回參數")
    @Column(columnDefinition = "text",length = 500)
    private String apiRespondData;

    @ApiModelProperty(value = "Api接受时间")
    @Temporal(TemporalType.TIMESTAMP)
    private Date apiReceiveTime;

    public ExternalApiLog(ExternalApiLogVO vo) {
        copyProperties(vo, this);
        }
}
