/**
 * Copyright (C), 2020-2020, 珠海联创有限公司
 * FileName: ParticipateAlone
 * Author:   liang
 * Date:     20-3-4 下午3:04
 * Description: 独立参展
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.exhibition.domain.apply;

import com.exhibition.vo.apply.EncourageCertVO;
import com.exhibition.vo.apply.EncourageECB2CVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.util.CollectionUtils;

import javax.persistence.CollectionTable;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @desc 038電子商務推廣（應用 B2C 平台）鼓勵措施
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "encourage_ecb2c")
public class EncourageECB2C extends Encourage<EncourageECB2C, EncourageECB2CVO> implements Serializable {

    private static final long serialVersionUID = 4965312976545574289L;

    /** 同意 */
    private Boolean stateAgree;

    /** 知悉 */
    private Boolean stateKnow;

    /** 申請者(自然人/法人名稱，請參照 M/1 納稅人姓名/公司名稱填寫) */
    private String applicantZh;
    private String applicantEngPt;

    /**
     * 纳税人編號
     */
    private String taxpayerCode;

    /**
     * 行業(按營業稅檔案，請填寫文字):
     */
    private String industry;

    /** 場所登記(營業稅檔案)名稱: */
    private String placeName;

    /** 場所登記(營業稅檔案)編號 */
    private String placeNum;

    /** 營運地址(按 M/1 上場所之坐落地點) */
    private String oprAddr;

    /** 成立日期 */
    private Date foundDate;

    /** 商業及動產登記局登記編號 */
    private String propertyRegNum;

    /** 主要股东 */
    @ElementCollection
    @CollectionTable(name = "encourage_ecb2c_chiefShareHolders")
    @Fetch(FetchMode.SELECT)
    private List<EncoShareholder> chiefShareHolders;

    /**
     * 由澳門居民擁有之股權或控股權百分比
     */
    private String macaoResiHoldPercent;

    /** 聯絡人名稱 */
    private String liaisonName;

    /** 聯絡人職位 */
    private String liaisonTitle;

    /** 聯絡人電話 */
    private String liaisonPhone;

    /** 聯絡人傳真 */
    private String liaisonFax;

    /** 聯絡人電郵 */
    private String liaisonEmail;

    /** 聯絡人網址 */
    private String liaisonURL;

    /** 電商平台名稱 */
    private String ecPlatformNmae;

    /** 欲於網上推廣及銷售之產品簡介 */
    private String ecProductInfo;

    /** 技術年費 */
    private Double costTechAnnual;

    /** 增值服務費用 */
    private Double costVAS;

    /**
     * 商業登記證明
     */
    @ElementCollection
    @CollectionTable(name = "encourage_ecb2c_registerFiles")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> registerFiles;
    /**
     * 營業稅-開業/更改申報表
     */
    @ElementCollection
    @CollectionTable(name = "encourage_ecb2c_identityForms")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> identityForms;
    /**
     * 營業稅-徵稅憑單
     */
    @ElementCollection
    @CollectionTable(name = "encourage_ecb2c_taxationBills")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> taxationBills;
    /**
     * 個人企業主需提交企業主的澳門身份證明文件副本；法人商業企業主需提交企業股東之澳門身份證明文件副本
     */
    @ElementCollection
    @CollectionTable(name = "encourage_ecb2c_identityFiles")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> identityFiles;
    /**
     * 申請單位簡介
     */
    @ElementCollection
    @CollectionTable(name = "encourage_ecb2c_companyProfileFiles")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> companyProfileFiles;
    /**
     * 產品資料
     */
    @ElementCollection
    @CollectionTable(name = "encourage_ecb2c_productInfoFiles")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> productInfoFiles;
    /**
     * 報價單
     */
    @ElementCollection
    @CollectionTable(name = "encourage_ecb2c_quotations")
    @Fetch(FetchMode.SELECT)
    private List<EncourageCert> quotations;

    /**
     * 纳税人類型
     */

    @Enumerated(EnumType.STRING)
    private TaxpayerType taxpayerType;

    /**
     * 收集個人資料聲明 1
     */
    Boolean isDocuStatement1;

    /**
     * 收集個人資料聲明 2
     */
    Boolean isDocuStatement2;

    private static final String[] IGNORE_PROPERTIES = new String[] { "registerFiles", "identityForms", "taxationBills",
            "identityFiles", "companyProfileFiles", "productInfoFiles", "quotations", };

    public EncourageECB2C(EncourageECB2CVO vo) {
        copyProperties(vo, this);
        List<EncourageCertVO> registerFiles = vo.getRegisterFiles();
        if (Objects.nonNull(registerFiles) && !CollectionUtils.isEmpty(registerFiles)) {
            this.registerFiles = registerFiles.stream().map(EncourageCert::new).collect(Collectors.toList());
        }

        List<EncourageCertVO> identityForms = vo.getIdentityForms();
        if (Objects.nonNull(identityForms) && !CollectionUtils.isEmpty(identityForms)) {
            this.identityForms = identityForms.stream().map(EncourageCert::new).collect(Collectors.toList());
        }
        List<EncourageCertVO> taxationBills = vo.getTaxationBills();
        if (Objects.nonNull(taxationBills) && !CollectionUtils.isEmpty(taxationBills)) {
            this.taxationBills = taxationBills.stream().map(EncourageCert::new).collect(Collectors.toList());
        }
        List<EncourageCertVO> identityFiles = vo.getIdentityFiles();
        if (Objects.nonNull(identityFiles) && !CollectionUtils.isEmpty(identityFiles)) {
            this.identityFiles = identityFiles.stream().map(EncourageCert::new).collect(Collectors.toList());
        }
        List<EncourageCertVO> companyProfileFiles = vo.getCompanyProfileFiles();
        if (Objects.nonNull(companyProfileFiles) && !CollectionUtils.isEmpty(companyProfileFiles)) {
            this.companyProfileFiles = companyProfileFiles.stream().map(EncourageCert::new)
                    .collect(Collectors.toList());
        }
        List<EncourageCertVO> productInfoFiles = vo.getProductInfoFiles();
        if (Objects.nonNull(productInfoFiles) && !CollectionUtils.isEmpty(productInfoFiles)) {
            this.productInfoFiles = productInfoFiles.stream().map(EncourageCert::new).collect(Collectors.toList());
        }
        List<EncourageCertVO> quotations = vo.getQuotations();
        if (Objects.nonNull(quotations) && !CollectionUtils.isEmpty(quotations)) {
            this.quotations = quotations.stream().map(EncourageCert::new).collect(Collectors.toList());
        }
    }

    @Override
    public EncourageECB2CVO toVO() {
        return toVO(false);
    }

    public EncourageECB2CVO toVO(boolean includeLazy) {
        EncourageECB2CVO vo = new EncourageECB2CVO();
        copyProperties(this, vo);

        List<EncourageCert> registerFiles = this.getRegisterFiles();
        if (Objects.nonNull(registerFiles) && !CollectionUtils.isEmpty(registerFiles)) {
            vo.setRegisterFiles(registerFiles.stream().map(file -> file.toVO()).collect(Collectors.toList()));
        }

        List<EncourageCert> identityForms = this.getIdentityForms();
        if (Objects.nonNull(identityForms) && !CollectionUtils.isEmpty(identityForms)) {
            vo.setIdentityFiles(identityForms.stream().map(file -> file.toVO()).collect(Collectors.toList()));
        }
        List<EncourageCert> taxationBills = this.getTaxationBills();
        if (Objects.nonNull(taxationBills) && !CollectionUtils.isEmpty(taxationBills)) {
            vo.setTaxationBills(taxationBills.stream().map(file -> file.toVO()).collect(Collectors.toList()));
        }
        List<EncourageCert> identityFiles = this.getIdentityFiles();
        if (Objects.nonNull(identityFiles) && !CollectionUtils.isEmpty(identityFiles)) {
            vo.setIdentityFiles(identityFiles.stream().map(file -> file.toVO()).collect(Collectors.toList()));
        }
        List<EncourageCert> companyProfileFiles = this.getCompanyProfileFiles();
        if (Objects.nonNull(companyProfileFiles) && !CollectionUtils.isEmpty(companyProfileFiles)) {
            vo.setCompanyProfileFiles(
                    companyProfileFiles.stream().map(file -> file.toVO()).collect(Collectors.toList()));
        }
        List<EncourageCert> productInfoFiles = this.getProductInfoFiles();
        if (Objects.nonNull(productInfoFiles) && !CollectionUtils.isEmpty(productInfoFiles)) {
            vo.setProductInfoFiles(productInfoFiles.stream().map(file -> file.toVO()).collect(Collectors.toList()));
        }
        List<EncourageCert> quotations = this.getQuotations();
        if (Objects.nonNull(quotations) && !CollectionUtils.isEmpty(quotations)) {
            vo.setQuotations(quotations.stream().map(file -> file.toVO()).collect(Collectors.toList()));
        }
        return vo;
    }
}
