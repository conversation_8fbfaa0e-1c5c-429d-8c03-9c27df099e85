package com.exhibition.domain.sys;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.enums.Confirm;
import com.exhibition.vo.sys.RegionVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.*;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaDelete;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.CriteriaUpdate;
import javax.persistence.metamodel.Metamodel;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "region")
public class Region extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 4950229695117153086L;

    /**
     * 名稱
     */
    private String       name;
    /**
     * 英文
     */
    private String       nameEn;
    /**
     * 葡文
     */
    private String       namePt;
    /**
     * 所属分组
     */
    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    private Region       parent;
    /**
     * 当區域类型为分组时，显示期下面的子區域
     */
//    @OneToMany(mappedBy = "parent", fetch = FetchType.LAZY)
//    @BatchSize(size = 20)
    //private List<Region> child;
    /**
     * 地名簡稱
     */
    private String       sName;
    /**
     * 區域等級
     */
    private Integer      level;
    /**
     * 區域編碼
     */
    private String       cityCode;
    /**
     * 郵政編碼
     */
    private String       yzCode;
    /**
     * 組合名稱
     */
    private String       merName;

    /**
     * 31mapping
     */
    private String       mapping;

    /**
     * code
     */
    private String       code;
    /**
     * 數據來源
     */
    private String       source;
    /**
     * 是否只读
     */
    @Enumerated(EnumType.STRING)
    private Confirm readonly;
    /**
     * 是否禁用
     */
    @Enumerated(EnumType.STRING)
    private Confirm      disabled;
    /**
     * 是否删除
     */
    @Enumerated(EnumType.STRING)
    private Confirm      deleted;

    public Region(RegionVO vo) {

        BeanUtils.copyProperties(vo, this);

    }

    public RegionVO toVO() {
        return toVO(false);
    }

    public RegionVO toVO(boolean includeLazy) {
        RegionVO vo = new RegionVO();
        BeanUtils.copyProperties(this, vo, "child");
        if(null!=parent){
            vo.setParentId(parent.getId());
        }
        //機構的管理員
        if (null != vo.getParentId()) {
            Region region = vo.getParent();
            if (region != null) {
                region.setId(vo.getParentId());
                this.parent = region;
            }

        }
        return vo;
    }
}
