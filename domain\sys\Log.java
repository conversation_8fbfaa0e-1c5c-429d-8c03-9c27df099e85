package com.exhibition.domain.sys;

import com.exhibition.vo.sys.LogVo;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Data
@NoArgsConstructor
@Entity
@Table(name = "log")
public class Log {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long   id;

    /**
     *用户ID
     */
    @Column
    private Long userId;

    /**
     * 用户名称
     */
    @Column
    private String userName;

    /**
     *操作方法
     */
    @Column
    private String operMethod;

    /**
     * 操作参数
     */
    @Column
    private String requestParam;

    /**
     * 操作说明
     */
    @Column
    private String operDesc;

    /**
     * 类型
     */
    @Column
    private String type;

    /**
     * 操作类型
     */
    @Column
    private String operateType;

    /**
     * 创建时间
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date   createTime;

    /**
     * 更新时间
     */
    @Temporal(TemporalType.TIMESTAMP)
    private Date   updateTime;

    /**
     * 唯一编码
     */
    @Column
    private String code;

    /**
     *ip地址
     */
    @Column
    private String ipAddress;


    public Log(LogVo log) {
        BeanUtils.copyProperties(log, this);
    }

    public LogVo toVO() {
        return toVO(false);
    }

    public LogVo toVO(boolean includeRole) {
        LogVo vo = new LogVo();
        BeanUtils.copyProperties(this, vo);

        if (includeRole) {
        }
        return vo;
    }
}
