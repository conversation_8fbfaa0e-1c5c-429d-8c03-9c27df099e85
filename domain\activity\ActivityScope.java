package com.exhibition.domain.activity;

import com.exhibition.domain.apply.Encourage;
import com.exhibition.exception.RenException;
import com.fasterxml.jackson.annotation.JsonView;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @desc 展會範圍
 * @date 2020-03-17
 * @since 1.0.0
 */
@JsonView(Encourage.EncourageSimpleView.class)
public enum ActivityScope {
    /**
     * 本局主辦之展會
     */
    LOCAL("本局主辦之展會"),
    /**
     * 本局組織之境外展會
     */
    OUTLANDS("本局組織之境外展會"),
    /**
     * 支持及鼓勵措施
     */
    ENCOURAGE("支持及鼓勵措施"),
    /**
     * 支持及鼓勵措施
     */
    ONESTEP("一站式跟進活動"),
    /**
     * 支持及鼓勵措施
     */
    NOIPIM("非本局主辦的活動"),
    /**
     * 其他
     */
    OTHER("其他");

    private final String name;

    ActivityScope( String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static ActivityScope getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }

    public static ActivityScope getEnumFromString(String string) {
        if (string != null) {
            try {
                return Enum.valueOf(ActivityScope.class, string.trim());
            } catch (IllegalArgumentException e) {
                throw new RenException(e.getMessage());
            }
        }
        return null;
    }
}
