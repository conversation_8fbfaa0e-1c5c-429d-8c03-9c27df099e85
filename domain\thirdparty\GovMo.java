package com.exhibition.domain.thirdparty;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.apply.Encourage;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import javax.persistence.CollectionTable;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@Entity
@Table(name = "macau_one_account")
@BatchSize(size = 20)
@JsonView(Encourage.EncourageSimpleView.class)
public class GovMo extends BaseEntity {
    @Temporal(TemporalType.TIMESTAMP)
    private Date birthday;

    private String preferredLanguage;

    private String gender;

    @Temporal(TemporalType.TIMESTAMP)
    private Date agreeTime;

    private String firstName;

    private String username;

    private String loginName;

    private String uid;

    private String euid;

    private String identityNo;

    private String identityType;

    private String identityIssuePlace;

    private String userTyp;

    private String nameCn;

    private String namePt;

    private String homeAddress;

    private String email;

    private String sepbox;

    private String mobile;

    private Boolean isActive;

    private Boolean verified;

    private Boolean isEsignBind;

    private Boolean isStaff;

    private String otpChannel;

    private String staffCode;

    private String departmentCode;

    private String departmentAlias;

    private String govEntityAccount;

    private Boolean isSuperuser;

    @Transient
    private Boolean bind;

    /**
     * 机构联络人
     */
    @ElementCollection
    @CollectionTable(name = "macau_one_account_entity_identityDocs")
    @Fetch(FetchMode.SELECT)
    private List<GovMoIdentityDocs> identityDocs;
}
