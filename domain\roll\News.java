package com.exhibition.domain.roll;

import com.exhibition.vo.roll.AnswerVO;
import com.exhibition.vo.roll.NewsVO;
import com.exhibition.vo.roll.QuestionItemVO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 *结果
 *
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@Entity
@Table(name = "news")
public class News implements Serializable {

    private static final String[] IGNORE_PROPERTIES = new String[]{};

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 標題
     */
    @Column(columnDefinition = "text")
    private String     titleSC;
    /**
     * 展會配圖
     */
    private String scope;
    /**
     * 類型
     */
    @Enumerated(EnumType.STRING)
    private NewsType newsType;
    /**
     * 標題
     */
    @Column(columnDefinition = "text")
    private String     titleTC;
    /**
     * 標題
     */
    @Column(columnDefinition = "text")
    private String     titleEN;
    /**
     * 標題
     */
    @Column(columnDefinition = "text")
    private String     titlePT;
    /**
     * 詳情
     */
    @Column(columnDefinition = "text")
    private String     contentSC;
    /**
     * 詳情
     */
    @Column(columnDefinition = "text")
    private String     contentTC;
    /**
     * 詳情
     */
    @Column(columnDefinition = "text")
    private String     contentEN;
    /**
     * 詳情
     */
    @Column(columnDefinition = "text")
    private String     contentPT;

    /**
     * 是否發佈（true:是\false:否)
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isDeal=false;
    /**
     * 發佈日期
     */
    private Date publicDate;


    public News(NewsVO vo) {
        BeanUtils.copyProperties(vo, this, IGNORE_PROPERTIES);

    }

    public NewsVO toVO() {
        NewsVO vo = new NewsVO();
        BeanUtils.copyProperties(this, vo, IGNORE_PROPERTIES);


        return vo;
    }
}
