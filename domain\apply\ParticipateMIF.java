package com.exhibition.domain.apply;

import com.exhibition.domain.sys.Status;
import com.exhibition.vo.apply.ParticipateMIFVO;
import com.exhibition.vo.apply.ParticipateVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.util.CollectionUtils;

import javax.persistence.CascadeType;
import javax.persistence.CollectionTable;
import javax.persistence.Column;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.apply
 * @ClassName: ParticipateThisBureau
 * @description: 參加本局展會
 * @author: ShiXin
 * @create: 2020-03-17 10:21
 **/
@Data
@NoArgsConstructor
@Entity
@Table(name = "participate_mif")
public class ParticipateMIF extends Participate<ParticipateMIF, ParticipateVO> implements Serializable {


    private static final long serialVersionUID = 810225304277862927L;

    private static final String[] IGNORE_PROPERTIES = new String[]{"applyItems"};

    /**
     * 其他參展方式
     */
    private String otherExhibitMethod;

    /**
     * 展位喜好
     */
    @Enumerated(value = EnumType.STRING)
    private ParticipateExhibitBoothPreference preference;

    /**
     * 希望參展面積
     */
    private double area;

    /**
     * 過往是否參展
     */
    private Boolean attendHistoryExhibition;

    /**
     * 过往參展年份
     */
    private String attendHistoryYear;


    /**
     * 參展產品、服務
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_exhibition_product")
    @Enumerated(EnumType.STRING)
    private List<ParticipateMIFProduct> exhibitionProducts;

    /**
     * 其他產品說明
     */
    private String otherProductSpecify;



    /**
     * 期望合作形式
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_business_matching")
    @Enumerated(EnumType.STRING)
    private List<ParticipateBusinessMatching> businessMatchings;
    /**
     * 商業配對意向說明
     */
    private String otherMatchingSpecify;

    /**
     * 主要目標市場
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_tarket_market")
    @Enumerated(EnumType.STRING)
    private List<ParticipateTargetMarket> targetMarkets;

    /**
     * 其他亞洲地區說明
     */
    private String otherAsiaAreaSpecify;

    /**
     * 其他國家地區說明
     */
    private String otherCountriesSpecify;


    /**
     * 繳費記錄
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_payment_record")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> paymentRecordFiles;


    /**
     * 組團企業
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_group")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateEnterpriseGroup> groups;


    /**
     * 上傳信函文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_letter")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> letterFiles;


    /**
     * 申請單位文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_unit")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> applicantUnitFiles;


    /**
     * 備註
     */
    private String remarks;
    /**
     * 展品
     */
    private String exhibits;
    /**
     * 展品其它
     */
    private String otherExhibits;

    /**
     * 展位楣板（中文）
     */
    private String boothlintel;
    /**
     * 展位楣板（英文）
     */
    private String boothlintelen;
    /*
     * 公司簡介英文
     */
    @Column(columnDefinition = "text")
    private String companyProfileen;
    /**
     * 主要業務範圍
     */
    @ElementCollection
    private List<String> mainBusinessscope;
    /**
     * 其它
     */
    private String otherMainBusinessscope;
    /**
     * 次要業務範圍
     */
    @ElementCollection
    private List<String> lessBusinessscope;
    /**
     * 其它
     */
    private String otherlessBusinessscope;
    /**
     * 是否電商平台
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean ePlatform;
    /**
     * 電商平台名稱
     */
    private String platformName;
    /**
     * 商業配對意向
     */
    private String  busniessIntention;
    /**
     * 有意尋找的採購商/合作夥伴類型（如超市、餐廳或貿易公司等）
     */
    private String Partner;
    /**
     * 展品類別
     */
    @ElementCollection
    private List<String>  ExhibitCategory;
    /**
     * 其它展品類別
     */
    private String otherexhibitCategory;
    //是否同意
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isagree;
    //是否聲明
    @Column(columnDefinition = "tinyint default 0")
    private Boolean isstate;

    //是否商業配對
    @Column(columnDefinition = "tinyint default 0")
    private Boolean  isbm;
    /**\
     * 公司简介
     */
    @Column(columnDefinition = "text")
    private String companyProfile;

    private String otherseeking;
    private String otheroffering;
    private String othertargetMarkets;




    /**
     * 参展的照片
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_image")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> images;

    /**
     *  参展的宣传片
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_video")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> video;
    /**
     *  参展商名单
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_exhibitor")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> exhibitor;


    /**
     *  申請表格
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_applicationform")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> applicationform;

    /**
     *  50%或以上之股東澳門身份證明文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_macaucard")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> macaucard;


    /**
     *  社會保障基金供款單副本
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_socialsecurity")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> socialsecurity;


    /**
     *  澳門製造/澳門品牌/澳門代理海外品牌商品補充文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_additional")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> additional;



    /**
     * 尋求
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_seeking")
    @Enumerated(EnumType.STRING)
    private List<ParticipateMIFPartnership> seeking;
    /**
     * 提供
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_offering")
    @Enumerated(EnumType.STRING)
    private List<ParticipateMIFPartnership> offering;

    /**
     * 參展聲明書
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_declaration_participation")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> declarationParticipation;

    /**
     * 是否同意条约
     */
    private Boolean isAgreeTreaty;

    /**
     * 澳門製造”之產品，須提供由澳門特別行政區政府經濟及科技發展局發出之產地來源證明書或工業准照副本 —>附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_macaozhizao")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> macaozhizaoFiles;

    /**
     * 澳門品牌”之產品，須提供由澳門特別行政區政府經濟及科技發展局發出之商標註冊證副本—>附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_macaobrand")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> macaobrandFiles;
    /**
     * 澳門代理海外品牌”之產品，須提供產品代理授權書副本 —>附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_macaoagent")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> macaoagentFiles;
    /**
     * 由商業及動產登記局於三個月內發出之本澳之商業登記證明/書面報告副本—>附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_shangye")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> shangyeFiles;
    /**
     * 營業稅 - 開業/更改申報表(M/1表格) 或財政局發出之開業聲明書副本—>附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_macaom1")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> macaom1Files;
    /**
     * 最近一年內發出的營業稅–徵稅憑單(M/8)副本—>附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_macaom8")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> macaom8Files;

    private String applyItemType; // item：項目，exhibit：產品
    @JsonIgnore
    @OneToMany(mappedBy = "participateMif", cascade = CascadeType.ALL)
    private List<ParticipateMifApplyItems> applyItems;

    /**
     * 选项
     */
    private String commercial;
    /**
     * 是否股东身份证明文件
     */
    @ElementCollection
    private List<String> isLetter;
    /**
     * 是否M1
     */
    @ElementCollection
    private List<String> isMacaom1;
    /**
     * 是否商业登记证三个月内
     */
    @ElementCollection
    private List<String> isShangye;

    /**
     * 是否M1
     */
    @ElementCollection
    private List<String> isMacaom8;
    /**
     * 社會保障基金
     */
    @ElementCollection
    private List<String> isFss;
    /**
     * 是否包含图片
     */
    @ElementCollection
    private List<String> isImages;

    /**
     * 財政局A
     */
    @ElementCollection
    private List<String> isDsfa;
    /**
     * 財政局B
     */
    @ElementCollection
    private List<String> isDsfb;
    /**
     * 最近一年的合同
     */
    @ElementCollection
    private List<String> isContract;
    /**
     * 符合「澳門製造」資格：商品必須為澳門生產，並提供由澳門經濟及科技發展局發出的產地來源證明書副本及工業准照(即廠牌)副本；
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_manufacturing")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> manufacturingFiles;
    /**
     * 社會保障基金
     */
    @ElementCollection
    private List<String> isManufacturingFiles;
    @ElementCollection
    private List<String> isBrandFiles;
    @ElementCollection
    private List<String> isOverseasFiles;
    @ElementCollection
    private List<String> isGbaFiles;

    /**
     * 社會保障基金文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_fssFiles")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> fssFiles;
    /**
     * 財政局A
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_dsfaFiles")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> dsfaFiles;
    /**
     * 財政局B
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_dsfbFiles")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> dsfbFiles;
    /**
     * 最近一年的合同
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_contractFiles")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> contractFiles;
    //是否同意第五项条款
    private Boolean  isAgreeFive;
    /*
    * 产品，可多选
    * */
    @ElementCollection
    private List<String> products;
    /**
     * 其他产品
     */
    private String otherProduct;
    /**
     * 参展产品/服务资料
     **/
    @ElementCollection
    @CollectionTable(name ="participate_mif_product_files")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> productFiles;

    /**
     *1.持有註冊資本百分之五十或以上的有效澳門特别行政區居民息份證明文件或法人的設立文件副本及倘有的授摧查
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_identityproof_or_legalperson")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> identityproofOrLegalperson;
    /**
     *4.若上述第3項之文件未能租示公司注冊诚本/股權分配情况或具其他特殊情况而未能提交本項所指之文件，經本局批准，可以聲明書或其他相閱道明文件作補充
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_other_registration")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> otherRegistrationFiles;
    /**
     * “橫琴粵澳深度合作區製造”之產品 —>附件
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_macaohq")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> macaohqFiles;
    /**
     * 四大產業及專業服務，證明文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_professional")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> professionalServiceFiles;
    @ElementCollection
    private List<String> isProfessionalServiceFiles;
    /**
     *  企業主的有效身份證明文件副本及倘有的授權書
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_powerattorney")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> powerAttorneyFiles;
    @ElementCollection
    private List<String> isPowerAttorne;
    /**
     * 最近一年的照片
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_phone_files")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> phoneFiles;
    /**
     * 照片
     */
    @ElementCollection
    private List<String> isPhone;
    /**
     * 其他文件
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_others")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> othersFiles;
    @ElementCollection
    private List<String> isOthersFiles;
    /**
     * 經營業務範疇
     */
    @ElementCollection
    @CollectionTable(name = "participate_mif_businessCope_files")
    @Fetch(FetchMode.SELECT)
    private List<ParticipateCert> businessCopeFiles;
    @ElementCollection
    private List<String> isBusinessCopeFiles;
    /**
     * 选项
     */
    @Column(columnDefinition = "text")
    private String natureOption;
    /**
     * 是否知悉
     */
    @Column(columnDefinition = "tinyint default 0")
    private Boolean know;

    @Enumerated(EnumType.STRING)
    private Status mifStatus;

    @ElementCollection
    private List<String> isBusinessOrPracticeFiles;
    @ElementCollection
    private List<String> isPowerAttorney;
    @ElementCollection
    private List<String> isDeclaration;
    /**
     * 50%股東證明
     */
    @ElementCollection
    private List<String> isIdentification;
    /**
     * 是否商業及動產登記局
     */
    @ElementCollection
    private List<String> isBusinessRegistration;
    /**
     * 是否其它文件
     */
    @ElementCollection
    private List<String> isOtherRegistration;

    /**
     * 場所登記
     */
    @Column(columnDefinition = "text")
    private String siteRegistrationCode;

    /**
     * 商业及动产登记局登记编号
     */
    @Column(columnDefinition = "text")
    private String registrationNumber;

    public ParticipateMIF(ParticipateMIFVO vo) {
        copyProperties(vo, this, IGNORE_PROPERTIES);
        if (!CollectionUtils.isEmpty(vo.getApplyItems())) {
            List<ParticipateMifApplyItems> collect =
                    vo.getApplyItems().stream().map(ParticipateMifApplyItems::new).peek(a -> a.setParticipateMif(this)).collect(Collectors.toList());
            this.setApplyItems(collect);
        }
    }

    @Override
    public ParticipateMIFVO toVO() {
        return toVO(true);
    }

    public ParticipateMIFVO toVO(boolean includeLazy) {
        ParticipateMIFVO vo = new ParticipateMIFVO();
        copyProperties(this, vo);
        if (includeLazy) {
            if (!CollectionUtils.isEmpty(applyItems)) {
                vo.setApplyItems(applyItems.stream().map(ParticipateMifApplyItems::toVO).collect(Collectors.toList()));
            }else{
                vo.setApplyItems(Collections.emptyList());
            }
        }
        return vo;
    }

}
