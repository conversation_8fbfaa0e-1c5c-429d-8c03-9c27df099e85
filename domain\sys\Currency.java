package com.exhibition.domain.sys;

import java.util.Arrays;

/**
 * @ProjectName: exhibition-backend
 * @Package: com.exhibition.domain.sys
 * @ClassName: Currency
 * @description: 幣種
 * @author: ShiXin
 * @create: 2020-02-25 13:46
 **/
public enum Currency {
    /**
     * 人民幣
     */
    CNY("人民幣"),
    /**
     * 美元
     */
    USD("美元"),
    /**
     * 港元
     */
    HKD("港元"),
    /**
     * 澳門元
     */
    MOP("澳門元");

    private final String name;

    Currency(String name) {
        this.name = name;
    }

    /**
     * 根据名称获取对应的枚举
     *
     * @param name
     * @return
     */
    public static Currency getByName(String name) {
        if (null == name || "".equals(name)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(t -> t.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public String getName() {
        return name;
    }

}
