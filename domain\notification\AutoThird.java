package com.exhibition.domain.notification;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.apply.Encourage;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.BatchSize;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @desc
 * @date 2023年05月18日 22:04
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "auto_third")
@BatchSize(size = 20)
@JsonView(Encourage.EncourageSimpleView.class)
public class AutoThird extends BaseEntity {
}
