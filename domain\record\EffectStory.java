//package com.exhibition.domain.record;
//
//import com.exhibition.domain.activity.Activity;
//import com.exhibition.domain.apply.Encourage;
//import com.exhibition.domain.apply.ParticipateCert;
//import com.exhibition.domain.apply.SubsidyType;
//import com.exhibition.domain.sys.Institution;
//import com.exhibition.domain.sys.InstitutionalNature;
//import com.exhibition.vo.apply.ParticipateRecordVO;
//import com.exhibition.vo.apply.ParticipateVO;
//import com.exhibition.vo.member.MemberVO;
//import com.exhibition.vo.record.BlacklistVO;
//import com.fasterxml.jackson.annotation.JsonView;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//import javax.persistence.*;
//import java.io.Serializable;
//import java.util.Date;
//import java.util.List;
//import com.exhibition.base.entity.BaseEntity;
//import org.hibernate.annotations.BatchSize;
//import org.hibernate.annotations.Fetch;
//import org.hibernate.annotations.FetchMode;
//import org.hibernate.annotations.Where;
//import org.springframework.beans.BeanUtils;
//
//
///**
// *
// * 规则表
// */
//@Data
//@NoArgsConstructor
//@Entity
//@Table(name = "blacklist")
//@BatchSize(size = 20)
//@JsonView(Encourage.EncourageSimpleView.class)
//public class Blacklist extends BaseEntity  implements Serializable {
//
//    private static final long serialVersionUID = 1L;
//
//    /**
//     * 所属机构
//     */
//    @ManyToOne(optional = false, fetch = FetchType.LAZY)
//    private Institution institution;
//    /**
//     * 所属机构
//     */
//    @ManyToOne(optional = false, fetch = FetchType.LAZY)
//    private Activity activity;
//    /**
//     * 記錄人
//     */
//    private String     recordPerson;
//    /**
//     * 記錄時間
//     */
//    @Temporal(TemporalType.TIMESTAMP)
//    private Date       recordTime;
//
//    /**
//     * 詳情
//     */
//    @Lob
//    @Basic(fetch = FetchType.LAZY)
//    @Column(name = "details", nullable = true)
//    private String details;
//    /**
//     * 清空說明
//     */
//    @Lob
//    @Basic(fetch = FetchType.LAZY)
//    @Column(name = "memo", nullable = true)
//    private String memo;
//    /**
//     * 解除違規說明
//     */
//    @Lob
//    @Basic(fetch = FetchType.LAZY)
//    @Column(name = "desc", nullable = true)
//    private String desc;
//    /**
//     * 補充日期
//     */
//    @Temporal(TemporalType.TIMESTAMP)
//    private Date       supplementTime;
//    /**
//     * 是否違規（true:是\false:否)
//     */
//    @Column(columnDefinition = "tinyint default 0")
//    private Boolean isBlack=false;
//
//    /**
//     * 其它文件
//     */
//    @ElementCollection
//    @CollectionTable(name = "blacklist_files")
//    @Fetch(FetchMode.SELECT)
//    private List<ParticipateCert> files;
//
//    public Blacklist(BlacklistVO vo) {
//        BeanUtils.copyProperties(vo, this);
//    }
//
//    public BlacklistVO toVO() {
//        BlacklistVO vo = new BlacklistVO();
//        BeanUtils.copyProperties(this, vo);
//        return vo;
//    }
//}

package com.exhibition.domain.record;

import com.exhibition.base.entity.BaseEntity;
import com.exhibition.domain.activity.Activity;
import com.exhibition.domain.apply.Encourage;
import com.exhibition.domain.apply.ParticipateCert;
import com.exhibition.domain.sys.Institution;
import com.exhibition.domain.sys.User;
import com.exhibition.util.StringUtils;
import com.exhibition.vo.record.BlacklistVO;
import com.exhibition.vo.record.EffectStoryVO;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.beans.BeanUtils;

import javax.persistence.CollectionTable;
import javax.persistence.Column;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * add by ryan 20240126
 * 成效故事
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "effect_story")
@BatchSize(size = 20)
@JsonView(Encourage.EncourageSimpleView.class)
public class EffectStory extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
        /**
     * 所属机构
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private Institution institution;
        /**
     * 所属机构
     */
    @ManyToOne( fetch = FetchType.LAZY)
    private Activity activity;
    /**
     * 記錄人
     */
    private String     recordPerson;

    /**
     * 詳情
     */
    @Column(columnDefinition = "text")
    private String     recordDetails;
    /**
     * 標題
     */
    @Column(columnDefinition = "text")
    private String     recordTitle;

    /**
     * 情況說明
     */
    @Column(columnDefinition = "text")
    private String     recordMemo;
    /**
     * 解除情況說明
     */
    @Column(columnDefinition = "text")
    private String     recordDesc;

    /**
     * 記錄時間
     */
    private Date       recordTime;//

    /**
     * 記錄人員
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private User recordUser;


    public EffectStory(EffectStoryVO vo) {
        BeanUtils.copyProperties(vo, this);
        if (null != vo.getActivityId()) {
            Activity activity = new Activity();
            activity.setId(vo.getActivityId());
            this.setActivity(activity);
        }
        if (null != vo.getInstitutionId()) {
            Institution institution=new Institution();
            institution.setId(vo.getInstitutionId());
            this.setInstitution(institution);
        }

        if (null != vo.getRecordUserId()) {
            User applicant = new User();
            applicant.setId(vo.getRecordUserId());
            this.setRecordUser(applicant);
        }
    }

    public EffectStoryVO toVO() {
        EffectStoryVO vo = new EffectStoryVO();
        BeanUtils.copyProperties(this, vo);
        if (null != vo.getActivityId()) {
            Activity activity = new Activity();
            activity.setId(vo.getActivityId());
            this.setActivity(activity);
        }
        if (null != vo.getInstitutionId()) {
            Institution institution=new Institution();
            institution.setId(vo.getInstitutionId());
            this.setInstitution(institution);
        }
        Activity activity = this.getActivity();
        if (null != activity) {
            vo.setActivityId(activity.getId());
            vo.setActivityName(activity.getNameZh());
            vo.setActivity(activity.toSimpleVO());
        }
        // 机构
        Institution institution = this.getInstitution();
        if (null != institution) {
            vo.setInstitutionId(institution.getId());
            vo.setInstutionName(institution.getNameZh());
            vo.setInstitution(institution.toSimpleVO());
        }

        //发起人
        User applicant = this.getRecordUser();
        if (null != applicant) {
            vo.setRecordUserId(applicant.getId());

            vo.setRecordUserName((StringUtils.isNotEmpty(applicant.getName())?applicant.getName():"") +(StringUtils.isNotEmpty(applicant.getLastName())?applicant.getLastName():"null"));
            vo.setRecordUserAccount(applicant.getAccount());
        }
        return vo;
    }
}
